<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tduck</groupId>
    <artifactId>tduck-platform</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>tduck-platform</name>
    <description>在线问卷</description>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.8</spring-boot.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <mybatis.plus.version>3.5.3</mybatis.plus.version>
        <guava.version>30.1.1-jre</guava.version>
        <commons.lang3.vesrion>3.12.0</commons.lang3.vesrion>
        <commons.io.version>2.9.0</commons.io.version>
        <commons.configuration.version>1.10</commons.configuration.version>
        <jwt.version>0.9.1</jwt.version>
        <hutool.version>5.8.16</hutool.version>
        <aliyun-oss.sdk.vesion>3.12.0</aliyun-oss.sdk.vesion>
        <qiniu-oss.version>7.11.0</qiniu-oss.version>
        <upyun.version>4.2.2</upyun.version>
        <aws-java-sdk-s3.version>1.12.400</aws-java-sdk-s3.version>
        <velocity.version>1.7</velocity.version>
        <wx-java-mp.version>3.9.0</wx-java-mp.version>
        <springdoc-openapi-ui.version>1.6.9</springdoc-openapi-ui.version>
        <jwt.version>0.7.0</jwt.version>
        <weixin-java.version>4.1.0</weixin-java.version>
        <captcha.version>1.2.8</captcha.version>
        <fastjson.version>2.0.26</fastjson.version>
        <tencentcloud.version>4.0.11</tencentcloud.version>
        <poi.version>5.1.0</poi.version>
        <okhttp3.version>4.11.0</okhttp3.version>
        <okio.version>3.5.0</okio.version>

    </properties>

    <modules>
        <module>tduck-common</module>
        <module>tduck-storage</module>
        <module>tduck-api</module>
        <module>tduck-account</module>
        <module>tduck-form</module>
        <module>tduck-wx-mp</module>
        <module>tduck-webhook</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <!--  mybatis plus https://mp.baomidou.com/guide/install.html#release-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <!--guava-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.vesrion}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>${commons.configuration.version}</version>
            </dependency>
            <!--   https://hutool.cn/docs/#/-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--   swagger-->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc-openapi-ui.version}</version>
            </dependency>
            <!--oss客户端-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.sdk.vesion}</version>
            </dependency>
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu-oss.version}</version>
            </dependency>
            <!--代码生成模板引擎-->
            <dependency>
                <artifactId>velocity</artifactId>
                <groupId>org.apache.velocity</groupId>
                <version>${velocity.version}</version>
            </dependency>
            <!--  微信依赖-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <!--  滑动验证码-->
            <dependency>
                <groupId>com.anji-plus</groupId>
                <artifactId>spring-boot-starter-captcha</artifactId>
                <version>${captcha.version}</version>
            </dependency>
            <!--滑动验证码 需要fastjson  -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencentcloud.version}</version><!-- 注：这里只是示例版本号，请获取并替换为 最新的版本号 -->
            </dependency>
            <!--   excel-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- POI excel-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!--又拍云-->
            <dependency>
                <groupId>com.upyun</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${upyun.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
