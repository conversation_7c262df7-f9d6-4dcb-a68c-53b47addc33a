# YAugment 6-Digit Verification Code Generator
# Based on IDA Pro analysis findings - Updated for 6-character codes

Write-Host "=== YAugment 6-Digit Verification Code Generator ===" -ForegroundColor Green
Write-Host "Analysis Date: $(Get-Date)" -ForegroundColor Yellow
Write-Host "Current UTC Time: $([DateTimeOffset]::UtcNow)" -ForegroundColor Yellow
Write-Host "Current Unix Timestamp: $([DateTimeOffset]::UtcNow.ToUnixTimeSeconds())" -ForegroundColor Yellow

# Key from IDA Pro analysis
$base64Key = "F8T8d3XQy6j9wL4qA7gC2rX7pV5kM9nH0zK1lC3bE4h="
$key = [System.Convert]::FromBase64String($base64Key)
$salt = [System.Text.Encoding]::UTF8.GetBytes("YAugment_QQ_Verification_Salt")

# Character sets for 6-digit codes
$numbers = "0123456789"
$letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
$mixedChars = $numbers + $letters

# Algorithm 1: 24-hour time window - 6 digits mixed
function Generate-Code24Hour {
    $currentTimestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
    $timeSlot = [Math]::Floor($currentTimestamp / 86400) * 86400

    $message = [System.Text.Encoding]::UTF8.GetBytes("YAugment_24h_" + $timeSlot.ToString())
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = $key
    $hash = $hmac.ComputeHash($message)

    $code = ""
    for ($i = 0; $i -lt 6; $i++) {
        $code += $mixedChars[$hash[$i] % $mixedChars.Length]
    }

    return @{
        Code = $code
        TimeSlot = $timeSlot
        Algorithm = "24-hour window (mixed)"
    }
}

# Algorithm 1b: 24-hour time window - 6 digits only
function Generate-Code24HourDigits {
    $currentTimestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
    $timeSlot = [Math]::Floor($currentTimestamp / 86400) * 86400

    $message = [System.Text.Encoding]::UTF8.GetBytes("YAugment_24h_" + $timeSlot.ToString())
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = $key
    $hash = $hmac.ComputeHash($message)

    $code = ""
    for ($i = 0; $i -lt 6; $i++) {
        $code += ($hash[$i] % 10).ToString()
    }

    return @{
        Code = $code
        TimeSlot = $timeSlot
        Algorithm = "24-hour window (digits only)"
    }
}

# Algorithm 2: Current timestamp + 24 hours - mixed
function Generate-CodeTimestampPlus24h {
    $currentTimestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
    $timestamp24h = $currentTimestamp + 86400

    $message = [System.Text.Encoding]::UTF8.GetBytes("YAugment_ts24_" + $timestamp24h.ToString())
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = $key
    $hash = $hmac.ComputeHash($message)

    $code = ""
    for ($i = 0; $i -lt 6; $i++) {
        $code += $mixedChars[$hash[$i] % $mixedChars.Length]
    }

    return @{
        Code = $code
        Timestamp = $timestamp24h
        Algorithm = "Current timestamp + 24h (mixed)"
    }
}

# Algorithm 2b: Date-based simple format (YYMMDD)
function Generate-CodeDateSimple {
    $dateStr = [DateTimeOffset]::UtcNow.ToString("yyMMdd")
    return @{
        Code = $dateStr
        Date = $dateStr
        Algorithm = "Date simple (YYMMDD)"
    }
}

# Algorithm 3: Date-based (YYYYMMDD)
function Generate-CodeDateBased {
    $dateStr = [DateTimeOffset]::UtcNow.ToString("yyyyMMdd")
    
    $message = [System.Text.Encoding]::UTF8.GetBytes("YAugment_date_" + $dateStr)
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = $key
    $hash = $hmac.ComputeHash($message)
    
    $code = ""
    for ($i = 0; $i -lt 6; $i++) {
        $code += ($hash[$i] % 10).ToString()
    }
    
    return @{
        Code = $code
        Date = $dateStr
        Algorithm = "Date-based (YYYYMMDD)"
    }
}

# Algorithm 4: Hour-based time windows
function Generate-CodeHourBased {
    $currentTimestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
    $timeSlot = [Math]::Floor($currentTimestamp / 3600) * 3600
    
    $message = [System.Text.Encoding]::UTF8.GetBytes("YAugment_hour_" + $timeSlot.ToString())
    $hmac = New-Object System.Security.Cryptography.HMACSHA256
    $hmac.Key = $key
    $hash = $hmac.ComputeHash($message)
    
    $code = ""
    for ($i = 0; $i -lt 6; $i++) {
        $code += ($hash[$i] % 10).ToString()
    }
    
    return @{
        Code = $code
        TimeSlot = $timeSlot
        Algorithm = "Hour-based window"
    }
}

# Algorithm 5: PBKDF2 with 24-hour window
function Generate-CodePBKDF2_24h {
    $currentTimestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
    $timeSlot = [Math]::Floor($currentTimestamp / 86400) * 86400
    
    $password = "YAugment_PBKDF2_" + $timeSlot.ToString()
    $pbkdf2 = New-Object System.Security.Cryptography.Rfc2898DeriveBytes($password, $salt, 99999)
    $hash = $pbkdf2.GetBytes(32)
    
    $code = ""
    for ($i = 0; $i -lt 6; $i++) {
        $code += ($hash[$i] % 10).ToString()
    }
    
    return @{
        Code = $code
        TimeSlot = $timeSlot
        Algorithm = "PBKDF2 with 24h window"
    }
}

# Algorithm 6: Multiple time windows
function Generate-CodeMultipleWindows {
    $results = @()
    $windows = @(300, 600, 1800, 3600, 7200, 21600, 43200, 86400)  # 5min to 24h
    
    foreach ($window in $windows) {
        $currentTimestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
        $timeSlot = [Math]::Floor($currentTimestamp / $window) * $window
        
        $message = [System.Text.Encoding]::UTF8.GetBytes("YAugment_" + $window.ToString() + "_" + $timeSlot.ToString())
        $hmac = New-Object System.Security.Cryptography.HMACSHA256
        $hmac.Key = $key
        $hash = $hmac.ComputeHash($message)
        
        $code = ""
        for ($i = 0; $i -lt 6; $i++) {
            $code += ($hash[$i] % 10).ToString()
        }
        
        $windowDesc = switch ($window) {
            300 { "5 minutes" }
            600 { "10 minutes" }
            1800 { "30 minutes" }
            3600 { "1 hour" }
            7200 { "2 hours" }
            21600 { "6 hours" }
            43200 { "12 hours" }
            86400 { "24 hours" }
        }
        
        $results += @{
            Code = $code
            Window = $windowDesc
            Seconds = $window
        }
    }
    
    return $results
}

# Algorithm for common 6-digit patterns
function Generate-CommonPatterns {
    $currentDate = Get-Date
    $patterns = @()

    # Date-based patterns
    $patterns += @{
        Code = $currentDate.ToString("yyMMdd")
        Algorithm = "Date YYMMDD"
    }

    $patterns += @{
        Code = $currentDate.ToString("ddMMyy")
        Algorithm = "Date DDMMYY"
    }

    $patterns += @{
        Code = $currentDate.ToString("MMddyy")
        Algorithm = "Date MMDDYY"
    }

    # Time-based patterns
    $patterns += @{
        Code = $currentDate.ToString("HHmmss")
        Algorithm = "Time HHMMSS"
    }

    # Mixed patterns
    $patterns += @{
        Code = "YA" + $currentDate.ToString("MMdd")
        Algorithm = "YA + MMDD"
    }

    $patterns += @{
        Code = "YA" + $currentDate.ToString("yy") + $currentDate.ToString("MM")
        Algorithm = "YA + YYMM"
    }

    return $patterns
}

# Generate all codes
Write-Host "`n=== GENERATING ALL POSSIBLE 6-DIGIT VERIFICATION CODES ===" -ForegroundColor Cyan

$code1 = Generate-Code24Hour
$code1b = Generate-Code24HourDigits
$code2 = Generate-CodeTimestampPlus24h
$code2b = Generate-CodeDateSimple
$code3 = Generate-CodeDateBased
$code4 = Generate-CodeHourBased
$code5 = Generate-CodePBKDF2_24h
$multiCodes = Generate-CodeMultipleWindows
$commonPatterns = Generate-CommonPatterns

Write-Host "`n1. 24-Hour Time Window (Mixed):" -ForegroundColor Yellow
Write-Host "   Code: $($code1.Code)" -ForegroundColor Green

Write-Host "`n2. 24-Hour Time Window (Digits Only):" -ForegroundColor Yellow
Write-Host "   Code: $($code1b.Code)" -ForegroundColor Green

Write-Host "`n3. Current Timestamp + 24 Hours (Mixed):" -ForegroundColor Yellow
Write-Host "   Code: $($code2.Code)" -ForegroundColor Green

Write-Host "`n4. Date Simple (YYMMDD):" -ForegroundColor Yellow
Write-Host "   Code: $($code2b.Code)" -ForegroundColor Green

Write-Host "`n5. Date-Based Algorithm:" -ForegroundColor Yellow
Write-Host "   Code: $($code3.Code)" -ForegroundColor Green

Write-Host "`n6. Hour-Based Window Algorithm:" -ForegroundColor Yellow
Write-Host "   Code: $($code4.Code)" -ForegroundColor Green

Write-Host "`n7. PBKDF2 with 24-Hour Window Algorithm:" -ForegroundColor Yellow
Write-Host "   Code: $($code5.Code)" -ForegroundColor Green

Write-Host "`n8. Common Date/Time Patterns:" -ForegroundColor Yellow
foreach ($pattern in $commonPatterns) {
    Write-Host "   $($pattern.Algorithm): $($pattern.Code)" -ForegroundColor Green
}

Write-Host "`n9. Multiple Time Windows:" -ForegroundColor Yellow
foreach ($result in $multiCodes) {
    Write-Host "   $($result.Window): $($result.Code)" -ForegroundColor Green
}

# Summary of all codes
Write-Host "`n=== SUMMARY: ALL 6-DIGIT VERIFICATION CODES TO TRY ===" -ForegroundColor Red
Write-Host "Priority order (most likely first):" -ForegroundColor White

$allCodes = @()
$allCodes += $code2b.Code  # Date simple
$allCodes += $commonPatterns | ForEach-Object { $_.Code }
$allCodes += $code1b.Code  # 24h digits only
$allCodes += $code1.Code   # 24h mixed
$allCodes += $code2.Code   # timestamp + 24h
$allCodes += $code3.Code   # date-based
$allCodes += $code4.Code   # hour-based
$allCodes += $code5.Code   # PBKDF2
$allCodes += $multiCodes | ForEach-Object { $_.Code }

# Filter to 6 characters only and remove duplicates
$sixDigitCodes = $allCodes | Where-Object { $_.Length -eq 6 } | Sort-Object -Unique

Write-Host "`n🎯 TOP PRIORITY 6-DIGIT CODES:" -ForegroundColor Red
$priority = 1
foreach ($code in $sixDigitCodes) {
    Write-Host "$priority. $code" -ForegroundColor Cyan
    $priority++
    if ($priority -gt 20) { break }  # Limit to top 20
}

Write-Host "`nTotal unique 6-digit codes generated: $($sixDigitCodes.Count)" -ForegroundColor Yellow
Write-Host "Try these codes in the YAugment verification interface!" -ForegroundColor Green

# Special focus on today's date patterns
Write-Host "`n🔥 TODAY'S DATE PATTERNS (HIGHEST PRIORITY):" -ForegroundColor Red
$today = Get-Date
Write-Host "1. $($today.ToString('yyMMdd')) (YYMMDD)" -ForegroundColor Yellow
Write-Host "2. $($today.ToString('ddMMyy')) (DDMMYY)" -ForegroundColor Yellow
Write-Host "3. $($today.ToString('MMddyy')) (MMDDYY)" -ForegroundColor Yellow
Write-Host "4. YA$($today.ToString('MMdd')) (YA+MMDD)" -ForegroundColor Yellow
