# 前端应用 Dockerfile
FROM node:16-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY tduck-front/package*.json ./

# 安装依赖
RUN npm install --registry=https://registry.npmmirror.com

# 复制源代码
COPY tduck-front/ ./

# 修改生产环境配置
RUN echo "VUE_APP_TITLE = 在线问卷系统" > .env.production && \
    echo "VUE_APP_API_ROOT = /api" >> .env.production && \
    echo "VUE_APP_DEBUG_TOOL =" >> .env.production && \
    echo "VUE_APP_MAP_KEY = f2200337d0d08538e78729572749882d" >> .env.production && \
    echo "VUE_APP_WX = OFF" >> .env.production

# 构建生产版本
RUN npm run build

# 生产镜像
FROM nginx:alpine

# 设置维护者信息
LABEL maintainer="wenjuan-system"

# 安装必要工具
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY config-files/frontend/nginx.conf /etc/nginx/nginx.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx && \
    touch /var/log/nginx/access.log && \
    touch /var/log/nginx/error.log

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/log/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:80 || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
