<!--<template>-->
<!--    <div>-->
<!--        <sign-pad />-->
<!--        <pagination />-->
<!--        <province-city />-->
<!--        <input-map />-->
<!--        <phone-verification />-->
<!--        <el-button @click="test">测试</el-button>-->
<!--        &lt;!&ndash;        <handle-process />&ndash;&gt;-->
<!--    </div>-->
<!--</template>-->

<!--<script>-->
<!--import SignPad from '../components/form/SignPad'-->
<!--import pagination from '../components/form/pagination'-->
<!--import ProvinceCity from '@/components/form/ProvinceCity'-->

<!--import InputMap from '@/components/form/InputMap/index'-->
<!--import PhoneVerification from '@/components/form/PhoneVerification/index'-->
<!--// import HandleProcess from '@/components/HandleProcess'-->

<!--export default {-->
<!--    name: 'App',-->
<!--    components: {-->
<!--        // HandleProcess,-->
<!--        ProvinceCity,-->
<!--        PhoneVerification,-->
<!--        SignPad,-->
<!--        pagination,-->
<!--        InputMap-->
<!--    },-->
<!--    data() {-->
<!--        return {}-->
<!--    },-->
<!--    mounted() {-->
<!--    },-->
<!--    methods: {-->
<!--        test() {-->
<!--            this.$process({-->
<!--                message: '处理中'-->
<!--            })-->
<!--        }-->
<!--    }-->
<!--}-->
<!--</script>-->
