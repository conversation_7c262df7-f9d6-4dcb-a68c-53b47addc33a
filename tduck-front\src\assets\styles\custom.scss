/* 头像上传组件全局样式 */
.avatar-modal {
  .avatar-uploader {
    z-index: 10001 !important;
  }

  /* 基础样式重置 */
  .vicp-wrap {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    max-height: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    background-color: transparent !important;
    z-index: auto !important;
  }

  /* 第一步：上传区域 */
  .vicp-step1 .vicp-drop-area {
    min-height: 180px !important;
    background-color: #f5f7fa !important;
    border: 2px dashed #d9e1ec !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;

    &:hover {
      background-color: #ebf5ff !important;
      border-color: #409eff !important;
    }

    .vicp-icon1 {
      color: #409eff !important;
      font-size: 64px !important;
    }

    .vicp-hint {
      color: #606266 !important;
      font-size: 14px !important;
      margin-top: 15px !important;
    }

    .vicp-no-supported-hint {
      color: #f56c6c !important;
      font-size: 14px !important;
    }

    .vicp-loading {
      color: #409eff !important;
      font-size: 15px !important;
    }
  }

  /* 第二步：裁剪区域 */
  .vicp-step2 {
    .vicp-crop {
      display: flex !important;
      flex-direction: row !important;
      margin: 0 !important;
      padding: 0 !important;
      border-radius: 8px !important;
      overflow: hidden !important;

      @media (max-width: 768px) {
        flex-direction: column !important;
      }

      .vicp-crop-left {
        flex: 1 !important;
        background-color: #f5f7fa !important;
        padding: 15px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        img {
          max-width: 100% !important;
          max-height: 300px !important;
        }
      }

      .vicp-crop-right {
        width: 180px !important;
        background-color: #f9fafc !important;
        padding: 15px !important;
        border-left: 1px solid #ebeef5 !important;

        @media (max-width: 768px) {
          width: 100% !important;
          border-left: none !important;
          border-top: 1px solid #ebeef5 !important;
        }

        .vicp-preview {
          display: flex !important;
          flex-wrap: wrap !important;
          justify-content: center !important;
          margin-bottom: 15px !important;

          .vicp-preview-item {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            border-radius: 4px !important;
            overflow: hidden !important;
            border: 2px solid #fff !important;
            margin: 5px !important;
            transition: all 0.3s ease !important;

            &:hover {
              transform: scale(1.05) !important;
              box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
              border-color: #409eff !important;
            }
          }
        }

        .vicp-range-wrap {
          margin: 15px 0 !important;
          padding: 0 !important;

          .vicp-range-title {
            font-size: 13px !important;
            color: #606266 !important;
            margin-bottom: 5px !important;
          }

          .vicp-range {
            margin: 8px 0 !important;

            input[type=range] {
              -webkit-appearance: none !important;
              width: 100% !important;
              height: 6px !important;
              background-color: #e4e7ed !important;
              border-radius: 3px !important;
              cursor: pointer !important;
              margin: 0 !important;

              &:focus {
                outline: none !important;
              }

              &::-webkit-slider-thumb {
                -webkit-appearance: none !important;
                width: 16px !important;
                height: 16px !important;
                background-color: #409eff !important;
                border-radius: 50% !important;
                cursor: pointer !important;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
                border: 2px solid #fff !important;
              }

              &::-moz-range-thumb {
                width: 16px !important;
                height: 16px !important;
                background-color: #409eff !important;
                border-radius: 50% !important;
                cursor: pointer !important;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
                border: 2px solid #fff !important;
              }
            }
          }
        }
      }
    }

    /* 操作按钮区域 */
    .vicp-operate {
      display: flex !important;
      justify-content: flex-end !important;
      padding: 10px 0 !important;
      margin-top: 10px !important;

      a {
        display: inline-block !important;
        padding: 8px 14px !important;
        margin-left: 10px !important;
        font-size: 13px !important;
        font-weight: 500 !important;
        border-radius: 4px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;

        &.vicp-operate-btn {
          background-color: #f5f7fa !important;
          color: #606266 !important;

          &:hover {
            background-color: #e9ecf2 !important;
          }
        }

        &:last-child {
          background-color: #409eff !important;
          color: white !important;

          &:hover {
            background-color: #66b1ff !important;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4) !important;
          }
        }
      }
    }
  }

  /* 隐藏原关闭按钮 */
  .vicp-close {
    display: none !important;
  }
}



/* Dialog styles improvement */
.t-dialog {
  overflow: visible;
  top: 5%;

  .el-dialog {
    min-height: 180px;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    border-radius: 10px;
    max-height: calc(100% - 100px);
    max-width: calc(100% - 100px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

    .el-dialog__header {
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;

      .el-dialog__title {
        font-weight: 600;
        font-size: 16px;
      }

      .el-dialog__headerbtn {
        top: 20px;
      }
    }

    .el-dialog__body {
      padding: 24px;
      flex: 1;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 10px 24px 15px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

/* Form styles improvement */
.el-form {
  .el-form-item {
    margin-bottom: 22px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #303133;
    }

    .el-form-item__content {
      line-height: 40px;
    }

    .el-input__inner {
      height: 40px;
      line-height: 40px;
      border-radius: 4px;
    }

    .el-textarea__inner {
      border-radius: 4px;
      padding: 8px 12px;
    }

    .el-select {
      width: 100%;
    }
  }
}

/* Upload component improvement */
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409EFF;
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
    object-fit: cover;
  }
}

/* Button styles improvement */
.el-button {
  &.el-button--primary {
    background-color: #1890ff;
    border-color: #1890ff;

    &:hover,
    &:focus {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }

    &:active {
      background-color: #096dd9;
      border-color: #096dd9;
    }
  }
}

.button-group {
  display: flex;

  .el-button+.el-button {
    margin-left: 10px;
  }
}

/* Card styles improvement */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;

  .el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    color: #303133;
  }

  .el-card__body {
    padding: 20px;
  }
}

/* QR code container styles */
.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;

  .el-button {
    margin-top: 15px;
  }
}

/* URL operations container */
.url-operate-container {
  margin: 15px 0;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;

  .el-input {
    .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  .button-group {
    .el-button {
      border-radius: 0;
      margin-right: 0;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }
}

/* Animation for dialog */
.dialog-fade-enter-active,
.dialog-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.dialog-fade-enter,
.dialog-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}