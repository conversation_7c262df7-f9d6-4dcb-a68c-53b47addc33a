#!/bin/bash

# TDuck 在线问卷系统 Docker 部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|status|logs|clean]

set -e

# 配置变量
PROJECT_NAME="wenjuan"
COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="./backups"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    ports=(13306 18999 18888 18080)
    for port in "${ports[@]}"; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            log_warn "端口 $port 已被占用"
            netstat -tlnp | grep ":$port "
        else
            log_debug "端口 $port 可用"
        fi
    done
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    directories=(
        "$BACKUP_DIR"
        "./logs"
        "./data/mysql"
        "./config"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
    fi
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="$BACKUP_DIR/mysql_backup_$timestamp.sql"
    
    if docker ps | grep -q "wenjuan-mysql"; then
        log_info "备份MySQL数据库..."
        docker exec wenjuan-mysql mysqldump -u wenjuan -pwenjuan2024 wenjuan > "$backup_file"
        log_info "数据库备份完成: $backup_file"
    else
        log_warn "MySQL容器未运行，跳过数据备份"
    fi
}

# 启动服务
start_services() {
    log_info "启动 $PROJECT_NAME 服务..."
    
    check_requirements
    create_directories
    check_ports
    
    log_info "构建并启动容器..."
    docker-compose -f "$COMPOSE_FILE" up -d --build
    
    log_info "等待服务启动..."
    sleep 30
    
    show_status
}

# 停止服务
stop_services() {
    log_info "停止 $PROJECT_NAME 服务..."
    
    backup_data
    
    log_info "停止容器..."
    docker-compose -f "$COMPOSE_FILE" down
    
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 $PROJECT_NAME 服务..."
    
    stop_services
    sleep 5
    start_services
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    echo "----------------------------------------"
    docker-compose -f "$COMPOSE_FILE" ps
    echo "----------------------------------------"
    
    log_info "端口监听状态:"
    netstat -tlnp 2>/dev/null | grep -E "(13306|18999|18888|18080)" || log_warn "未找到监听端口"
    
    log_info "容器健康状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep wenjuan || log_warn "未找到运行中的容器"
}

# 查看日志
show_logs() {
    service=${2:-""}
    
    if [ -z "$service" ]; then
        log_info "显示所有服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs -f --tail=100
    else
        log_info "显示 $service 服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs -f --tail=100 "$service"
    fi
}

# 清理资源
clean_resources() {
    log_warn "这将删除所有容器、镜像和数据卷，是否继续? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "备份数据..."
        backup_data
        
        log_info "停止并删除容器..."
        docker-compose -f "$COMPOSE_FILE" down -v --rmi all
        
        log_info "清理未使用的资源..."
        docker system prune -f
        
        log_info "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    services=("wenjuan-mysql" "wenjuan-backend" "wenjuan-frontend" "wenjuan-nginx")
    
    for service in "${services[@]}"; do
        if docker ps | grep -q "$service"; then
            health=$(docker inspect --format='{{.State.Health.Status}}' "$service" 2>/dev/null || echo "no-health-check")
            if [ "$health" = "healthy" ]; then
                log_info "$service: 健康"
            elif [ "$health" = "no-health-check" ]; then
                log_warn "$service: 无健康检查"
            else
                log_error "$service: 不健康 ($health)"
            fi
        else
            log_error "$service: 未运行"
        fi
    done
    
    # 测试连接
    log_info "测试服务连接..."
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:18888 | grep -q "200"; then
        log_info "前端服务: 可访问"
    else
        log_error "前端服务: 不可访问"
    fi
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:18999/actuator/health | grep -q "200"; then
        log_info "后端服务: 可访问"
    else
        log_error "后端服务: 不可访问"
    fi
}

# 显示帮助信息
show_help() {
    echo "TDuck 在线问卷系统 Docker 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    显示服务状态"
    echo "  logs      查看日志 (可指定服务名)"
    echo "  health    执行健康检查"
    echo "  backup    备份数据"
    echo "  clean     清理所有资源"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                 # 启动所有服务"
    echo "  $0 logs wenjuan-backend  # 查看后端服务日志"
    echo "  $0 status                # 显示服务状态"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$@"
            ;;
        health)
            health_check
            ;;
        backup)
            backup_data
            ;;
        clean)
            clean_resources
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
