#!/bin/sh

# 后端应用启动脚本

echo "=========================================="
echo "启动 TDuck 在线问卷系统后端服务"
echo "时间: $(date)"
echo "=========================================="

# 等待MySQL服务就绪
echo "等待MySQL数据库服务启动..."
while ! wget --quiet --tries=1 --spider http://wenjuan-mysql:3306 2>/dev/null; do
    echo "MySQL服务未就绪，等待5秒后重试..."
    sleep 5
done
echo "MySQL数据库服务已就绪"

# 检查配置文件
if [ -f "/app/config/application-prod.yml" ]; then
    echo "发现生产环境配置文件"
    CONFIG_ARGS="--spring.config.location=classpath:/application.yml,/app/config/application-prod.yml"
else
    echo "使用默认配置文件"
    CONFIG_ARGS=""
fi

# 设置JVM参数
if [ -z "$JAVA_OPTS" ]; then
    JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"
fi

echo "JVM参数: $JAVA_OPTS"
echo "Spring配置: $CONFIG_ARGS"
echo "启动应用..."

# 启动应用
exec java $JAVA_OPTS \
    -Djava.security.egd=file:/dev/./urandom \
    -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE:-prod} \
    -jar /app/app.jar \
    $CONFIG_ARGS
