{"name": "tduck-front", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build-dev": "vue-cli-service build --mode development --dest dist-dev", "preview": "node build/index.js --preview", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "svgo": "svgo -f src/assets/icons", "report": "vue-cli-service build --report"}, "dependencies": {"axios": "^0.21.0", "core-js": "^3.6.4", "crypto-js": "^4.0.0", "dayjs": "^1.9.4", "echarts": "^5.0.0", "element-ui": "2.15.14", "exceljs": "^4.3.0", "file-saver": "^2.0.2", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "nprogress": "^0.2.0", "resize-detector": "^0.3.0", "signature_pad": "^3.0.0-beta.4", "tduck-form-generator": "1.9.1", "ua-parser-js": "^0.7.23", "vant": "^2.12.25", "vue": "^2.7.0", "vue-clipboard2": "^0.3.1", "vue-image-crop-upload": "^2.5.0", "vue-meta": "^2.4.0", "vue-qr": "^2.3.0", "vue-router": "^3.4.8", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "vxe-table": "3.4.6", "vxe-table-plugin-export-xlsx": "2.2.1", "xe-utils": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.18", "@vue/cli-plugin-eslint": "~4.5.18", "@vue/cli-service": "^4.5.13", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-import": "1.13.6", "eslint": "6.8.0", "eslint-plugin-vue": "^9.17.0", "husky": "^1.3.1", "lint-staged": "8.1.5", "prettier": "2.8.1", "sass": "^1.34.0", "sass-loader": "^10.1.1", "sass-resources-loader": "^2.1.1", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "^5.0.0", "svgo": "^1.3.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}