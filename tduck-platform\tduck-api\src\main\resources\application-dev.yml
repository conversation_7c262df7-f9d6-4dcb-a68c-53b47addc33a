spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 1234
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-test-query: SELECT 1 FROM DUAL
  mail:
    host: smtp.88.com
    username: # 邮箱账号
    password: #邮箱授权码

logging:
  level:
    com.tduck.cloud: debug
  config: classpath:logback-spring.xml

wx:
  mp:
    configs:
      - appId: #公众号appId
        secret: #公众号秘钥
        token: dHClTzEAcayUX0Jyy9TwBp89QrdWvvZZ
        aesKey: 3aNsbtc8wdx92M14YszvQSU4zksx9M1Gl8k655vYOLB
