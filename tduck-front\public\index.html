<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <style>
        /* 头像上传组件样式 */
        .vue-image-crop-upload,
        .vicp-wrap {
            z-index: 10001 !important;
        }
    </style>
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
        <!-- 使用CDN的CSS文件 -->
        <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="external nofollow preload" as="style">
        <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="external nofollow stylesheet">
    <% } %>
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
        <!-- 使用CDN的JS文件 -->
        <link href="<%= htmlWebpackPlugin.options.cdn.js[i] %>" rel="external nofollow preload" as="script">
    <% } %>
    <!--百度访问统计-->
    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?4dbdbc5421c41984499f878628d60f2f";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
  <body>
    <noscript>
        <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected 移动端调试工具-->
    <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
        <script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
    <% if (htmlWebpackPlugin.options.debugTool == 'eruda') { %>
        <script src="https://cdn.jsdelivr.net/npm/eruda/eruda.min.js"></script>
        <script>eruda.init();</script>
    <% } %>
    <% if (htmlWebpackPlugin.options.debugTool == 'vconsole') { %>
        <script src="https://cdn.jsdelivr.net/npm/vconsole/dist/vconsole.min.js"></script>
        <script>new VConsole();</script>
    <% } %>
 </body>
</html>

