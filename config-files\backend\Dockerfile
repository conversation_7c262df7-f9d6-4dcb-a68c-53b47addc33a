# 后端应用 Dockerfile
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="wenjuan-system"

# 安装必要工具
RUN apk add --no-cache \
    tzdata \
    wget \
    curl \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 app && \
    adduser -D -s /bin/sh -u 1000 -G app app

# 设置工作目录
WORKDIR /app

# 创建必要目录
RUN mkdir -p /app/config /app/logs /app/images && \
    chown -R app:app /app

# 复制jar文件
COPY tduck-api.jar /app/app.jar

# 复制启动脚本
COPY docker-configs/backend/start.sh /app/start.sh
RUN chmod +x /app/start.sh && chown app:app /app/start.sh

# 切换到应用用户
USER app

# 设置环境变量
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"
ENV SPRING_PROFILES_ACTIVE=prod
ENV SERVER_PORT=8999

# 暴露端口
EXPOSE 8999

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=5 \
  CMD wget --quiet --tries=1 --spider http://localhost:8999/actuator/health || exit 1

# 启动命令
ENTRYPOINT ["/app/start.sh"]
