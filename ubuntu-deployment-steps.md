# Ubuntu 服务器 Docker 部署详细步骤

## 第一步：连接服务器并准备环境

### 1.1 SSH连接服务器
```bash
# 使用SSH连接到Ubuntu服务器
ssh username@your-server-ip

# 如果使用密钥文件
ssh -i /path/to/your-key.pem username@your-server-ip
```

### 1.2 更新系统
```bash
# 更新包列表
sudo apt update

# 升级系统包
sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget unzip tar htop net-tools
```

## 第二步：安装Docker环境

### 2.1 安装Docker
```bash
# 下载Docker安装脚本
curl -fsSL https://get.docker.com -o get-docker.sh

# 执行安装脚本
sudo sh get-docker.sh

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使组权限生效
newgrp docker

# 验证Docker安装
docker --version
docker run hello-world
```

### 2.2 安装Docker Compose
```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 2.3 启动Docker服务
```bash
# 启动Docker服务
sudo systemctl start docker

# 设置开机自启
sudo systemctl enable docker

# 检查服务状态
sudo systemctl status docker
```

## 第三步：上传和解压项目文件

### 3.1 创建项目目录
```bash
# 创建项目根目录
mkdir -p /home/<USER>/projects
cd /home/<USER>/projects

# 查看当前目录
pwd
ls -la
```

### 3.2 上传部署包
```bash
# 方法1：使用scp从本地上传（在本地执行）
scp wenjuan-deploy.tar.gz username@your-server-ip:/home/<USER>/projects/

# 方法2：使用wget下载（如果文件在网络上）
wget http://your-domain.com/wenjuan-deploy.tar.gz

# 方法3：使用rsync同步（在本地执行）
rsync -avz wenjuan-deploy.tar.gz username@your-server-ip:/home/<USER>/projects/
```

### 3.3 解压项目文件
```bash
# 解压部署包
tar -xzf wenjuan-deploy.tar.gz

# 查看解压结果
ls -la wenjuan-deploy/

# 进入项目目录
cd wenjuan-deploy

# 查看项目结构
tree . || find . -type f | head -20
```

## 第四步：准备配置文件

### 4.1 复制配置文件到正确位置
```bash
# 确保在项目根目录
cd /home/<USER>/projects/wenjuan-deploy

# 创建配置目录结构
mkdir -p docker-configs/{mysql,backend,frontend,nginx}
mkdir -p config

# 复制配置文件（假设配置文件已经准备好）
# 这些文件应该在之前创建的config-files目录中
```

### 4.2 设置文件权限
```bash
# 设置执行权限
chmod +x deployment-scripts/deploy.sh
chmod +x docker-configs/backend/start.sh

# 设置配置文件权限
chmod 644 config/*.yml
chmod 644 docker-configs/*/*.conf
```

## 第五步：构建Docker镜像

### 5.1 检查Docker文件
```bash
# 查看Dockerfile
ls -la docker-configs/*/Dockerfile

# 查看docker-compose.yml
cat docker-compose.yml
```

### 5.2 构建镜像
```bash
# 方法1：使用docker-compose构建
docker-compose build

# 方法2：单独构建每个镜像
docker build -t wenjuan-mysql:latest -f docker-configs/mysql/Dockerfile .
docker build -t wenjuan-backend:latest -f docker-configs/backend/Dockerfile .
docker build -t wenjuan-frontend:latest -f docker-configs/frontend/Dockerfile .

# 查看构建的镜像
docker images | grep wenjuan
```

### 5.3 检查镜像构建结果
```bash
# 查看所有镜像
docker images

# 查看镜像详细信息
docker inspect wenjuan-mysql:latest
docker inspect wenjuan-backend:latest
docker inspect wenjuan-frontend:latest
```

## 第六步：启动容器服务

### 6.1 启动服务
```bash
# 使用docker-compose启动所有服务
docker-compose up -d

# 或者使用部署脚本
./deployment-scripts/deploy.sh start
```

### 6.2 查看启动过程
```bash
# 查看容器启动日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f wenjuan-mysql
docker-compose logs -f wenjuan-backend
docker-compose logs -f wenjuan-frontend
```

## 第七步：检查容器状态

### 7.1 查看容器运行状态
```bash
# 查看所有容器
docker ps -a

# 查看docker-compose服务状态
docker-compose ps

# 查看容器详细信息
docker inspect wenjuan-mysql
docker inspect wenjuan-backend
docker inspect wenjuan-frontend
docker inspect wenjuan-nginx
```

### 7.2 检查容器健康状态
```bash
# 查看健康检查状态
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 检查特定容器健康状态
docker inspect --format='{{.State.Health.Status}}' wenjuan-mysql
docker inspect --format='{{.State.Health.Status}}' wenjuan-backend
docker inspect --format='{{.State.Health.Status}}' wenjuan-frontend
```

### 7.3 查看容器资源使用
```bash
# 查看容器资源使用情况
docker stats

# 查看系统资源
free -h
df -h
```

## 第八步：检查网络和端口

### 8.1 检查Docker网络
```bash
# 查看Docker网络
docker network ls

# 查看项目网络详情
docker network inspect wenjuan-deploy_wenjuan-network
```

### 8.2 检查端口监听
```bash
# 检查端口占用
sudo netstat -tlnp | grep -E "(13306|18999|18888|18080)"

# 或使用ss命令
sudo ss -tlnp | grep -E "(13306|18999|18888|18080)"

# 检查防火墙状态
sudo ufw status
```

### 8.3 测试容器间连接
```bash
# 测试容器间网络连接
docker exec wenjuan-backend ping -c 3 wenjuan-mysql
docker exec wenjuan-frontend ping -c 3 wenjuan-backend
```

## 第九步：验证服务功能

### 9.1 测试数据库连接
```bash
# 进入MySQL容器
docker exec -it wenjuan-mysql mysql -u wenjuan -pwenjuan2024

# 在MySQL中执行测试
USE wenjuan;
SHOW TABLES;
SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'wenjuan';
EXIT;
```

### 9.2 测试后端API
```bash
# 测试健康检查接口
curl -I http://localhost:18999/actuator/health

# 测试API响应
curl http://localhost:18999/actuator/health | jq .

# 如果没有jq，直接查看响应
curl http://localhost:18999/actuator/health
```

### 9.3 测试前端访问
```bash
# 测试前端页面
curl -I http://localhost:18888

# 测试反向代理
curl -I http://localhost:18080

# 测试完整页面加载
curl -s http://localhost:18888 | grep -i "title"
```

## 第十步：配置防火墙和安全

### 10.1 配置防火墙
```bash
# 检查防火墙状态
sudo ufw status

# 开放必要端口
sudo ufw allow 18080/tcp comment "Wenjuan Nginx Proxy"
sudo ufw allow 18888/tcp comment "Wenjuan Frontend"
sudo ufw allow 18999/tcp comment "Wenjuan Backend"
sudo ufw allow 13306/tcp comment "Wenjuan MySQL"

# 启用防火墙（如果未启用）
sudo ufw enable

# 查看规则
sudo ufw status numbered
```

### 10.2 设置日志轮转
```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/wenjuan-docker << EOF
/home/<USER>/projects/wenjuan-deploy/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

## 第十一步：服务管理

### 11.1 常用管理命令
```bash
# 查看服务状态
./deployment-scripts/deploy.sh status

# 查看日志
./deployment-scripts/deploy.sh logs

# 重启服务
./deployment-scripts/deploy.sh restart

# 停止服务
./deployment-scripts/deploy.sh stop

# 健康检查
./deployment-scripts/deploy.sh health
```

### 11.2 备份和恢复
```bash
# 备份数据
./deployment-scripts/deploy.sh backup

# 手动备份MySQL
docker exec wenjuan-mysql mysqldump -u wenjuan -pwenjuan2024 wenjuan > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份Docker卷
docker run --rm -v wenjuan-deploy_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_data_backup.tar.gz -C /data .
```

## 第十二步：监控和维护

### 12.1 设置监控
```bash
# 安装htop用于系统监控
sudo apt install -y htop

# 查看系统资源
htop

# 监控Docker容器
watch docker stats
```

### 12.2 定期维护
```bash
# 清理未使用的Docker资源
docker system prune -f

# 清理未使用的镜像
docker image prune -f

# 查看磁盘使用
df -h
du -sh /var/lib/docker/
```

## 故障排除

### 常见问题及解决方案

1. **容器启动失败**
```bash
# 查看详细错误日志
docker logs container-name
docker-compose logs service-name
```

2. **端口冲突**
```bash
# 查找占用端口的进程
sudo lsof -i :port-number
sudo netstat -tlnp | grep port-number
```

3. **数据库连接失败**
```bash
# 检查MySQL容器状态
docker exec wenjuan-mysql mysqladmin ping -h localhost -u root -pwenjuan2024
```

4. **内存不足**
```bash
# 查看内存使用
free -h
# 调整JVM参数或增加服务器内存
```

完成以上步骤后，您的在线问卷系统就成功部署在Ubuntu服务器上了！
