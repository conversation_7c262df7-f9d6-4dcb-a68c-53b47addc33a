version: '3.8'

services:
  # MySQL数据库
  wenjuan-mysql:
    build:
      context: ./docker-configs/mysql
      dockerfile: Dockerfile
    container_name: wenjuan-mysql
    restart: always
    ports:
      - "13306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./wenjuan.sql:/docker-entrypoint-initdb.d/init.sql:ro
    environment:
      MYSQL_ROOT_PASSWORD: wenjuan2024
      MYSQL_DATABASE: wenjuan
      MYSQL_USER: wenjuan
      MYSQL_PASSWORD: wenjuan2024
      TZ: Asia/Shanghai
    networks:
      - wenjuan-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pwenjuan2024"]
      timeout: 20s
      retries: 10
      interval: 30s
      start_period: 60s

  # 后端应用
  wenjuan-backend:
    build:
      context: .
      dockerfile: ./docker-configs/backend/Dockerfile
    container_name: wenjuan-backend
    restart: always
    ports:
      - "18999:8999"
    depends_on:
      wenjuan-mysql:
        condition: service_healthy
    volumes:
      - ./config/application-prod.yml:/app/config/application-prod.yml:ro
      - ./images:/app/images:ro
      - backend_logs:/app/logs
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: "-Xms512m -Xmx1024m -XX:+UseG1GC -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"
      TZ: Asia/Shanghai
    networks:
      - wenjuan-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8999/actuator/health || exit 1"]
      timeout: 10s
      retries: 5
      interval: 30s
      start_period: 120s

  # 前端应用
  wenjuan-frontend:
    build:
      context: .
      dockerfile: ./docker-configs/frontend/Dockerfile
    container_name: wenjuan-frontend
    restart: always
    ports:
      - "18888:80"
    depends_on:
      wenjuan-backend:
        condition: service_healthy
    volumes:
      - ./docker-configs/frontend/nginx.conf:/etc/nginx/nginx.conf:ro
    environment:
      TZ: Asia/Shanghai
    networks:
      - wenjuan-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80 || exit 1"]
      timeout: 5s
      retries: 3
      interval: 30s

  # Nginx反向代理
  wenjuan-nginx:
    image: nginx:alpine
    container_name: wenjuan-nginx
    restart: always
    ports:
      - "18080:80"
    volumes:
      - ./docker-configs/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker-configs/nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - wenjuan-frontend
      - wenjuan-backend
    environment:
      TZ: Asia/Shanghai
    networks:
      - wenjuan-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80 || exit 1"]
      timeout: 5s
      retries: 3
      interval: 30s

volumes:
  mysql_data:
    driver: local
  backend_logs:
    driver: local

networks:
  wenjuan-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
