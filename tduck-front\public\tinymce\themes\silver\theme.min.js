/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.0 (2021-10-11)
 */
!function(){"use strict";function t(o){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===o;var n,e}}function n(n){return function(t){return typeof t===n}}function e(n){return function(t){return n===t}}function g(t){return null==t}function f(t,n){if(c(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return;return 1}}function st(){}function r(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}}function rt(t){return function(){return t}}function h(t){return t}function v(t,n){return t===n}var y=t("string"),x=t("object"),c=t("array"),b=e(null),w=n("boolean"),E=e(void 0),d=function(t){return!g(t)},m=n("function"),u=n("number");function S(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}function C(n){return function(t){return!n(t)}}function k(t){return function(){throw new Error(t)}}var O=rt(!1),_=rt(!0),o=tinymce.util.Tools.resolve("tinymce.ThemeManager"),lt=function(){return(lt=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function s(t,n){var e={};for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(e[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(e[r[o]]=t[r[o]]);return e}function H(t,n,e){if(e||2===arguments.length)for(var o,r=0,i=n.length;r<i;r++)!o&&r in n||((o=o||Array.prototype.slice.call(n,0,r))[r]=n[r]);return t.concat(o||Array.prototype.slice.call(n))}function i(){return a}var a={fold:function(t,n){return t()},isSome:O,isNone:_,getOr:h,getOrThunk:l,getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:rt(null),getOrUndefined:rt(void 0),or:h,orThunk:l,map:i,each:st,bind:i,exists:O,forall:_,filter:function(){return a},toArray:function(){return[]},toString:rt("none()")};function l(t){return t()}function p(t,n){return yt.call(t,n)}function T(t,n){for(var e=0,o=t.length;e<o;e++)if(n(t[e],e))return!0;return!1}function D(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e}function B(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=bt.call(t,o,o+n);e.push(r)}return e}function M(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o}function A(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r)?e:o).push(u)}return{pass:e,fail:o}}function F(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e}function I(t,o,r){return function(t){for(var n,e=t.length-1;0<=e;e--)n=t[e],r=o(r,n,e)}(t),r}function R(t,e,o){return St(t,function(t,n){o=e(o,t,n)}),o}function V(t,n){return function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return vt.some(i);if(e(i,o))break}return vt.none()}(t,n,O)}function P(t,n){for(var e=0,o=t.length;e<o;e++)if(n(t[e],e))return vt.some(e);return vt.none()}function ft(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!c(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);xt.apply(n,t[e])}return n}function z(t,n){return ft(M(t,n))}function N(t,n){for(var e=0,o=t.length;e<o;++e)if(!0!==n(t[e],e))return!1;return!0}function L(t){var n=bt.call(t,0);return n.reverse(),n}function W(t,n){return F(t,function(t){return!wt(n,t)})}function U(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e}function j(t){return[t]}function G(t,n){var e=bt.call(t,0);return e.sort(n),e}function X(t,n){return 0<=n&&n<t.length?vt.some(t[n]):vt.none()}function Y(t){return X(t,0)}function q(t){return X(t,t.length-1)}function K(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return vt.none()}function J(t,n){for(var e=kt(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i)}}function dt(t,e){return _t(t,function(t,n){return{k:n,v:e(t,n)}})}function $(t,e){var o=[];return J(t,function(t,n){o.push(e(t,n))}),o}function Q(t,n){for(var e=kt(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return vt.some(u)}return vt.none()}function Z(t){return $(t,h)}function tt(t,n){return Tt(t,n)?vt.from(t[n]):vt.none()}function nt(t,n){return Tt(t,n)&&void 0!==t[n]&&null!==t[n]}function mt(t,n,e){return void 0===e&&(e=v),t.exists(function(t){return e(t,n)})}function et(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n}function ot(t,n){return t?vt.some(n):vt.none()}function it(t,n,e){return""===n||t.length>=n.length&&t.substr(e,e+n.length)===n}function ut(t,n){return-1!==t.indexOf(n)}function at(t){return 0<t.length}function ct(t){return void 0!==t.style&&m(t.style.getPropertyValue)}function gt(t){if(null==t)throw new Error("Node cannot be null or undefined");return{dom:t}}var pt,ht=function(e){function t(){return r}function n(t){return t(e)}var o=rt(e),r={fold:function(t,n){return n(e)},isSome:_,isNone:O,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:t,orThunk:t,map:function(t){return ht(t(e))},each:function(t){t(e)},bind:n,exists:n,forall:n,filter:function(t){return t(e)?r:a},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return r},vt={some:ht,none:i,from:function(t){return null==t?a:ht(t)}},bt=Array.prototype.slice,yt=Array.prototype.indexOf,xt=Array.prototype.push,wt=function(t,n){return-1<p(t,n)},St=function(t,n){for(var e=0,o=t.length;e<o;e++)n(t[e],e)},Ct=m(Array.from)?Array.from:function(t){return bt.call(t)},kt=Object.keys,Ot=Object.hasOwnProperty,_t=function(t,o){var r={};return J(t,function(t,n){var e=o(t,n);r[e.k]=e.v}),r},Tt=function(t,n){return Ot.call(t,n)},Et=function(t,n,e){return t.isSome()&&n.isSome()?vt.some(e(t.getOrDie(),n.getOrDie())):vt.none()},Dt=function(t,n){return it(t,n,0)},Bt=function(t,n){return it(t,n,t.length-n.length)},Mt=(pt=/^\s+|\s+$/g,function(t){return t.replace(pt,"")}),At={fromHtml:function(t,n){var e=(n||document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return gt(e.childNodes[0])},fromTag:function(t,n){var e=(n||document).createElement(t);return gt(e)},fromText:function(t,n){var e=(n||document).createTextNode(t);return gt(e)},fromDom:gt,fromPoint:function(t,n,e){return vt.from(t.dom.elementFromPoint(n,e)).map(gt)}};function Ft(t){return t.dom.nodeName.toLowerCase()}function It(n){return function(t){return t.dom.nodeType===n}}function Rt(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}}function Vt(t,n){var e=String(n).toLowerCase();return V(t,function(t){return t.search(e)})}function Pt(n){return function(t){return ut(t,n)}}function Ht(t){return window.matchMedia(t).matches}function zt(t,n){var e=t.dom;if(1!==e.nodeType)return!1;var o=e;if(void 0!==o.matches)return o.matches(n);if(void 0!==o.msMatchesSelector)return o.msMatchesSelector(n);if(void 0!==o.webkitMatchesSelector)return o.webkitMatchesSelector(n);if(void 0!==o.mozMatchesSelector)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")}function Nt(t){return 1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount}function Lt(t,n){return t.dom===n.dom}function Wt(t,n){return se().browser.isIE()?(e=t.dom,o=n.dom,r=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(e.compareDocumentPosition(o)&r)):(i=t.dom)!==(u=n.dom)&&i.contains(u);var e,o,r,i,u}function Ut(t){return At.fromDom(t.dom.ownerDocument)}function jt(t){return Yn(t)?t:Ut(t)}function Gt(t){return At.fromDom(jt(t).dom.documentElement)}function Xt(t){return At.fromDom(jt(t).dom.defaultView)}function Yt(t){return vt.from(t.dom.parentNode).map(At.fromDom)}function qt(t){return vt.from(t.dom.offsetParent).map(At.fromDom)}function Kt(t){return M(t.dom.childNodes,At.fromDom)}function Jt(t,n){var e=t.dom.childNodes;return vt.from(e[n]).map(At.fromDom)}function $t(t,n){return{element:t,offset:n}}function Qt(t,n){var e=Kt(t);return 0<e.length&&n<e.length?$t(e[n],0):$t(t,n)}function Zt(t){return qn(t)&&d(t.dom.host)}function tn(t){return Zt(t)?t:At.fromDom(jt(t).dom.body)}function nn(t){return At.fromDom(t.dom.host)}function en(t,n,e){if(!(y(e)||w(e)||u(e)))throw console.error("Invalid call to Attribute.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")}function on(t,n,e){en(t.dom,n,e)}function rn(t,n){var e=t.dom.getAttribute(n);return null===e?void 0:e}function un(t,n){return vt.from(rn(t,n))}function an(t,n){var e=t.dom;return!(!e||!e.hasAttribute)&&e.hasAttribute(n)}function cn(t,n){t.dom.removeAttribute(n)}function sn(t,n,e){if(!y(e))throw console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);ct(t)&&t.style.setProperty(n,e)}function ln(t,n){ct(t)&&t.style.removeProperty(n)}function fn(t,n,e){sn(t.dom,n,e)}function dn(t,n){var e=t.dom;J(n,function(t,n){sn(e,n,t)})}function mn(t,n){var e=t.dom;J(n,function(t,n){t.fold(function(){ln(e,n)},function(t){sn(e,n,t)})})}function gn(t,n){var e=t.dom,o=window.getComputedStyle(e).getPropertyValue(n);return""!==o||he(t)?o:ye(e,n)}function pn(t,n){var e=t.dom,o=ye(e,n);return vt.from(o).filter(function(t){return 0<t.length})}function hn(t,n,e){var o=At.fromTag(t);return fn(o,n,e),pn(o,n).isSome()}function vn(t,n){ln(t.dom,n),mt(un(t,"style").map(Mt),"")&&cn(t,"style")}function bn(t){return t.dom.offsetWidth}function yn(o,r){function t(t){var n=r(t);if(n<=0||null===n){var e=gn(t,o);return parseFloat(e)||0}return n}function i(r,t){return R(t,function(t,n){var e=gn(r,n),o=void 0===e?0:parseInt(e,10);return isNaN(o)?t:t+o},0)}return{set:function(t,n){if(!u(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom;ct(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}}function xn(t){return xe.get(t)}function wn(t){return xe.getOuter(t)}function Sn(t,n){return void 0!==t?t:void 0!==n?n:0}function Cn(t){var n=t.dom.ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom)return Se(e.offsetLeft,e.offsetTop);var i=Sn(null==o?void 0:o.pageYOffset,r.scrollTop),u=Sn(null==o?void 0:o.pageXOffset,r.scrollLeft),a=Sn(r.clientTop,e.clientTop),c=Sn(r.clientLeft,e.clientLeft);return Ce(t).translate(u-c,i-a)}function kn(t){return ke.get(t)}function On(t){return ke.getOuter(t)}function _n(t){function n(){return t.stopPropagation()}function e(){return t.preventDefault()}var o=r(e,n);return{target:At.fromDom(function(t){if(de()&&d(t.target)){var n=At.fromDom(t.target);if(Gn(n)&&pe(n)&&t.composed&&t.composedPath){var e=t.composedPath();if(e)return Y(e)}}return vt.from(t.target)}(t).getOr(t.target)),x:t.clientX,y:t.clientY,stop:n,prevent:e,kill:o,raw:t}}function Tn(t,n,e,o,r){var i,u,a=(i=e,u=o,function(t){i(t)&&u(_n(t))});return t.dom.addEventListener(n,a,r),{unbind:S(Oe,t,n,a,r)}}function En(n,e){Yt(n).each(function(t){t.dom.insertBefore(e.dom,n.dom)})}function Dn(t,n){vt.from(t.dom.nextSibling).map(At.fromDom).fold(function(){Yt(t).each(function(t){_e(t,n)})},function(t){En(t,n)})}function Bn(n,e){Jt(n,0).fold(function(){_e(n,e)},function(t){n.dom.insertBefore(e.dom,t.dom)})}function Mn(n,t){St(t,function(t){_e(n,t)})}function An(t){t.dom.textContent="",St(Kt(t),function(t){Te(t)})}function Fn(t){var n,e=Kt(t);0<e.length&&(n=t,St(e,function(t){En(n,t)})),Te(t)}function In(t){var n=void 0!==t?t.dom:document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return Se(e,o)}function Rn(t,n,e){var o=(void 0!==e?e.dom:document).defaultView;o&&o.scrollTo(t,n)}function Vn(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}}function Pn(t){var o=void 0===t?window:t,n=o.document,r=In(At.fromDom(n)),e=void 0===o?window:o;return(se().browser.isFirefox()?vt.none():vt.from(e.visualViewport)).fold(function(){var t=o.document.documentElement,n=t.clientWidth,e=t.clientHeight;return Vn(r.left,r.top,n,e)},function(t){return Vn(Math.max(t.pageLeft,r.left),Math.max(t.pageTop,r.top),t.width,t.height)})}function Hn(o){var t,r=In(At.fromDom(document)),n=(t=De).owner(o),e=Ee(t,n);return vt.some(e).fold(S(Cn,o),function(t){var n=Ce(o),e=I(t,function(t,n){var e=Ce(n);return{left:t.left+e.left,top:t.top+e.top}},{left:0,top:0});return Se(e.left+n.left+r.left,e.top+n.top+r.top)})}function zn(t){var n=Hn(t),e=On(t),o=wn(t);return Be(n.left,n.top,e,o)}"undefined"!=typeof window||Function("return this;")();function Nn(){return Kn(0,0)}function Ln(t){function n(t){return function(){return e===t}}var e=t.current,o=t.version;return{current:e,version:o,isEdge:n("Edge"),isChrome:n("Chrome"),isIE:n("IE"),isOpera:n("Opera"),isFirefox:n(Zn),isSafari:n("Safari")}}function Wn(t){function n(t){return function(){return e===t}}var e=t.current,o=t.version;return{current:e,version:o,isWindows:n(ee),isiOS:n("iOS"),isAndroid:n(oe),isOSX:n("OSX"),isLinux:n("Linux"),isSolaris:n(re),isFreeBSD:n(ie),isChromeOS:n(ue)}}var Un,jn,Gn=It(1),Xn=It(3),Yn=It(9),qn=It(11),Kn=function(t,n){return{major:t,minor:n}},Jn={nu:Kn,detect:function(t,n){var e,o,r=String(n).toLowerCase();return 0===t.length?Nn():(o=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}}(t,e=r))?Kn(i(1),i(2)):{major:0,minor:0};function i(t){return Number(e.replace(o,"$"+t))}},unknown:Nn},$n=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Qn={browsers:rt([{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return ut(t,"edge/")&&ut(t,"chrome")&&ut(t,"safari")&&ut(t,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,$n],search:function(t){return ut(t,"chrome")&&!ut(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return ut(t,"msie")||ut(t,"trident")}},{name:"Opera",versionRegexes:[$n,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Pt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Pt("firefox")},{name:"Safari",versionRegexes:[$n,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(ut(t,"safari")||ut(t,"mobile/"))&&ut(t,"applewebkit")}}]),oses:rt([{name:"Windows",search:Pt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return ut(t,"iphone")||ut(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Pt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Pt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Pt("linux"),versionRegexes:[]},{name:"Solaris",search:Pt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Pt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Pt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}])},Zn="Firefox",te=function(){return Ln({current:void 0,version:Jn.unknown()})},ne=Ln,ee=(rt("Edge"),rt("Chrome"),rt("IE"),rt("Opera"),rt(Zn),rt("Safari"),"Windows"),oe="Android",re="Solaris",ie="FreeBSD",ue="ChromeOS",ae=function(){return Wn({current:void 0,version:Jn.unknown()})},ce=Wn,se=(rt(ee),rt("iOS"),rt(oe),rt("Linux"),rt("OSX"),rt(re),rt(ie),rt(ue),Rt(function(){return t=navigator.userAgent,n=vt.from(navigator.userAgentData),e=Ht,p=Qn.browsers(),h=Qn.oses(),v=n.bind(function(t){return o=p,K(t.brands,function(n){var e=n.brand.toLowerCase();return V(o,function(t){var n;return e===(null===(n=t.brand)||void 0===n?void 0:n.toLowerCase())}).map(function(t){return{current:t.name,version:Jn.nu(parseInt(n.version,10),0)}})});var o}).orThunk(function(){return Vt(p,e=t).map(function(t){var n=Jn.detect(t.versionRegexes,e);return{current:t.name,version:n}});var e}).fold(te,ne),b=Vt(h,o=t).map(function(t){var n=Jn.detect(t.versionRegexes,o);return{current:t.name,version:n}}).fold(ae,ce),{browser:v,os:b,deviceType:(i=v,u=t,a=e,c=(r=b).isiOS()&&!0===/ipad/i.test(u),s=r.isiOS()&&!c,f=(l=r.isiOS()||r.isAndroid())||a("(pointer:coarse)"),d=c||!s&&l&&a("(min-device-width:768px)"),m=s||l&&!d,g=i.isSafari()&&r.isiOS()&&!1===/safari/i.test(u),{isiPad:rt(c),isiPhone:rt(s),isTablet:rt(d),isPhone:rt(m),isTouch:rt(f),isAndroid:r.isAndroid,isiOS:r.isiOS,isWebView:rt(g),isDesktop:rt(!m&&!d&&!g)})};var t,n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b})),le=Yt,fe=m(Element.prototype.attachShadow)&&m(Node.prototype.getRootNode),de=rt(fe),me=fe?function(t){return At.fromDom(t.dom.getRootNode())}:jt,ge=function(t){var n=me(t);return Zt(n)?vt.some(n):vt.none()},pe=function(t){return d(t.dom.shadowRoot)},he=function(t){var n=Xn(t)?t.dom.parentNode:t.dom;if(null==n||null===n.ownerDocument)return!1;var e,o,r=n.ownerDocument;return ge(At.fromDom(n)).fold(function(){return r.body.contains(n)},(e=he,o=nn,function(t){return e(o(t))}))},ve=function(){return be(At.fromDom(document))},be=function(t){var n=t.dom.body;if(null==n)throw new Error("Body is not available yet");return At.fromDom(n)},ye=function(t,n){return ct(t)?t.style.getPropertyValue(n):""},xe=yn("height",function(t){var n=t.dom;return he(t)?n.getBoundingClientRect().height:n.offsetHeight}),we=function(e,o){return{left:e,top:o,translate:function(t,n){return we(e+t,o+n)}}},Se=we,Ce=function(t){var n,e=t.dom,o=e.ownerDocument.body;return o===e?Se(o.offsetLeft,o.offsetTop):he(t)?(n=e.getBoundingClientRect(),Se(n.left,n.top)):Se(0,0)},ke=yn("width",function(t){return t.dom.offsetWidth}),Oe=function(t,n,e,o){t.dom.removeEventListener(n,e,o)},_e=function(t,n){t.dom.appendChild(n.dom)},Te=function(t){var n=t.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},Ee=function(o,t){return o.view(t).fold(rt([]),function(t){var n=o.owner(t),e=Ee(o,n);return[t].concat(e)})},De=Object.freeze({__proto__:null,view:function(t){var n;return(t.dom===document?vt.none():vt.from(null===(n=t.dom.defaultView)||void 0===n?void 0:n.frameElement)).map(At.fromDom)},owner:Ut}),Be=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Me=function(t){var n=Cn(t),e=On(t),o=wn(t);return Be(n.left,n.top,e,o)},Ae=function(){return Pn(window)},Fe=function(e){return{isValue:_,isError:O,getOr:rt(e),getOrThunk:rt(e),getOrDie:rt(e),or:function(t){return Fe(e)},orThunk:function(t){return Fe(e)},fold:function(t,n){return n(e)},map:function(t){return Fe(t(e))},mapError:function(t){return Fe(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOptional:function(){return vt.some(e)}}},Ie=function(e){return{isValue:O,isError:_,getOr:h,getOrThunk:function(t){return t()},getOrDie:function(){return k(String(e))()},or:h,orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return Ie(e)},mapError:function(t){return Ie(t(e))},each:st,bind:function(t){return Ie(e)},exists:O,forall:_,toOptional:vt.none}},Re={value:Fe,error:Ie,fromOption:function(t,n){return t.fold(function(){return Ie(n)},Fe)}};function Ve(t,n,e){return t.stype===Un.Error?n(t.serror):e(t.svalue)}function Pe(t){return{stype:Un.Value,svalue:t}}function He(t){return{stype:Un.Error,serror:t}}function ze(t,n,e,o){return{tag:"field",key:t,newKey:n,presence:e,prop:o}}function Ne(t,n,e){switch(t.tag){case"field":return n(t.key,t.newKey,t.presence,t.prop);case"custom":return e(t.newKey,t.instantiator)}}function Le(u){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r,i=t[o];for(r in i)Tt(i,r)&&(e[r]=u(e[r],i[r]))}return e}}function We(){return{tag:"required",process:{}}}function Ue(t){return{tag:"defaultedThunk",process:t}}function je(t){return Ue(rt(t))}function Ge(){return{tag:"option",process:{}}}function Xe(t){return{tag:"mergeWithThunk",process:t}}function Ye(t){return x(t)&&100<kt(t).length?" removed due to size":JSON.stringify(t,null,2)}function qe(t,n){return No([{path:t,getErrorInfo:n}])}function Ke(e){return{extract:function(n,t){return Wo(e(t),function(t){return qe(n,rt(t))})},toString:rt("val")}}function Je(t,n,e,o){return o(tt(t,n).getOrThunk(function(){return e(t)}))}function $e(n,e,o,r,i){function u(t){return i.extract(e.concat([r]),t)}function t(t){return t.fold(function(){return zo(vt.none())},function(t){var n=i.extract(e.concat([r]),t);return Uo(n,vt.some)})}var a,c,s,l,f,d;switch(n.tag){case"required":return s=e,d=u,tt(l=o,f=r).fold(function(){return t=f,n=l,qe(s,function(){return'Could not find valid *required* value for "'+t+'" in '+Ye(n)});var t,n},d);case"defaultedThunk":return Je(o,r,n.process,u);case"option":return t(tt(o,r));case"defaultedOptionThunk":return c=n.process,t(tt(a=o,r).map(function(t){return!0===t?c(a):t}));case"mergeWithThunk":return Je(o,r,rt({}),function(t){return u(Xo(n.process(o),t))})}}function Qe(e){return{extract:function(t,n){return e().extract(t,n)},toString:function(){return e().toString()}}}function Ze(t){var s=Jo(t),l=I(t,function(e,t){return Ne(t,function(t){var n;return Xo(e,((n={})[t]=!0,n))},rt(e))},{});return{extract:function(t,n){var e,o,r,i,u,a,c=F(w(n)?[]:kt((r=d,i=o={},u=function(t,n){i[n]=t},a=st,J(n,function(t,n){(r(t,n)?u:a)(t,n)}),o)),function(t){return!nt(l,t)});return 0===c.length?s.extract(t,n):(e=c,qe(t,function(){return"There are unsupported fields: ["+e.join(", ")+"] specified"}))},toString:s.toString}}function to(o){return{extract:function(e,t){var n=M(t,function(t,n){return o.extract(e.concat(["["+n+"]"]),t)});return qo(n)},toString:function(){return"array("+o.toString()+")"}}}function no(u){return{extract:function(t,n){for(var e=[],o=0,r=u;o<r.length;o++){var i=r[o].extract(t,n);if(i.stype===Un.Value)return i;e.push(i)}return qo(e)},toString:function(){return"oneOf("+M(u,function(t){return t.toString()}).join(", ")+")"}}}function eo(e,o){return Ke(function(t){var n=typeof t;return e(t)?zo(t):No("Expected type: "+o+" but got: "+n)})}function oo(n,a){return{extract:function(i,u){return tt(u,n).fold(function(){return t=n,qe(i,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return e=i,n=u,tt(o=a,r=t).fold(function(){return t=o,n=r,qe(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+Ye(t)});var t,n},function(t){return t.extract(e.concat(["branch: "+r]),n)});var e,n,o,r})},toString:function(){return"chooseOn("+n+"). Possible values: "+kt(a)}}}function ro(n){return Ke(function(t){return n(t).fold(No,zo)})}function io(n,t){return r=function(t){return n(t).fold(He,Pe)},i=t,{extract:function(e,o){var t=kt(o),n=to(Ke(r)).extract(e,t);return Lo(n,function(t){var n=M(t,function(t){return ze(t,t,We(),i)});return Jo(n).extract(e,o)})},toString:function(){return"setOf("+i.toString()+")"}};var r,i}function uo(t,n,e){return Ho((r=n.extract([t],o=e),jo(r,function(t){return{input:o,errors:t}})));var o,r}function ao(t){return t.fold(function(t){throw new Error(ir(t))},h)}function co(t,n,e){return ao(uo(t,n,e))}function so(t,n){return oo(t,dt(n,Jo))}function lo(n){return ro(function(t){return wt(n,t)?Re.value(t):Re.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})}function fo(t){return ur(t,t,We(),Qo())}function mo(t,n){return ur(t,t,We(),n)}function go(t){return mo(t,tr)}function po(t,n){return ur(t,t,We(),lo(n))}function ho(t){return mo(t,er)}function vo(t,n){return ur(t,t,We(),Jo(n))}function bo(t,n){return ur(t,t,We(),$o(n))}function yo(t,n){return ur(t,t,We(),to(n))}function xo(t){return ur(t,t,Ge(),Qo())}function wo(t,n){return ur(t,t,Ge(),n)}function So(t){return wo(t,Zo)}function Co(t){return wo(t,tr)}function ko(t){return wo(t,er)}function Oo(t,n){return wo(t,to(n))}function _o(t,n){return wo(t,Jo(n))}function To(t,n){return ur(t,t,je(n),Qo())}function Eo(t,n,e){return ur(t,t,je(n),e)}function Do(t,n){return Eo(t,n,Zo)}function Bo(t,n){return Eo(t,n,tr)}function Mo(t,n,e){return Eo(t,n,lo(e))}function Ao(t,n){return Eo(t,n,nr)}function Fo(t,n){return Eo(t,n,er)}function Io(t,n,e){return Eo(t,n,to(e))}function Ro(t,n,e){return Eo(t,n,Jo(e))}function Vo(t){var n=t;return{get:function(){return n},set:function(t){n=t}}}(jn=Un={})[jn.Error=0]="Error",jn[jn.Value=1]="Value";function Po(u){if(!c(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return St(u,function(t,o){var n=kt(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(void 0!==e[r])throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!c(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);return{fold:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(t.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+t.length);return t[o].apply(null,e)},match:function(t){var n=kt(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!N(a,function(t){return wt(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){console.log(t,{constructors:a,constructor:r,params:e})}}}}),e}var Ho=function(t){return Ve(t,Re.error,Re.value)},zo=Pe,No=He,Lo=function(t,n){return t.stype===Un.Value?n(t.svalue):t},Wo=function(t,n){return t.stype===Un.Error?n(t.serror):t},Uo=function(t,n){return t.stype===Un.Value?{stype:Un.Value,svalue:n(t.svalue)}:t},jo=function(t,n){return t.stype===Un.Error?{stype:Un.Error,serror:n(t.serror)}:t},Go=Ve,Xo=Le(function(t,n){return x(t)&&x(n)?Xo(t,n):n}),Yo=Le(function(t,n){return n}),qo=function(t){var n,e,o=(n=[],e=[],St(t,function(t){Ve(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e});return 0<o.errors.length?r(No,ft)(o.errors):zo(o.values)},Ko=Ke(zo),Jo=function(e){return{extract:function(i,u){for(var a={},c=[],t=0,n=e;t<n.length;t++)Ne(n[t],function(t,n,e,o){var r=$e(e,i,u,t,o);Go(r,function(t){c.push.apply(c,t)},function(t){a[n]=t})},function(t,n){a[t]=n(u)});return 0<c.length?No(c):zo(a)},toString:function(){return"obj{\n"+M(e,function(t){return Ne(t,function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"}}},$o=r(to,Jo),Qo=rt(Ko),Zo=eo(u,"number"),tr=eo(y,"string"),nr=eo(w,"boolean"),er=eo(m,"function"),or=function(n){if(Object(n)!==n)return!0;switch({}.toString.call(n).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(n).every(function(t){return or(n[t])});default:return!1}},rr=Ke(function(t){return or(t)?zo(t):No("Expected value to be acceptable for sending via postMessage")}),ir=function(t){return"Errors: \n"+M(10<(n=t.errors).length?n.slice(0,10).concat([{path:[],getErrorInfo:rt("... (only showing first ten failures)")}]):n,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n")+"\n\nInput object: "+Ye(t.input);var n},ur=ze,ar=function(t,n){return{tag:"custom",newKey:t,instantiator:n}};function cr(t,n){return(e={})[t]=n,e;var e}function sr(t){return n={},St(t,function(t){n[t.key]=t.value}),n;var n}function lr(t){return m(t)?t:O}function fr(t,n,e){for(var o=t.dom,r=lr(e);o.parentNode;){var o=o.parentNode,i=At.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return vt.none()}function dr(t,n,e){var o=n(t),r=lr(e);return o.orThunk(function(){return r(t)?vt.none():fr(t,n,r)})}function mr(t,n){return Lt(t.element,n.event.target)}function gr(t){if(!nt(t,"can")&&!nt(t,"abort")&&!nt(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return lt(lt({},ui),t)}function pr(t){return rt("alloy."+t)}function hr(t,n){tu(t,t.element,n,{})}function vr(t,n,e){tu(t,t.element,n,e)}function br(t){hr(t,Fi())}function yr(t,n,e){tu(t,n,e,{})}function xr(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event)}function wr(t,n){return{key:t,value:gr({abort:n})}}function Sr(t){return{key:t,value:gr({run:function(t,n){n.event.prevent()}})}}function Cr(t,n){return{key:t,value:gr({run:n})}}function kr(t,e,o){return{key:t,value:gr({run:function(t,n){e.apply(void 0,[t,n].concat(o))}})}}function Or(t){return function(e){return{key:t,value:gr({run:function(t,n){mr(t,n)&&e(t,n)}})}}}function _r(t,n,e){var o,r=n.partUids[e];return Cr(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){xr(t,t.element,o,n)})})}function Tr(t,r){return Cr(t,function(n,t){var e=t.event,o=n.getSystem().getByDom(e.target).getOrThunk(function(){return dr(e.target,function(t){return n.getSystem().getByDom(t).toOptional()},O).getOr(n)});r(n,o,t)})}function Er(t){return Cr(t,function(t,n){n.cut()})}function Dr(t,n){return Or(t)(n)}function Br(t){return t.dom.innerHTML}function Mr(t,n){var e,o,r=Ut(t).dom,i=At.fromDom(r.createDocumentFragment());Mn(i,(e=n,(o=(r||document).createElement("div")).innerHTML=e,Kt(At.fromDom(o)))),An(t),_e(t,i)}function Ar(t){if(Zt(t))return"#shadow-root";var n=At.fromDom(t.dom.cloneNode(!1)),e=At.fromTag("div"),o=At.fromDom(n.dom.cloneNode(!0));return _e(e,o),Br(e)}function Fr(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++cu+String(n)}function Ir(t){var n=Gn(t)?t.dom[du]:null;return vt.from(n)}function Rr(n){function e(t){return"The component must be in a context to execute: "+t+(n?"\n"+Ar(n().element)+" is not in context.":"")}function t(t){return function(){throw new Error(e(t))}}function o(t){return function(){console.warn(e(t))}}return{debugInfo:rt("fake"),triggerEvent:o("triggerEvent"),triggerFocus:o("triggerFocus"),triggerEscape:o("triggerEscape"),broadcast:o("broadcast"),broadcastOn:o("broadcastOn"),broadcastEvent:o("broadcastEvent"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),isConnected:O}}function Vr(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:vu(i)}},t}function Pr(t){return cr(bu,t)}function Hr(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(void 0,H([t.getApis(),t],n,!1))},e=(n=o.toString()).indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:vu(i.slice(1))}},t;var t,n,e,r,i}function zr(t,r){var i={};return J(t,function(t,o){J(t,function(t,n){var e=tt(i,n).getOr([]);i[n]=e.concat([r(o,t)])})}),i}function Nr(t){return{classes:E(t.classes)?[]:t.classes,attributes:E(t.attributes)?{}:t.attributes,styles:E(t.styles)?{}:t.styles}}function Lr(t){return t.cHandler}function Wr(t,n){return{name:t,handler:n}}function Ur(t,n,e){var o=n[e];return o?function(u,t,a){try{var n=G(t,function(t,n){var e=t.name,o=n.name,r=a.indexOf(e),i=a.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(a,null,2));return r<i?-1:i<r?1:0});return Re.value(n)}catch(t){return Re.error([t])}}("Event: "+e,t,o).map(function(t){var e,n,o,r,i=M(t,function(t){return t.handler});return{can:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return R(n,function(t,n){return t&&n.can.apply(void 0,e)},!0)},abort:(o=n=e=i,r=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return R(o,function(t,n){return t||r(n).apply(void 0,e)},!1)}),run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];St(e,function(t){t.run.apply(void 0,n)})}}}):Re.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(M(t,function(t){return t.name}),null,2)])}function jr(t,n){var e=rn(t,n);return void 0===e||""===e?[]:e.split(" ")}function Gr(t){return void 0!==t.dom.classList}function Xr(t,n){var e,o,r;Gr(t)?t.dom.classList.add(n):(o=n,r=jr(e=t,"class").concat([o]),on(e,"class",r.join(" ")))}function Yr(t,n){var e,o,r,i;Gr(t)?t.dom.classList.remove(n):(r=n,0<(i=F(jr(o=t,"class"),function(t){return t!==r})).length?on(o,"class",i.join(" ")):cn(o,"class")),0===(Gr(e=t)?e.dom.classList:jr(e,"class")).length&&cn(e,"class")}function qr(t,n){return Gr(t)&&t.dom.classList.contains(n)}function Kr(n,t){St(t,function(t){Xr(n,t)})}function Jr(n,t){St(t,function(t){Yr(n,t)})}function $r(t){return t.dom.value}function Qr(t,n){if(void 0===n)throw new Error("Value.set was undefined");t.dom.value=n}function Zr(t){var e,n,o,r,i=(e=tt(t,"behaviours").getOr({}),z(kt(e),function(t){var n=e[t];return d(n)?[n.me]:[]}));return n=t,r=M(o=i,function(t){return _o(t.name(),[fo("config"),To("state",yu)])}),{list:o,data:dt(uo("component.behaviours",Jo(r),n.behaviours).fold(function(t){throw new Error(ir(t)+"\nComplete spec:\n"+JSON.stringify(n,null,2))},h),function(t){return rt(t.map(function(t){return{config:t.config,state:t.state.init(t.config)}}))})}}function ti(t,n,e){var o,r,i=lt(lt({},(o=t).dom),{uid:o.uid,domChildren:M(o.components,function(t){return t.element})}),u=t.domModification.fold(function(){return Nr({})},Nr),a=0<n.length?function(n,t,e,o){var r=lt({},t);function i(t){return I(t,function(t,n){return lt(lt({},n.modification),t)},{})}St(e,function(t){r[t.name()]=t.exhibit(n,o)});var u=zr(r,function(t,n){return{name:t,modification:n}});return Nr({classes:I(u.classes,function(t,n){return n.modification.concat(t)},[]),attributes:i(u.attributes),styles:i(u.styles)})}(e,{"alloy.base.modification":u},n,i):u;return lt(lt({},r=i),{attributes:lt(lt({},r.attributes),a.attributes),styles:lt(lt({},r.styles),a.styles),classes:r.classes.concat(a.classes)})}function ni(t,n,e){var o,r,i,u,a,c={"alloy.base.behaviour":t.events},s=t.eventOrder;return r=e,i=n,o=zr(lt(lt({},c),(u=r,a={},St(i,function(t){a[t.name()]=t.handlers(u)}),a)),Wr),wu(o,s).getOrDie()}function ei(e){function t(){return l}var n=Vo(hu),o=ao(uo("custom.definition",Cu,e)),r=Zr(e),i=r.list,u=r.data,a=function(t){var n=At.fromTag(t.tag),e=t.attributes,o=n.dom;J(e,function(t,n){en(o,n,t)}),Kr(n,t.classes),dn(n,t.styles),t.innerHtml.each(function(t){return Mr(n,t)});var r=t.domChildren;return Mn(n,r),t.value.each(function(t){Qr(n,t)}),t.uid,mu(n,t.uid),n}(ti(o,i,u)),c=ni(o,i,u),s=Vo(o.components),l={uid:e.uid,getSystem:n.get,config:function(t){var n=u;return(m(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(t){return m(u[t.name()])},spec:e,readState:function(t){return u[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return o.apis},connect:function(t){n.set(t)},disconnect:function(){n.set(Rr(t))},element:a,syncComponents:function(){var t=z(Kt(a),function(t){return n.get().getByDom(t).fold(function(){return[]},j)});s.set(t)},components:s.get,events:c};return l}function oi(t){var n=At.fromText(t);return ku({element:n})}Po([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var ri,ii,ui={can:_,abort:O,run:st},ai=rt,ci=ai("touchstart"),si=ai("touchmove"),li=ai("touchend"),fi=ai("touchcancel"),di=ai("mousedown"),mi=ai("mousemove"),gi=ai("mouseout"),pi=ai("mouseup"),hi=ai("mouseover"),vi=ai("focusin"),bi=ai("focusout"),yi=ai("keydown"),xi=ai("keyup"),wi=ai("input"),Si=ai("change"),Ci=ai("click"),ki=ai("transitioncancel"),Oi=ai("transitionend"),_i=ai("transitionstart"),Ti=ai("selectstart"),Ei={tap:pr("tap")},Di=pr("focus"),Bi=pr("blur.post"),Mi=pr("paste.post"),Ai=pr("receive"),Fi=pr("execute"),Ii=pr("focus.item"),Ri=Ei.tap,Vi=pr("longpress"),Pi=pr("sandbox.close"),Hi=pr("typeahead.cancel"),zi=pr("system.init"),Ni=pr("system.touchmove"),Li=pr("system.touchend"),Wi=pr("system.scroll"),Ui=pr("system.resize"),ji=pr("system.attached"),Gi=pr("system.detached"),Xi=pr("system.dismissRequested"),Yi=pr("system.repositionRequested"),qi=pr("focusmanager.shifted"),Ki=pr("slotcontainer.visibility"),Ji=pr("change.tab"),$i=pr("dismiss.tab"),Qi=pr("highlight"),Zi=pr("dehighlight"),tu=function(t,n,e,o){var r=lt({target:n},o);t.getSystem().triggerEvent(e,n,r)},nu=sr,eu=Or(ji()),ou=Or(Gi()),ru=Or(zi()),iu=(ri=Fi(),function(t){return Cr(ri,t)}),uu=nu([{key:Di(),value:gr({can:function(t,n){var e,o=n.event,r=o.originator,i=o.target;return!(Lt(e=r,t.element)&&!Lt(e,i)&&(console.warn(Di()+" did not get interpreted by the desired target. \nOriginator: "+Ar(r)+"\nTarget: "+Ar(i)+"\nCheck the "+Di()+" event handlers"),1))}})}]),au=Object.freeze({__proto__:null,events:uu}),cu=0,su=rt("alloy-id-"),lu=rt("data-alloy-id"),fu=su(),du=lu(),mu=function(t,n){Object.defineProperty(t.dom,du,{value:n,writable:!0})},gu=Fr,pu=h,hu=Rr(),vu=function(t){return M(t,function(t){return Bt(t,"/*")?t.substring(0,t.length-"/*".length):t})},bu=Fr("alloy-premade"),yu={init:function(){return xu({readState:rt("No State required")})}},xu=function(t){return t},wu=function(t,a){var n,e,o,r,i,u,c=$(t,function(r,u){return(1===r.length?Re.value(r[0].handler):Ur(r,a,u)).map(function(t){var n,i,e=(i=m(n=t)?{can:_,abort:O,run:n}:n,function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(void 0,r)?n.stop():i.can.apply(void 0,r)&&i.run.apply(void 0,r)}),o=1<r.length?F(a[u],function(n){return T(r,function(t){return t.name===n})}).join(" > "):r[0].name;return cr(u,{handler:e,purpose:o})})});return n={},e=[],o=[],St(c,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),0<(u={errors:e,values:o}).errors.length?Re.error(ft(u.errors)):(i=n,0===(r=u.values).length?Re.value(i):Re.value(Xo(i,Yo.apply(void 0,r))))},Su="alloy.base.behaviour",Cu=Jo([ur("dom","dom",We(),Jo([fo("tag"),To("styles",{}),To("classes",[]),To("attributes",{}),xo("value"),xo("innerHtml")])),fo("components"),fo("uid"),To("events",{}),To("apis",{}),ur("eventOrder","eventOrder",((ii={})[Fi()]=["disabling",Su,"toggling","typeaheadevents"],ii[Di()]=[Su,"focusing","keying"],ii[zi()]=[Su,"disabling","toggling","representing"],ii[wi()]=[Su,"representing","streaming","invalidating"],ii[Gi()]=[Su,"representing","item-events","tooltipping"],ii[di()]=["focusing",Su,"item-type-events"],ii[ci()]=["focusing",Su,"item-type-events"],ii[hi()]=["item-type-events","tooltipping"],ii[Ai()]=["receiving","reflecting","tooltipping"],Xe(rt(ii))),Qo()),xo("domModification")]),ku=function(t){var n=co("external.component",Ze([fo("element"),xo("uid")]),t),e=Vo(Rr()),o=n.uid.getOrThunk(function(){return gu("external")});mu(n.element,o);var r={uid:o,getSystem:e.get,config:vt.none,hasConfigured:O,connect:function(t){e.set(t)},disconnect:function(){e.set(Rr(function(){return r}))},getApis:function(){return{}},element:n.element,spec:t,readState:rt("No state"),syncComponents:st,components:rt([]),events:{}};return Pr(r)},Ou=gu,_u=function(u){return tt(u,bu).getOrThunk(function(){var t,n,e,o,r,i=Tt(u,"uid")?u:lt({uid:Ou("")},u);return t=pu(i),n=t.events,e=s(t,["events"]),o=M(tt(e,"components").getOr([]),_u),r=lt(lt({},e),{events:lt(lt({},au),n),components:o}),Re.value(ei(r)).getOrDie()})},Tu=Pr;function Eu(t,n,e,o,r){return t(e,o)?vt.some(e):m(r)&&r(e)?vt.none():n(e,o,r)}function Du(t,n,e){for(var o=t.dom,r=m(e)?e:O;o.parentNode;){var o=o.parentNode,i=At.fromDom(o);if(n(i))return vt.some(i);if(r(i))break}return vt.none()}function Bu(t,n,e){return Eu(function(t,n){return n(t)},Du,t,n,e)}function Mu(t,n,e){return Bu(t,n,e).isSome()}function Au(t,n,e){return Du(t,function(t){return zt(t,n)},e)}function Fu(t,n){return e=n,Nt(o=void 0===t?document:t.dom)?vt.none():vt.from(o.querySelector(e)).map(At.fromDom);var e,o}function Iu(t,n,e){return Eu(zt,Au,t,n,e)}function Ru(){var n=Fr("aria-owns");return{id:n,link:function(t){on(t,"aria-owns",n)},unlink:function(t){cn(t,"aria-owns")}}}var Vu,Pu,Hu=function(n,t){return Mu(t,function(t){return Lt(t,n.element)},O)||(e=n,Bu(t,function(t){if(!Gn(t))return!1;var n=rn(t,"id");return void 0!==n&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=rn(t,"id");return Fu(me(t),'[aria-owns="'+n+'"]')}).exists(function(t){return Hu(e,t)}));var e},zu="unknown";function Nu(n,t,e){var o,r,i,u;switch(tt(Lu.get(),n).orThunk(function(){return K(kt(Lu.get()),function(t){return-1<n.indexOf(t)?vt.some(Lu.get()[t]):vt.none()})}).getOr(Vu.NORMAL)){case Vu.NORMAL:return e(Uu());case Vu.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();wt(["mousemove","mouseover","mouseout",zi()],o)||console.log(o,{event:o,time:t-u,target:r.dom,sequence:M(i,function(t){return wt(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+Ar(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case Vu.STOP:return!0}}(Pu=Vu=Vu||{})[Pu.STOP=0]="STOP",Pu[Pu.NORMAL=1]="NORMAL",Pu[Pu.LOGGING=2]="LOGGING";var Lu=Vo({}),Wu=["alloy/data/Fields","alloy/debugging/Debugging"],Uu=rt({logEventCut:st,logEventStopped:st,logNoParent:st,logEventNoHandlers:st,logEventResponse:st,write:st}),ju=rt([fo("menu"),fo("selectedMenu")]),Gu=rt([fo("item"),fo("selectedItem")]);function Xu(){return vo("markers",[fo("backgroundMenu")].concat(ju()).concat(Gu()))}function Yu(t){return vo("markers",M(t,fo))}function qu(t,n,e){return void 0!==(o=new Error).stack&&V(o.stack.split("\n"),function(n){return 0<n.indexOf("alloy")&&!T(Wu,function(t){return-1<n.indexOf(t)})}).getOr(zu),ur(n,n,e,ro(function(e){return Re.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(void 0,t)})}));var o}function Ku(t){return qu(0,t,je(st))}function Ju(t){return qu(0,t,je(vt.none))}function $u(t){return qu(0,t,We())}function Qu(t){return qu(0,t,We())}function Zu(t,n){return ar(t,rt(n))}function ta(t){return ar(t,h)}function na(t,n,e,o,r,i,u,a){return{x:t,y:n,bubble:e,direction:o,placement:r,restriction:i,label:u+"-"+r,alwaysFit:a=void 0!==a&&a}}function ea(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r}function oa(e,n){return U(["left","right","top","bottom"],function(t){return tt(n,t).map(function(n){return function(t){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(e)})})}function ra(t,n){return t.x+t.width/2-n.width/2}function ia(t,n){return t.x+t.width-n.width}function ua(t,n){return t.y-n.height}function aa(t){return t.y+t.height}function ca(t,n){return t.y+t.height/2-n.height/2}function sa(t,n,e){return na(t.x+t.width,ca(t,n),e.east(),La(),"east",oa(t,{left:0}),ja)}function la(t,n,e){return na(t.x-n.width,ca(t,n),e.west(),Wa(),"west",oa(t,{right:1}),ja)}function fa(){return[Ga,Xa,Ya,qa,Ja,Ka,sa,la]}function da(){return[Xa,Ga,qa,Ya,Ja,Ka,sa,la]}function ma(){return[Ya,qa,Ga,Xa,Ka,Ja]}function ga(){return[qa,Ya,Xa,Ga,Ka,Ja]}function pa(){return[Ga,Xa,Ya,qa,Ja,Ka]}function ha(){return[Xa,Ga,qa,Ya,Ja,Ka]}function va(e,o,r){return ru(function(t,n){r(t,e,o)})}function ba(t){return{key:t,value:void 0}}function ya(t){var n,e,o,r,i,u,a,c,s=co("Creating behaviour: "+t.name,nc,t);return n=s.fields,e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,a=Ze(n),c=_o(e,[wo("config",Ze(n))]),Za(a,c,e,o,r,i,u)}function xa(t){var n,e=co("Creating behaviour: "+t.name,ec,t),o=so(e.branchKey,e.branches),r=e.name,i=e.active,u=e.apis,a=e.extra,c=e.state,s=_o(r,[wo("config",n=o)]);return Za(n,s,r,i,u,a,c)}function wa(){return At.fromDom(document)}function Sa(t){return t.dom.focus()}function Ca(t){var n=me(t).dom;return t.dom===n.activeElement}function ka(t){return void 0===t&&(t=wa()),vt.from(t.dom.activeElement).map(At.fromDom)}function Oa(n){return ka(me(n)).filter(function(t){return n.dom.contains(t.dom)})}function _a(t,e){var o=me(e),n=ka(o).bind(function(n){function t(t){return Lt(n,t)}var r,i;return t(e)?vt.some(e):(r=t,(i=function(t){for(var n=0;n<t.childNodes.length;n++){var e=At.fromDom(t.childNodes[n]);if(r(e))return vt.some(e);var o=i(t.childNodes[n]);if(o.isSome())return o}return vt.none()})(e.dom))}),r=t(e);return n.each(function(n){ka(o).filter(function(t){return Lt(t,n)}).fold(function(){Sa(n)},st)}),r}function Ta(t,n,e,o,r){function i(t){return t+"px"}return{position:t,left:n.map(i),top:e.map(i),right:o.map(i),bottom:r.map(i)}}function Ea(t,n){var e;mn(t,lt(lt({},e=n),{position:vt.some(e.position)}))}function Da(t,n,e,o,r,i){var u=n.rect,a=u.x-e,c=u.y-o,s=r-(a+u.width),l=i-(c+u.height),f=vt.some(a),d=vt.some(c),m=vt.some(s),g=vt.some(l),p=vt.none();return n.direction.fold(function(){return Ta(t,f,d,p,p)},function(){return Ta(t,p,d,m,p)},function(){return Ta(t,f,p,p,g)},function(){return Ta(t,p,p,m,g)},function(){return Ta(t,f,d,p,p)},function(){return Ta(t,f,p,p,g)},function(){return Ta(t,f,d,p,p)},function(){return Ta(t,p,d,m,p)})}function Ba(t,r){return t.fold(function(){var t=r.rect;return Ta("absolute",vt.some(t.x),vt.some(t.y),vt.none(),vt.none())},function(t,n,e,o){return Da("absolute",r,t,n,e,o)},function(t,n,e,o){return Da("fixed",r,t,n,e,o)})}function Ma(t,n){var e=S(Hn,n),o=t.fold(e,e,function(){var t=In();return Hn(n).translate(-t.left,-t.top)}),r=On(n),i=wn(n);return Be(o.left,o.top,r,i)}rt(Jo(Gu().concat(ju())));var Aa=rt(Jo(Gu())),Fa=rt(vo("initSize",[fo("numColumns"),fo("numRows")])),Ia=Po([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ra=Ia.southeast,Va=Ia.southwest,Pa=Ia.northeast,Ha=Ia.northwest,za=Ia.south,Na=Ia.north,La=Ia.east,Wa=Ia.west,Ua=function(t,n,e){return Math.min(Math.max(t,n),e)},ja="layout",Ga=function(t,n,e){return na(t.x,aa(t),e.southeast(),Ra(),"southeast",oa(t,{left:1,top:3}),ja)},Xa=function(t,n,e){return na(ia(t,n),aa(t),e.southwest(),Va(),"southwest",oa(t,{right:0,top:3}),ja)},Ya=function(t,n,e){return na(t.x,ua(t,n),e.northeast(),Pa(),"northeast",oa(t,{left:1,bottom:2}),ja)},qa=function(t,n,e){return na(ia(t,n),ua(t,n),e.northwest(),Ha(),"northwest",oa(t,{right:0,bottom:2}),ja)},Ka=function(t,n,e){return na(ra(t,n),ua(t,n),e.north(),Na(),"north",oa(t,{bottom:2}),ja)},Ja=function(t,n,e){return na(ra(t,n),aa(t),e.south(),za(),"south",oa(t,{top:3}),ja)},$a=Object.freeze({__proto__:null,events:function(a){return nu([Cr(Ai(),function(r,t){var n,i=a.channels,e=kt(i),u=t,o=(n=u).universal?e:F(e,function(t){return wt(n.channels,t)});St(o,function(t){var n=i[t],e=n.schema,o=co("channel["+t+"] data\nReceiver: "+Ar(r.element),e,u.data);n.onReceive(r,o)})})])}}),Qa=[mo("channels",io(Re.value,Ze([$u("onReceive"),To("schema",Qo())])))],Za=function(e,t,f,n,o,r,i){function u(t){return nt(t,f)?t[f]():vt.none()}var a=dt(o,function(t,n){return r=f,e=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:rt(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(void 0,[e,t.config,t.state].concat(n))})},o=u=n,a=(i=t).toString(),c=a.indexOf(")")+1,s=a.indexOf("("),l=a.substring(s+1,c-1).split(/,\s*/),e.toFunctionAnnotation=function(){return{name:o,parameters:vu(l.slice(0,1).concat(l.slice(3)))}},e;var r,i,u,e,o,a,c,s,l}),c=dt(r,Vr),s=lt(lt(lt({},c),a),{revoke:S(ba,f),config:function(t){var n=co(f+"-config",e,t);return{key:f,value:{config:n,me:s,configAsRaw:Rt(function(){return co(f+"-config",e,t)}),initialConfig:t,state:i}}},schema:rt(t),exhibit:function(t,e){return Et(u(t),tt(n,"exhibit"),function(t,n){return n(e,t.config,t.state)}).getOrThunk(function(){return Nr({})})},name:rt(f),handlers:function(t){return u(t).map(function(t){return tt(n,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return s},tc=sr,nc=Ze([fo("fields"),fo("name"),To("active",{}),To("apis",{}),To("state",yu),To("extra",{})]),ec=Ze([fo("branchKey"),fo("branches"),fo("name"),To("active",{}),To("apis",{}),To("state",yu),To("extra",{})]),oc=rt(void 0),rc=ya({fields:Qa,name:"receiving",active:$a}),ic=Object.freeze({__proto__:null,exhibit:function(t,n){return Nr({classes:[],styles:n.useFixed()?{}:{position:"relative"}})}}),uc=Po([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),ac=function(t,n,e){var o=Se(n,e);return t.fold(rt(o),rt(o),function(){var t=In();return o.translate(-t.left,-t.top)})};function cc(t){return un(t,Tc)}function sc(t,n,e,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,C,k,O,_,T,E,D,B,M,A,F,I,R,V,P,H,z,N,L,W,U=t.bubble,j=U.offset,G=(V=o,P=t.restriction,H=j,z=ot("left",V.x),N=ot("top",V.y),L=ot("right",V.right),W=ot("bottom",V.bottom),Be(z,N,L-z,W-N)),X=t.x+j.left,Y=t.y+j.top,q=Be(X,Y,n,e),K=(r=G.x,i=G.y,u=G.right,a=G.bottom,c=q.x,s=q.y,l=q.right,f=q.bottom,d=q.width,m=q.height,{originInBounds:r<=c&&c<=u&&i<=s&&s<=a,sizeInBounds:l<=u&&r<=l&&f<=a&&i<=f,visibleW:Math.min(d,r<=c?u-c:l-r),visibleH:Math.min(m,i<=s?a-s:f-i)}),J=K.visibleW,$=K.visibleH,Q=K.originInBounds&&K.sizeInBounds,Z=Q?q:(g=G.x,p=G.y,h=G.right,v=G.bottom,b=q.x,y=q.y,x=q.width,w=q.height,S=Math.max(g,h-x),C=Math.max(p,v-w),k=Ua(b,g,S),O=Ua(y,p,C),_=Math.min(k+x,h)-k,T=Math.min(O+w,v)-O,Be(k,O,_,T)),tt=0<Z.width&&0<Z.height,nt=(E=t.direction,M=rt((D=Z).bottom-(B=o).y),A=rt(B.bottom-D.y),F=E.fold(A,A,M,M,A,M,A,A),I=rt(D.right-B.x),R=rt(B.right-D.x),{maxWidth:E.fold(R,I,R,I,R,R,R,I),maxHeight:F}),et={rect:Z,maxHeight:nt.maxHeight,maxWidth:nt.maxWidth,direction:t.direction,placement:t.placement,classes:{on:U.classesOn,off:U.classesOff},layout:t.label,testY:Y};function ot(r,i){return P[r].map(function(t){var n="top"===r||"bottom"===r,e=n?H.top:H.left,o=("left"===r||"top"===r?Math.max:Math.min)(t,i)+e;return n?Ua(o,V.y,V.bottom):Ua(o,V.x,V.right)}).getOr(i)}return Q||t.alwaysFit?Ec.fit(et):Ec.nofit(et,J,$,tt)}function lc(t){function n(){return e.get().each(t)}var e=Vo(vt.none());return{clear:function(){n(),e.set(vt.none())},isSet:function(){return e.get().isSome()},get:function(){return e.get()},set:function(t){n(),e.set(vt.some(t))}}}function fc(){return lc(function(t){return t.unbind()})}function dc(){var n=lc(st);return lt(lt({},n),{on:function(t){return n.get().each(t)}})}function mc(t,n,e){return Tn(t,n,Dc,e,!1)}function gc(t,n,e){return Tn(t,n,Dc,e,!0)}function pc(o,e){function r(t){var n,e=null!==(n=t.raw.pseudoElement)&&void 0!==n?n:"";return Lt(t.target,o)&&!at(e)&&wt(Mc,t.raw.propertyName)}function t(t){var n;(g(t)||r(t))&&(a.clear(),c.clear(),!g(n=null==t?void 0:t.raw.type)&&n!==Oi()||(clearTimeout(i),cn(o,Ac),Jr(o,e.classes)))}function n(){a.set(mc(o,Oi(),t)),c.set(mc(o,ki(),t))}var i,u,a=fc(),c=fc();"ontransitionstart"in o.dom?u=mc(o,_i(),function(t){r(t)&&(u.unbind(),n())}):n();var s,l,f=(s=o,l=d("transition-delay"),R(d("transition-duration"),function(t,n,e){var o=m(l[e])+m(n);return Math.max(t,o)},0));function d(t){var n=gn(s,t);return F(y(n)?n.split(/\s*,\s*/):[],at)}function m(t){if(y(t)&&/^[\d.]+/.test(t)){var n=parseFloat(t);return Bt(t,"ms")?n:1e3*n}return 0}requestAnimationFrame(function(){i=setTimeout(t,f+17),on(o,Ac,i)})}function hc(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(u=o,a=r,i.exists(function(t){var n=u.mode;return"all"===n||t[n]!==a[n]}));function h(t){return parseFloat(t).toFixed(3)}p||(g=t,N(o.classes,function(t){return qr(g,t)}))?(fn(t,"position",e.position),c=Ma(n,t),s=Ba(n,lt(lt({},r),{rect:c})),l=U(Mc,function(t){return s[t]}),m=e,Q(l,function(t,n){var e,o,r,i=m[n].map(h),u=t.map(h);return!Et(e=i,o=u,r=void 0===r?v:r).getOr(e.isNone()&&o.isNone())}).isSome()&&(mn(t,l),p&&(Kr(f=t,(d=o).classes),un(f,Ac).each(function(t){clearTimeout(parseInt(t,10)),cn(f,Ac)}),pc(f,d)),bn(t))):Jr(t,o.classes)}function vc(t,n,e,o){vn(n,"max-height"),vn(n,"max-width");var r,s,i,l,f,d,m,g,p,u={width:On(r=n),height:wn(r)};return s=n,i=o.preference,l=t,f=u,d=e,m=o.bounds,g=f.width,p=f.height,R(i,function(t,n){var e=S(a,n);return t.fold(rt(t),e)},Ec.nofit({rect:l,maxHeight:f.height,maxWidth:f.width,direction:Ra(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:l.y},-1,-1,!1)).fold(h,h);function a(t,r,i,u,a){var c=sc(t(l,f,d,s,m),g,p,m);return c.fold(rt(c),function(t,n,e,o){return(a===o?u<e||i<n:!a&&o)?c:Ec.nofit(r,i,u,a)})}}function bc(t,n){var e=t,o=Math.floor(n);fn(e,"max-height",xe.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"])+"px")}function yc(t,n,e){return void 0===t[n]?e:t[n]}function xc(t,n,e,o){function r(t){return tt(e,t).getOr([])}function i(t,n,e){var o=W(Vc,e);return{offset:Se(t,n),classesOn:z(e,r),classesOff:z(o,r)}}var u=t*(o=void 0===o?1:o),a=n*o;return{southeast:function(){return i(-t,n,["top","alignLeft"])},southwest:function(){return i(t,n,["top","alignRight"])},south:function(){return i(-t/2,n,["top","alignCentre"])},northeast:function(){return i(-t,-n,["bottom","alignLeft"])},northwest:function(){return i(t,-n,["bottom","alignRight"])},north:function(){return i(-t/2,-n,["bottom","alignCentre"])},east:function(){return i(t,-n/2,["valignCentre","left"])},west:function(){return i(-t,-n/2,["valignCentre","right"])},insetNortheast:function(){return i(u,a,["top","alignLeft","inset"])},insetNorthwest:function(){return i(-u,a,["top","alignRight","inset"])},insetNorth:function(){return i(-u/2,a,["top","alignCentre","inset"])},insetSoutheast:function(){return i(u,-a,["bottom","alignLeft","inset"])},insetSouthwest:function(){return i(-u,-a,["bottom","alignRight","inset"])},insetSouth:function(){return i(-u/2,-a,["bottom","alignCentre","inset"])},insetEast:function(){return i(-u,-a/2,["valignCentre","right","inset"])},insetWest:function(){return i(u,-a/2,["valignCentre","left","inset"])}}}function wc(){return xc(0,0,{})}function Sc(n,e){return function(t){return"rtl"===Hc(t)?e:n}}uc.none;var Cc,kc,Oc=uc.relative,_c=uc.fixed,Tc="data-alloy-placement",Ec=Po([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Dc=_,Bc=_n,Mc=["top","bottom","right","left"],Ac="data-alloy-transition-timer",Fc=rt(function(t,n){bc(t,n),dn(t,{"overflow-x":"hidden","overflow-y":"auto"})}),Ic=rt(function(t,n){bc(t,n)}),Rc=function(t,n,e,o){var r,i,u,a,c=vc(t,n,e,o),s=n,l=c,f=Ba((r=o).origin,l);return r.transition.each(function(t){hc(s,r.origin,f,t,l,r.lastPlacement)}),Ea(s,f),a=c.placement,on(n,Tc,a),Jr(i=n,(u=c.classes).off),Kr(i,u.on),(0,o.maxHeightFunction)(n,c.maxHeight),(0,o.maxWidthFunction)(n,c.maxWidth),{layout:c.layout,placement:c.placement}},Vc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Pc=h,Hc=function(t){return"rtl"===gn(t,"direction")?"rtl":"ltr"};function zc(t){return Mu(t,function(t){return Gn(t)&&rn(t,"data-alloy-vertical-dir")===Cc.BottomToTop})}function Nc(){return _o("layouts",[fo("onLtr"),fo("onRtl"),xo("onBottomLtr"),xo("onBottomRtl")])}function Lc(n,t,e,o,r,i,u){var a=u.map(zc).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(n)}),s=t.layouts.map(function(t){return t.onRtl(n)});return Sc(a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(n)})}).or(c).getOr(r):c.getOr(e),a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(n)})}).or(s).getOr(i):s.getOr(o))(n)}function Wc(t){return t.fold(h,function(t,n,e){return t.translate(-n,-e)})}function Uc(t){return t.fold(h,h)}function jc(t){return R(t,function(t,n){return t.translate(n.left,n.top)},Se(0,0))}function Gc(t){return jc(M(t,Uc))}function Xc(t,n,e){var o,r,i=In(Ut(t.element)),u=(o=t,r=Xt(e.root).dom,vt.from(r.frameElement).map(At.fromDom).filter(function(t){return Lt(Ut(t),Ut(o.element))}).map(Cn).getOr(i));return is(u,i.left,i.top)}function Yc(t,n,e,o){var r=rs(Se(t,n));return vt.some({point:r,width:e,height:o})}function qc(t,a,c,s,l){return t.map(function(t){var n=[a,t.point],e=s.fold(function(){return Gc(n)},function(){return Gc(n)},function(){return jc(M(n,Wc))}),o={x:e.left,y:e.top,width:t.width,height:t.height},r=(c.showAbove?ma:pa)(),i=(c.showAbove?ga:ha)(),u=Lc(l,c,r,i,r,i,vt.none());return Pc({anchorBox:o,bubble:c.bubble.getOr(wc()),overrides:c.overrides,layouts:u,placer:vt.none()})})}function Kc(t,n,e){var o,r=t.document.createRange(),i=r;return n.fold(function(t){i.setStartBefore(t.dom)},function(t,n){i.setStart(t.dom,n)},function(t){i.setStartAfter(t.dom)}),o=r,e.fold(function(t){o.setEndBefore(t.dom)},function(t,n){o.setEnd(t.dom,n)},function(t){o.setEndAfter(t.dom)}),r}function Jc(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom,e),i.setEnd(o.dom,r),i}function $c(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom,width:t.width,height:t.height}}function Qc(t,n,e){return n(At.fromDom(e.startContainer),e.startOffset,At.fromDom(e.endContainer),e.endOffset)}function Zc(i,t){return r=i,o=t.match({domRange:function(t){return{ltr:rt(t),rtl:vt.none}},relative:function(t,n){return{ltr:Rt(function(){return Kc(r,t,n)}),rtl:Rt(function(){return vt.some(Kc(r,n,t))})}},exact:function(t,n,e,o){return{ltr:Rt(function(){return Jc(r,t,n,e,o)}),rtl:Rt(function(){return vt.some(Jc(r,e,o,t,n))})}}}),((e=(n=o).ltr()).collapsed?n.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return ds.rtl(At.fromDom(t.endContainer),t.endOffset,At.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return Qc(0,ds.ltr,e)}):Qc(0,ds.ltr,e)).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom,n),r.setEnd(e.dom,o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom,o),r.setEnd(t.dom,n),r}});var r,n,e,o}(Cc=Cc||{}).TopToBottom="toptobottom",Cc.BottomToTop="bottomtotop";var ts="data-alloy-vertical-dir",ns=[fo("hotspot"),xo("bubble"),To("overrides",{}),Nc(),Zu("placement",function(t,n,e){var o=Ma(e,n.hotspot.element),r=Lc(t.element,n,pa(),ha(),ma(),ga(),vt.some(n.hotspot.element));return vt.some(Pc({anchorBox:o,bubble:n.bubble.getOr(wc()),overrides:n.overrides,layouts:r,placer:vt.none()}))})],es=[fo("x"),fo("y"),To("height",0),To("width",0),To("bubble",wc()),To("overrides",{}),Nc(),Zu("placement",function(t,n,e){var o=ac(e,n.x,n.y),r=Be(o.left,o.top,n.width,n.height),i=Lc(t.element,n,fa(),da(),fa(),da(),vt.none());return vt.some(Pc({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:vt.none()}))})],os=Po([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),rs=os.screen,is=os.absolute,us=[fo("node"),fo("root"),xo("bubble"),Nc(),To("overrides",{}),To("showAbove",!1),Zu("placement",function(r,i,u){var a=Xc(r,0,i);return i.node.filter(he).bind(function(t){var n=t.dom.getBoundingClientRect(),e=Yc(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element);return qc(e,a,i,u,o)})})],as=function(t,n,e,o){return{start:t,soffset:n,finish:e,foffset:o}},cs=Po([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),ss=(cs.before,cs.on,cs.after,function(t){return t.fold(h,h,h)}),ls=Po([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),fs={domRange:ls.domRange,relative:ls.relative,exact:ls.exact,exactFromRange:function(t){return ls.exact(t.start,t.soffset,t.finish,t.foffset)},getWin:function(t){return Xt(t.match({domRange:function(t){return At.fromDom(t.startContainer)},relative:function(t,n){return ss(t)},exact:function(t,n,e,o){return t}}))},range:as},ds=Po([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function ms(t){return Zl.getOption(t)}function gs(t){return ms(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||wt(tf,Ft(t))}function ps(t,n){return Nt(e=void 0===t?document:t.dom)?[]:M(e.querySelectorAll(n),At.fromDom);var e}function hs(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return vt.some(as(At.fromDom(n.startContainer),n.startOffset,At.fromDom(e.endContainer),e.endOffset))}return vt.none()}function vs(t){if(null===t.anchorNode||null===t.focusNode)return hs(t);var n,e,o,r,i,u,a,c,s,l,f,d=At.fromDom(t.anchorNode),m=At.fromDom(t.focusNode);return n=d,e=t.anchorOffset,o=m,r=t.focusOffset,u=e,a=o,c=r,(s=Ut(i=n).dom.createRange()).setStart(i.dom,u),s.setEnd(a.dom,c),l=s,f=Lt(n,o)&&e===r,l.collapsed&&!f?vt.some(as(d,t.anchorOffset,m,t.focusOffset)):hs(t)}function bs(t,n){var e,o,r=(e=Zc(t,n)).getClientRects();return 0<(o=0<r.length?r[0]:e.getBoundingClientRect()).width||0<o.height?vt.some(o).map($c):vt.none()}function ys(t,n){return{element:t,offset:n}}function xs(t,n){return(Xn(t)?ys:function(t,n){var e=Kt(t);if(0===e.length)return ys(t,n);if(n<e.length)return ys(e[n],0);var o=e[e.length-1];return ys(o,(Xn(o)?Zl.get(o):Kt(o)).length)})(t,n)}function ws(t,n){return n.getSelection.getOrThunk(function(){return function(){return vt.from(t.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(vs)}})().map(function(t){var n=xs(t.start,t.soffset),e=xs(t.finish,t.foffset);return fs.range(n.element,n.offset,e.element,e.offset)})}function Ss(t){return t.x+t.width}function Cs(t,n){return t.x-n.width}function ks(t,n){return t.y-n.height+t.height}function Os(t,n,e){return na(Ss(t),t.y,e.southeast(),Ra(),"southeast",oa(t,{left:0,top:2}),ef)}function _s(t,n,e){return na(Cs(t,n),t.y,e.southwest(),Va(),"southwest",oa(t,{right:1,top:2}),ef)}function Ts(t,n,e){return na(Ss(t),ks(t,n),e.northeast(),Pa(),"northeast",oa(t,{left:0,bottom:3}),ef)}function Es(t,n,e){return na(Cs(t,n),ks(t,n),e.northwest(),Ha(),"northwest",oa(t,{right:1,bottom:3}),ef)}function Ds(){return[Os,_s,Ts,Es]}function Bs(){return[_s,Os,Es,Ts]}function Ms(t,n,e,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v,b,y,x,w={anchorBox:e.anchorBox,origin:n};return a=w,c=r.element,s=e.bubble,l=e.layouts,f=i,d=o,m=e.overrides,g=u,h=yc(m,"maxHeightFunction",Fc()),v=yc(m,"maxWidthFunction",st),b=a.anchorBox,y=a.origin,x={bounds:(p=y,d.fold(function(){return p.fold(Ae,Ae,Be)},function(e){return p.fold(e,e,function(){var t=e(),n=ac(p,t.x,t.y);return Be(n.left,n.top,t.width,t.height)})})),origin:y,preference:l,maxHeightFunction:h,maxWidthFunction:v,lastPlacement:f,transition:g},Rc(b,c,s,x)}function As(t,n){_e(t.element,n.element)}function Fs(n,t){var e,o=n.components();St((e=n).components(),function(t){return Te(t.element)}),An(e.element),e.syncComponents();var r=W(o,t);St(r,function(t){lf(t),n.getSystem().removeFromWorld(t)}),St(t,function(t){t.getSystem().isConnected()?As(n,t):(n.getSystem().addToWorld(t),As(n,t),he(n.element)&&ff(t)),n.syncComponents()})}function Is(t,n){df(t,n,_e)}function Rs(t){lf(t),Te(t.element),t.getSystem().removeFromWorld(t)}function Vs(n){var t=Yt(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()});Rs(n),t.each(function(t){t.syncComponents()})}function Ps(t){var n=t.components();St(n,Rs),An(t.element),t.syncComponents()}function Hs(t,n){mf(t,n,_e)}function zs(n){var t=Kt(n.element);St(t,function(t){n.getByDom(t).each(lf)}),Te(n.element)}function Ns(n,t,e,o){e.get().each(function(t){Ps(n)}),Is(t.getAttachPoint(n),n);var r=n.getSystem().build(o);return Is(n,r),e.set(r),r}function Ls(t,n,e,o){var r=Ns(t,n,e,o);return n.onOpen(t,r),r}function Ws(n,e,o){o.get().each(function(t){Ps(n),Vs(n),e.onClose(n,t),o.clear()})}function Us(t,n,e){return e.isOpen()}function js(t){var e=co("Dismissal",xf,t),n={};return n[vf()]={schema:Ze([fo("target")]),onReceive:function(n,t){hf.isOpen(n)&&(hf.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return hf.close(n)},function(t){return hr(n,t.event)}))}},n}function Gs(t){var e=co("Reposition",wf,t),n={};return n[bf()]={onReceive:function(n){hf.isOpen(n)&&e.fireEventInstead.fold(function(){return e.doReposition(n)},function(t){return hr(n,t.event)})}},n}function Xs(t,n,e){n.store.manager.onLoad(t,n,e)}function Ys(t,n,e){n.store.manager.onUnload(t,n,e)}function qs(){var t=Vo(null);return xu({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})}function Ks(){var i=Vo({}),u=Vo({});return xu({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return tt(i.get(),t).orThunk(function(){return tt(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};St(t,function(n){tt(o[n.value]=n,"meta").each(function(t){tt(t,"text").each(function(t){r[t]=n})})}),i.set(lt(lt({},n),o)),u.set(lt(lt({},e),r))},clear:function(){i.set({}),u.set({})}})}function Js(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)}function $s(o,t){return Ro(o,{},M(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,ur(n,n,Ge(),Ke(function(t){return No("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([ar("dump",h)]))}function Qs(t){return t.dump}function Zs(t,n){return lt(lt({},tc(n)),t.dump)}function tl(t){return Tt(t,"uiType")}function nl(t){return t.fold(vt.some,vt.none,vt.some,vt.some)}function el(t){function n(t){return t.name}return t.fold(n,n,n,n)}function ol(e,o){return function(t){var n=co("Converting part type",o,t);return e(n)}}function rl(t,n,e,o){return Xo(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))}function il(r,t){var n={};return St(t,function(t){nl(t).each(function(e){var o=td(r,e.pname);n[e.name]=function(t){var n=co("Part: "+e.name+" in "+r,Jo(e.schema),t);return lt(lt({},o),{config:t,validated:n})}})}),n}function ul(t,n,e){return{uiType:Rf(),owner:t,name:n,config:e,validated:{}}}function al(t){return z(t,function(t){return t.fold(vt.none,vt.some,vt.none,vt.none).map(function(t){return vo(t.name,t.schema.concat([ta(Qf())]))}).toArray()})}function cl(t){return M(t,el)}function sl(t,n,e){return o=n,r={},i={},St(e,function(t){t.fold(function(o){r[o.pname]=Ff(!0,function(t,n,e){return o.factory.sketch(rl(t,o,n,e))})},function(t){var n=o.parts[t.name];i[t.name]=rt(t.factory.sketch(rl(o,t,n[Qf()]),n))},function(o){r[o.pname]=Ff(!1,function(t,n,e){return o.factory.sketch(rl(t,o,n,e))})},function(o){r[o.pname]=If(!0,function(n,t,e){return M(n[o.name],function(t){return o.factory.sketch(Xo(o.defaults(n,t,e),t,o.overrides(n,t)))})})})}),{internals:rt(r),externals:rt(i)};var o,r,i}function ll(t,n,e){return o=vt.some(t),i=(r=n).components,s=dt(e,function(t,n){return o=t,r=!1,{name:rt(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),u=o,a=r,c=s,l=z(i,function(t){return Af(u,a,t,c)}),J(s,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+o.getOr("none")+"\nComponents: "+JSON.stringify(r.components,null,2))}),l;var o,r,i,u,a,c,s,l}function fl(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOptional()}function dl(t,n,e){return fl(t,n,e).getOrDie("Could not find part: "+e)}function ml(t,n,e){var o={},r=n.partUids,i=t.getSystem();return St(e,function(t){o[t]=rt(i.getByUid(r[t]))}),o}function gl(t,n){var e=t.getSystem();return dt(n.partUids,function(t,n){return rt(e.getByUid(t))})}function pl(t){return kt(t.partUids)}function hl(t,n,e){var o={},r=n.partUids,i=t.getSystem();return St(e,function(t){o[t]=rt(i.getByUid(r[t]).getOrDie())}),o}function vl(n,t){return sr(M(cl(t),function(t){return{key:t,value:n+"-"+t}}))}function bl(n){return ur("partUids","partUids",Xe(function(t){return vl(t.uid,n)}),Qo())}function yl(t,n,e,o,r){var i;return co(t+" [SpecSchema]",Ze((i=r,(0<o.length?[vo("parts",o)]:[]).concat([fo("uid"),To("dom",{}),To("components",[]),ta("originalSpec"),To("debug.sketcher",{})]).concat(i)).concat(n)),e)}function xl(t,n,e,o,r){var i=ed(r),u=yl(t,n,i,al(e),[bl(e)]),a=sl(0,u,e);return o(u,ll(t,u,a.internals()),i,a.externals())}function wl(t){var r=co("Sketcher for "+t.name,od,t),n=dt(r.apis,Hr),e=dt(r.extraApis,Vr);return lt(lt({name:r.name,configFields:r.configFields,sketch:function(t){return n=r.name,e=r.configFields,(0,r.factory)(yl(n,e,o=ed(t),[],[]),o);var n,e,o}},n),e)}function Sl(t){var n=co("Sketcher for "+t.name,rd,t),e=il(n.name,n.partFields),o=dt(n.apis,Hr),r=dt(n.extraApis,Vr);return lt(lt({name:n.name,partFields:n.partFields,configFields:n.configFields,sketch:function(t){return xl(n.name,n.configFields,n.partFields,n.factory,t)},parts:e},o),r)}function Cl(t){return"input"===Ft(t)&&"radio"!==rn(t,"type")||"textarea"===Ft(t)}function kl(t,n,e){(n.disabled()?cd:sd)(t,n)}function Ol(t,n){return!0===n.useNative&&wt(ad,Ft(t.element))}function _l(t,n){return Ol(t,n)?an(t.element,"disabled"):"true"===rn(t.element,"aria-disabled")}function Tl(e,o,t,r){var n=ps(e.element,"."+o.highlightClass);St(n,function(n){T(r,function(t){return t.element===n})||(Yr(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),hr(t,Zi())}))})}function El(t,n,e,o){Tl(t,n,0,[o]),Gl(0,n,0,o)||(Xr(o.element,n.highlightClass),n.onHighlight(t,o),hr(o,Qi()))}function Dl(e,n,t,o){var r=ps(e.element,"."+n.itemClass);return P(r,function(t){return qr(t,n.highlightClass)}).bind(function(t){var n=ea(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOptional()})}function Bl(t,n,e){var o=L(t.slice(0,n)),r=L(t.slice(n+1));return V(o.concat(r),e)}function Ml(t,n,e){return V(L(t.slice(0,n)),e)}function Al(t,n,e){var o=t.slice(0,n);return V(t.slice(n+1).concat(o),e)}function Fl(t,n,e){return V(t.slice(n+1),e)}function Il(e){return function(t){var n=t.raw;return wt(e,n.which)}}function Rl(t){return function(n){return N(t,function(t){return t(n)})}}function Vl(t){return!0===t.raw.shiftKey}function Pl(t){return!0===t.raw.ctrlKey}function Hl(t,n){return{matches:t,classification:n}}function zl(t,n,e){n.exists(function(n){return e.exists(function(t){return Lt(t,n)})})||vr(t,qi(),{prevFocus:n,newFocus:e})}function Nl(){function o(t){return Oa(t.element)}return{get:o,set:function(t,n){var e=o(t);t.getSystem().triggerFocus(n,t.element),zl(t,e,o(t))}}}function Ll(){function r(t){return gd.getHighlighted(t).map(function(t){return t.element})}return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold(st,function(t){gd.highlight(n,t)});var o=r(n);zl(n,e,o)}}}ds.ltr,ds.rtl;function Wl(t,n,e,o,r,i){var u=i.map(Me);return cf(t,n,e,o,r,u)}function Ul(t,n,e){var o,r,i,u=n.getAttachPoint(t);fn(t.element,"position",sf.getMode(u)),i=n.cloakVisibilityAttr,pn((o=t).element,r="visibility").fold(function(){cn(o.element,i)},function(t){on(o.element,i,t)}),fn(o.element,r,"hidden")}function jl(t,n,e){var o,r,i,u=t.element;T(["top","left","right","bottom"],function(t){return pn(u,t).isSome()})||vn(t.element,"position"),r="visibility",i=n.cloakVisibilityAttr,un((o=t).element,i).fold(function(){return vn(o.element,r)},function(t){return fn(o.element,r,t)})}function Gl(t,n,e,o){return qr(o.element,n.highlightClass)}function Xl(n,t,e){return Fu(n.element,"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}function Yl(n,t,e){var o=ps(n.element,"."+t.itemClass);return(0<o.length?vt.some(o[o.length-1]):vt.none()).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}function ql(n,t,e){return et(M(ps(n.element,"."+t.itemClass),function(t){return n.getSystem().getByDom(t).toOptional()}))}var Kl,Jl,$l,Ql,Zl=(Kl=Xn,{get:function(t){if(!Kl(t))throw new Error("Can only get text value of a text node");return Jl(t).getOr("")},getOption:Jl=function(t){return Kl(t)?vt.from(t.dom.nodeValue):vt.none()},set:function(t,n){if(!Kl(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}),tf=["img","br"],nf=[xo("getSelection"),fo("root"),xo("bubble"),Nc(),To("overrides",{}),To("showAbove",!1),Zu("placement",function(t,n,e){var r=Xt(n.root).dom,o=Xc(t,0,n);return qc(ws(r,n).bind(function(e){return t=r,n=fs.exactFromRange(e),(0<(o=Zc(t,n).getBoundingClientRect()).width||0<o.height?vt.some(o).map($c):vt.none()).orThunk(function(){var t=At.fromText("\ufeff");En(e.start,t);var n=bs(r,fs.exact(t,0,t,1));return Te(t),n}).bind(function(t){return Yc(t.left,t.top,t.width,t.height)});var t,n,o}),o,n,e,ws(r,n).bind(function(t){return Gn(t.start)?vt.some(t.start):le(t.start)}).getOr(t.element))})],ef="link-layout",of=so("type",{selection:nf,node:us,hotspot:ns,submenu:[fo("item"),Nc(),To("overrides",{}),Zu("placement",function(t,n,e){var o=Ma(e,n.item.element),r=Lc(t.element,n,Ds(),Bs(),Ds(),Bs(),vt.none());return vt.some(Pc({anchorBox:o,bubble:wc(),overrides:n.overrides,layouts:r,placer:vt.none()}))})],makeshift:es}),rf=[yo("classes",tr),Mo("mode","all",["all","layout","placement"])],uf=[To("useFixed",O),xo("getBounds")],af=[mo("anchor",of),_o("transition",rf)],cf=function(c,s,l,f,t,d){var m=co("placement.info",Jo(af),t),g=m.anchor,p=f.element,h=l.get(f.uid);_a(function(){fn(p,"position","fixed");var t=pn(p,"visibility");fn(p,"visibility","hidden");var n,e,o,r,i=s.useFixed()?(r=document.documentElement,_c(0,0,r.clientWidth,r.clientHeight)):(e=Cn((n=c).element),o=n.element.dom.getBoundingClientRect(),Oc(e.left,e.top,o.width,o.height)),u=g.placement,a=d.map(rt).or(s.getBounds);u(c,g,i).each(function(t){var n=t.placer.getOr(Ms)(c,i,t,a,f,h,m.transition);l.set(f.uid,n)}),t.fold(function(){vn(p,"visibility")},function(t){fn(p,"visibility",t)}),pn(p,"left").isNone()&&pn(p,"top").isNone()&&pn(p,"right").isNone()&&pn(p,"bottom").isNone()&&mt(pn(p,"position"),"fixed")&&vn(p,"position")},p)},sf=ya({fields:uf,name:"positioning",active:ic,apis:Object.freeze({__proto__:null,position:function(t,n,e,o,r){Wl(t,n,e,o,r,vt.none())},positionWithin:Wl,positionWithinBounds:cf,getMode:function(t,n,e){return n.useFixed()?"fixed":"absolute"},reset:function(t,n,e,o){var r=o.element;St(["position","left","right","top","bottom"],function(t){return vn(r,t)}),cn(r,Tc),e.clear(o.uid)}}),state:Object.freeze({__proto__:null,init:function(){var e={};return xu({readState:function(){return e},clear:function(t){d(t)?delete e[t]:e={}},set:function(t,n){e[t]=n},get:function(t){return tt(e,t)}})}})}),lf=function(t){hr(t,Gi());var n=t.components();St(n,lf)},ff=function(t){var n=t.components();St(n,ff),hr(t,ji())},df=function(t,n,e){t.getSystem().addToWorld(n),e(t.element,n.element),he(t.element)&&ff(n),t.syncComponents()},mf=function(t,n,e){e(t,n.element);var o=Kt(n.element);St(o,function(t){n.getByDom(t).each(ff)})},gf=Object.freeze({__proto__:null,cloak:Ul,decloak:jl,open:Ls,openWhileCloaked:function(t,n,e,o,r){Ul(t,n),Ls(t,n,e,o),r(),jl(t,n)},close:Ws,isOpen:Us,isPartOf:function(n,e,t,o){return Us(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()},setContent:function(t,n,e,o){return e.get().map(function(){return Ns(t,n,e,o)})}}),pf=Object.freeze({__proto__:null,events:function(e,o){return nu([Cr(Pi(),function(t,n){Ws(t,e,o)})])}}),hf=ya({fields:[Ku("onOpen"),Ku("onClose"),fo("isPartOf"),fo("getAttachPoint"),To("cloakVisibilityAttr","data-precloak-visibility")],name:"sandboxing",active:pf,apis:gf,state:Object.freeze({__proto__:null,init:function(){var t=dc(),n=rt("not-implemented");return xu({readState:n,isOpen:t.isSet,clear:t.clear,set:t.set,get:t.get})}})}),vf=rt("dismiss.popups"),bf=rt("reposition.popups"),yf=rt("mouse.released"),xf=Ze([To("isExtraPart",O),_o("fireEventInstead",[To("event",Xi())])]),wf=Ze([_o("fireEventInstead",[To("event",Yi())]),ho("doReposition")]),Sf=Object.freeze({__proto__:null,onLoad:Xs,onUnload:Ys,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),Cf=Object.freeze({__proto__:null,events:function(e,o){var t=e.resetOnDom?[eu(function(t,n){Xs(t,e,o)}),ou(function(t,n){Ys(t,e,o)})]:[va(e,o,Xs)];return nu(t)}}),kf=Object.freeze({__proto__:null,memory:qs,dataset:Ks,manual:function(){return xu({readState:st})},init:function(t){return t.store.manager.state(t)}}),Of=[xo("initialValue"),fo("getFallbackEntry"),fo("getDataKey"),fo("setValue"),Zu("manager",{setValue:Js,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).getOrThunk(function(){return o.getFallbackEntry(r)})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){Js(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:Ks})],_f=[fo("getValue"),To("setValue",st),xo("initialValue"),Zu("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:st,state:yu.init})],Tf=ya({fields:[Eo("store",{mode:"memory"},so("mode",{memory:[xo("initialValue"),Zu("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:qs})],manual:_f,dataset:Of})),Ku("onSetValue"),To("resetOnDom",!1)],name:"representing",active:Cf,apis:Sf,extra:{setValueFrom:function(t,n){var e=Tf.getValue(n);Tf.setValue(t,e)}},state:kf}),Ef=$s,Df=Zs,Bf="placeholder",Mf=Po([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Af=function(r,i,u,a){return t=r,e=a,(tl(n=u)&&n.uiType===Bf?(c=n,s=e,(o=t).exists(function(t){return t!==c.owner})?Mf.single(!0,rt(c)):tt(s,c.name).fold(function(){throw new Error("Unknown placeholder component: "+c.name+"\nKnown: ["+kt(s)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(c,null,2))},function(t){return t.replace()})):Mf.single(!1,rt(n))).fold(function(t,n){var e=tl(u)?n(i,u.config,u.validated):n(i),o=z(tt(e,"components").getOr([]),function(t){return Af(r,i,t,a)});return[lt(lt({},e),{components:o})]},function(t,n){if(tl(u)){var e=n(i,u.config,u.validated);return u.validated.preprocess.getOr(h)(e)}return n(i)});var t,n,e,o,c,s},Ff=Mf.single,If=Mf.multiple,Rf=rt(Bf),Vf=Po([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Pf=To("factory",{sketch:h}),Hf=To("schema",[]),zf=fo("name"),Nf=ur("pname","pname",Ue(function(t){return"<alloy."+Fr(t.name)+">"}),Qo()),Lf=ar("schema",function(){return[xo("preprocess")]}),Wf=To("defaults",rt({})),Uf=To("overrides",rt({})),jf=Jo([Pf,Hf,zf,Nf,Wf,Uf]),Gf=Jo([Pf,Hf,zf,Wf,Uf]),Xf=Jo([Pf,Hf,zf,Nf,Wf,Uf]),Yf=Jo([Pf,Lf,zf,fo("unit"),Nf,Wf,Uf]),qf=ol(Vf.required,jf),Kf=ol(Vf.external,Gf),Jf=ol(Vf.optional,Xf),$f=ol(Vf.group,Yf),Qf=rt("entirety"),Zf=Object.freeze({__proto__:null,required:qf,external:Kf,optional:Jf,group:$f,asNamedPart:nl,name:el,asCommon:function(t){return t.fold(h,h,h,h)},original:Qf}),td=function(t,n){return{uiType:Rf(),owner:t,name:n}},nd=Object.freeze({__proto__:null,generate:il,generateOne:ul,schemas:al,names:cl,substitutes:sl,components:ll,defaultUids:vl,defaultUidsSchema:bl,getAllParts:gl,getAllPartNames:pl,getPart:fl,getPartOrDie:dl,getParts:ml,getPartsOrDie:hl}),ed=function(t){return Tt(t,"uid")?t:lt(lt({},t),{uid:gu("uid")})},od=Ze([fo("name"),fo("factory"),fo("configFields"),To("apis",{}),To("extraApis",{})]),rd=Ze([fo("name"),fo("factory"),fo("configFields"),fo("partFields"),To("apis",{}),To("extraApis",{})]),id=Object.freeze({__proto__:null,getCurrent:function(t,n,e){return n.find(t)}}),ud=ya({fields:[fo("find")],name:"composing",apis:id}),ad=["input","button","textarea","select"],cd=function(n,t,e){t.disableClass.each(function(t){Xr(n.element,t)}),(Ol(n,t)?function(t){on(t.element,"disabled","disabled")}:function(t){on(t.element,"aria-disabled","true")})(n),t.onDisabled(n)},sd=function(n,t,e){t.disableClass.each(function(t){Yr(n.element,t)}),(Ol(n,t)?function(t){cn(t.element,"disabled")}:function(t){on(t.element,"aria-disabled","false")})(n),t.onEnabled(n)},ld=Object.freeze({__proto__:null,enable:sd,disable:cd,isDisabled:_l,onLoad:kl,set:function(t,n,e,o){(o?cd:sd)(t,n)}}),fd=Object.freeze({__proto__:null,exhibit:function(t,n){return Nr({classes:n.disabled()?n.disableClass.toArray():[]})},events:function(e,t){return nu([wr(Fi(),function(t,n){return _l(t,e)}),va(e,t,kl)])}}),dd=ya({fields:[Fo("disabled",O),To("useNative",!0),xo("disableClass"),Ku("onDisabled"),Ku("onEnabled")],name:"disabling",active:fd,apis:ld}),md=Object.freeze({__proto__:null,dehighlightAll:function(t,n,e){return Tl(t,n,0,[])},dehighlight:function(t,n,e,o){Gl(0,n,0,o)&&(Yr(o.element,n.highlightClass),n.onDehighlight(t,o),hr(o,Zi()))},highlight:El,highlightFirst:function(n,e,t){Xl(n,e).each(function(t){El(n,e,0,t)})},highlightLast:function(n,e,t){Yl(n,e).each(function(t){El(n,e,0,t)})},highlightAt:function(n,e,t,o){var r,i,u;i=o,u=ps((r=n).element,"."+e.itemClass),vt.from(u[i]).fold(function(){return Re.error(new Error("No element found with index "+i))},r.getSystem().getByDom).fold(function(t){throw t},function(t){El(n,e,0,t)})},highlightBy:function(n,e,t,o){V(ql(n,e),o).each(function(t){El(n,e,0,t)})},isHighlighted:Gl,getHighlighted:function(n,t,e){return Fu(n.element,"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},getFirst:Xl,getLast:Yl,getPrevious:function(t,n,e){return Dl(t,n,0,-1)},getNext:function(t,n,e){return Dl(t,n,0,1)},getCandidates:ql}),gd=ya({fields:[fo("highlightClass"),fo("itemClass"),Ku("onHighlight"),Ku("onDehighlight")],name:"highlighting",apis:md}),pd=[8],hd=[9],vd=[13],bd=[27],yd=[32],xd=[37],wd=[38],Sd=[39],Cd=[40],kd=C(Vl);function Od(t,n,e,o,a){function c(n,e,t,o,r){var i=t(n,e,o,r),u=e.event;return V(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(n,e,o,r)})}var r={schema:function(){return t.concat([To("focusManager",Nl()),Eo("focusInside","onFocus",ro(function(t){return wt(["onFocus","onEnterOrSpace","onApi"],t)?Re.value(t):Re.error("Invalid value for focusInside")})),Zu("handler",r),Zu("state",n),Zu("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==$l.OnFocusMode?vt.none():a(i).map(function(e){return Cr(Di(),function(t,n){e(t,i,u),n.stop()})}),n=[Cr(yi(),function(o,r){c(o,r,e,i,u).fold(function(){var n=o,e=r,t=Il(yd.concat(vd))(e.event);i.focusInside===$l.OnEnterOrSpaceMode&&t&&mr(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),Cr(xi(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})];return nu(t.toArray().concat(n))}};return r}function _d(t){function a(t,n){return 0<xn(t.visibilitySelector.bind(function(t){return Iu(n,t)}).getOr(n))}function n(n,e,t){var o=e,r=F(ps(n.element,o.selector),function(t){return a(o,t)});vt.from(r[o.firstTabstop]).each(function(t){e.focusManager.set(n,t)})}function o(e,t,r,i){var n,u=ps(e.element,r.selector);return(n=r).focusManager.get(e).bind(function(t){return Iu(t,n.selector)}).bind(function(t){return P(u,S(Lt,t)).bind(function(t){return n=e,o=r,i(u,t,function(t){return a(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?vt.some(!0):vt.none()},function(t){return o.focusManager.set(n,t),vt.some(!0)});var n,o})})}var e=[xo("onEscape"),xo("onEnter"),To("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),To("firstTabstop",0),To("useTabstopAt",_),xo("visibilitySelector")].concat([t]),r=rt([Hl(Rl([Vl,Il(hd)]),function(t,n,e){return o(t,0,e,e.cyclic?Bl:Ml)}),Hl(Il(hd),function(t,n,e){return o(t,0,e,e.cyclic?Al:Fl)}),Hl(Il(bd),function(n,e,t){return t.onEscape.bind(function(t){return t(n,e)})}),Hl(Rl([kd,Il(vd)]),function(n,e,t){return t.onEnter.bind(function(t){return t(n,e)})})]),i=rt([]);return Od(e,yu.init,r,i,function(){return vt.some(n)})}function Td(t,n,e){return Cl(e)&&Il(yd)(n.event)?vt.none():(yr(t,e,Fi()),vt.some(!0))}function Ed(t,n){return vt.some(!0)}function Dd(t,n,e){return e.execute(t,n,t.element)}function Bd(){var e=dc();return xu({readState:function(){return e.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set({numRows:t,numColumns:n})},getNumRows:function(){return e.get().map(function(t){return t.numRows})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns})}})}function Md(i){return function(t,n,e,o){var r=i(t.element);return Qm(r,t,n,e,o)}}function Ad(t,n){return Md(Sc(t,n))}function Fd(t,n){return Md(Sc(n,t))}function Id(r){return function(t,n,e,o){return Qm(r,t,n,e,o)}}function Rd(t){return!((n=t.dom).offsetWidth<=0&&n.offsetHeight<=0);var n}function Vd(t,n,e){var o,r=F(ps(t,e),Rd);return P(o=r,function(t){return Lt(t,n)}).map(function(t){return{index:t,candidates:o}})}function Pd(t,n){return P(t,function(t){return Lt(n,t)})}function Hd(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row*o+t.column;return 0<=n&&n<e.length?vt.some(e[n]):vt.none()})}function zd(r,t,i,u,a){return Hd(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=ea(n,a,0,e-1);return vt.some({row:t,column:o})})}function Nd(i,t,u,a,c){return Hd(i,t,a,function(t,n){var e=ea(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=Ua(n,0,o-1);return vt.some({row:e,column:r})})}function Ld(n,e,t){Fu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})}function Wd(r){return function(t,n,e,o){return Vd(t,n,e.selector).bind(function(t){return r(t.candidates,t.index,o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Ud(t,n,e){return e.captureTab?vt.some(!0):vt.none()}function jd(t,n,e,i){var u=function(t,n,e){var o,r=ea(n,i,0,e.length-1);return r===t?vt.none():"button"===Ft(o=e[r])&&"disabled"===rn(o,"disabled")?u(t,r,e):vt.from(e[r])};return Vd(t,e,n).bind(function(t){var n=t.index,e=t.candidates;return u(n,n,e)})}function Gd(n,e,o){return(r=o).focusManager.get(n).bind(function(t){return Iu(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var r}function Xd(n,e,t){e.getInitial(n).orThunk(function(){return Fu(n.element,e.selector)}).each(function(t){e.focusManager.set(n,t)})}function Yd(t,n,e){return jd(t,e.selector,n,-1)}function qd(t,n,e){return jd(t,e.selector,n,1)}function Kd(r){return function(t,n,e,o){return r(t,n,e,o).bind(function(){return e.executeOnMove?Gd(t,n,e):vt.some(!0)})}}function Jd(t,n,e){return e.onEscape(t,n)}function $d(t,n,e){return vt.from(t[n]).bind(function(t){return vt.from(t[e]).map(function(t){return{rowIndex:n,columnIndex:e,cell:t}})})}function Qd(t,n,e,o){return $d(t,n,ea(e,o,0,t[n].length-1))}function Zd(t,n,e,o){var r=ea(e,o,0,t.length-1),i=t[r].length;return $d(t,r,Ua(n,0,i-1))}function tm(t,n,e,o){var r=t[n].length;return $d(t,n,Ua(e+o,0,r-1))}function nm(t,n,e,o){var r=Ua(e+o,0,t.length-1),i=t[r].length;return $d(t,r,Ua(n,0,i-1))}function em(n,e,t){e.previousSelector(n).orThunk(function(){var t=e.selectors;return Fu(n.element,t.cell)}).each(function(t){e.focusManager.set(n,t)})}function om(t,o){return function(n,e,i){var u=i.cycles?t:o;return Iu(e,i.selectors.row).bind(function(t){return Pd(ps(t,i.selectors.cell),e).bind(function(o){var r=ps(n,i.selectors.row);return Pd(r,t).bind(function(t){var n,e=(n=i,M(r,function(t){return ps(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell})})})})}}function rm(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})}function im(n,e,t){Fu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})}function um(t,n,e){return jd(t,e.selector,n,-1)}function am(t,n,e){return jd(t,e.selector,n,1)}function cm(t,n,e,o){var r=t.getSystem().build(o);df(t,r,e)}function sm(t,n,e,o){V(Ag(t),function(t){return Lt(o.element,t.element)}).each(Vs)}function lm(n,t,e,r,o){var i=Ag(n);return vt.from(i[r]).map(function(t){return sm(n,0,0,t),o.each(function(t){cm(n,0,function(t,n){var e,o=n;Jt(e=t,r).fold(function(){_e(e,o)},function(t){En(t,o)})},t)}),t})}function fm(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=nu(n),ya({fields:[fo("enabled")],name:e,active:{events:rt(o)}})),configAsRaw:rt({}),initialConfig:{},state:yu}}}function dm(t,n){n.ignore||(Sa(t.element),n.onFocus(t))}function mm(t,n,e){var o=n.aria;o.update(t,o,e.get())}function gm(n,t,e){t.toggleClass.each(function(t){(e.get()?Xr:Yr)(n.element,t)})}function pm(t,n,e){Gm(t,n,e,!e.get())}function hm(t,n,e){e.set(!0),gm(t,n,e),mm(t,n,e)}function vm(t,n,e){e.set(!1),gm(t,n,e),mm(t,n,e)}function bm(t,n,e){Gm(t,n,e,n.selected)}function ym(){function t(t,n){n.stop(),br(t)}return[Cr(Ci(),t),Cr(Ri(),t),Er(ci()),Er(di())]}function xm(t){return nu(ft([t.map(function(e){return iu(function(t,n){e(t),n.stop()})}).toArray(),ym()]))}function wm(t){(Oa(t.element).isNone()||Vg.isFocused(t))&&(Vg.isFocused(t)||Vg.focus(t),vr(t,Ng,{item:t}))}function Sm(t){vr(t,Lg,{item:t})}function Cm(t,n){return t.x+t.width/2-n.width/2}function km(t,n){return t.x+t.width-n.width}function Om(t,n){return t.y+t.height-n.height}function _m(t,n){return t.y+t.height/2-n.height/2}function Tm(t,n,e){return na(km(t,n),Om(t,n),e.insetSouthwest(),Ha(),"southwest",oa(t,{right:0,bottom:3}),ip)}function Em(t,n,e){return na(t.x,Om(t,n),e.insetSoutheast(),Pa(),"southeast",oa(t,{left:1,bottom:3}),ip)}function Dm(t,n,e){return na(km(t,n),t.y,e.insetNorthwest(),Va(),"northwest",oa(t,{right:0,top:2}),ip)}function Bm(t,n,e){return na(t.x,t.y,e.insetNortheast(),Ra(),"northeast",oa(t,{left:1,top:2}),ip)}function Mm(t,n,e){return na(km(t,n),_m(t,n),e.insetEast(),Wa(),"east",oa(t,{right:0}),ip)}function Am(t,n,e){return na(t.x,_m(t,n),e.insetWest(),La(),"west",oa(t,{left:1}),ip)}function Fm(t){switch(t){case"north":return up;case"northeast":return Bm;case"northwest":return Dm;case"south":return ap;case"southeast":return Em;case"southwest":return Tm;case"east":return Mm;case"west":return Am}}function Im(t,n,e,o,r){return cc(o).map(Fm).getOr(up)(t,n,e,o,r)}function Rm(t){switch(t){case"north":return ap;case"northeast":return Em;case"northwest":return Tm;case"south":return up;case"southeast":return Bm;case"southwest":return Dm;case"east":return Am;case"west":return Mm}}function Vm(t,n,e,o,r){return cc(o).map(Rm).getOr(up)(t,n,e,o,r)}function Pm(t){var n=void 0!==t.uid&&nt(t,"uid")?t.uid:gu("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).toOptional()},asSpec:function(){return lt(lt({},t),{uid:n})}}}function Hm(t){return function(){return tt(t,dp).getOr("!not found!")}}function zm(t,n){var e,o=t.toLowerCase();if(lp.isRtl()){var r=Bt(e=o,"-rtl")?e:e+"-rtl";return Tt(n,r)?r:o}return o}function Nm(t,n){return tt(n,zm(t,n))}function Lm(t,n){var e=n();return Nm(t,e).getOrThunk(Hm(e))}function Wm(){return fm("add-focusable",[eu(function(t){var n,e,o;n=t.element,e="svg",o=function(t){return zt(t,e)},V(n.dom.childNodes,function(t){return o(At.fromDom(t))}).map(At.fromDom).each(function(t){return on(t,"focusable","false")})})])}function Um(t,n,e,o){var r,i,u,a=(u=n,lp.isRtl()&&Tt(fp,u)?["tox-icon--flip"]:[]),c=tt(e,zm(n,e)).or(o).getOrThunk(Hm(e));return{dom:{tag:t.tag,attributes:null!==(r=t.attributes)&&void 0!==r?r:{},classes:t.classes.concat(a),innerHtml:c},behaviours:tc(H(H([],null!==(i=t.behaviours)&&void 0!==i?i:[],!0),[Wm()],!1))}}function jm(t,n,e,o){return void 0===o&&(o=vt.none()),Um(n,t,e(),o)}(Ql=$l=$l||{}).OnFocusMode="onFocus",Ql.OnEnterOrSpaceMode="onEnterOrSpace",Ql.OnApiMode="onApi";function Gm(t,n,e,o){(o?hm:vm)(t,n,e)}function Xm(t,n,e){on(t.element,"aria-expanded",e)}function Ym(t){return"prepared"===t.type?vt.some(t.menu):vt.none()}var qm=_d(ar("cyclic",O)),Km=_d(ar("cyclic",_)),Jm=Od([To("execute",Td),To("useSpace",!1),To("useEnter",!0),To("useControlEnter",!1),To("useDown",!1)],yu.init,function(t,n,e,o){var r=e.useSpace&&!Cl(t.element)?yd:[],i=e.useEnter?vd:[],u=e.useDown?Cd:[];return[Hl(Il(r.concat(i).concat(u)),Dd)].concat(e.useControlEnter?[Hl(Rl([Pl,Il(vd)]),Dd)]:[])},function(t,n,e,o){return e.useSpace&&!Cl(t.element)?[Hl(Il(yd),Ed)]:[]},function(){return vt.none()}),$m=Object.freeze({__proto__:null,flatgrid:Bd,init:function(t){return t.state(t)}}),Qm=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element,t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},Zm=Id,tg=Id,ng=Id,eg=Wd(function(t,n,e,o){return zd(t,n,e,o,-1)}),og=Wd(function(t,n,e,o){return zd(t,n,e,o,1)}),rg=Wd(function(t,n,e,o){return Nd(t,n,e,o,-1)}),ig=Wd(function(t,n,e,o){return Nd(t,n,e,o,1)}),ug=Od([fo("selector"),To("execute",Td),Ju("onEscape"),To("captureTab",!1),Fa()],Bd,rt([Hl(Il(xd),Ad(eg,og)),Hl(Il(Sd),Fd(eg,og)),Hl(Il(wd),Zm(rg)),Hl(Il(Cd),tg(ig)),Hl(Rl([Vl,Il(hd)]),Ud),Hl(Rl([kd,Il(hd)]),Ud),Hl(Il(bd),function(t,n,e){return e.onEscape(t,n)}),Hl(Il(yd.concat(vd)),function(n,e,o,t){return(r=o).focusManager.get(n).bind(function(t){return Iu(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var r})]),rt([Hl(Il(yd),Ed)]),function(){return vt.some(Ld)}),ag=[fo("selector"),To("getInitial",vt.none),To("execute",Td),Ju("onEscape"),To("executeOnMove",!1),To("allowVertical",!0)],cg=rt([Hl(Il(yd),Ed)]),sg=Od(ag,yu.init,function(t,n,e,o){var r=xd.concat(e.allowVertical?wd:[]),i=Sd.concat(e.allowVertical?Cd:[]);return[Hl(Il(r),Kd(Ad(Yd,qd))),Hl(Il(i),Kd(Fd(Yd,qd))),Hl(Il(vd),Gd),Hl(Il(yd),Gd),Hl(Il(bd),Jd)]},cg,function(){return vt.some(Xd)}),lg=[vo("selectors",[fo("row"),fo("cell")]),To("cycles",!0),To("previousSelector",vt.none),To("execute",Td)],fg=om(function(t,n,e){return Qd(t,n,e,-1)},function(t,n,e){return tm(t,n,e,-1)}),dg=om(function(t,n,e){return Qd(t,n,e,1)},function(t,n,e){return tm(t,n,e,1)}),mg=om(function(t,n,e){return Zd(t,e,n,-1)},function(t,n,e){return nm(t,e,n,-1)}),gg=om(function(t,n,e){return Zd(t,e,n,1)},function(t,n,e){return nm(t,e,n,1)}),pg=rt([Hl(Il(xd),Ad(fg,dg)),Hl(Il(Sd),Fd(fg,dg)),Hl(Il(wd),Zm(mg)),Hl(Il(Cd),tg(gg)),Hl(Il(yd.concat(vd)),function(n,e,o){return Oa(n.element).bind(function(t){return o.execute(n,e,t)})})]),hg=rt([Hl(Il(yd),Ed)]),vg=Od(lg,yu.init,pg,hg,function(){return vt.some(em)}),bg=[fo("selector"),To("execute",Td),To("moveOnTab",!1)],yg=rt([Hl(Il(wd),ng(um)),Hl(Il(Cd),ng(am)),Hl(Rl([Vl,Il(hd)]),function(t,n,e,o){return e.moveOnTab?ng(um)(t,n,e,o):vt.none()}),Hl(Rl([kd,Il(hd)]),function(t,n,e,o){return e.moveOnTab?ng(am)(t,n,e,o):vt.none()}),Hl(Il(vd),rm),Hl(Il(yd),rm)]),xg=rt([Hl(Il(yd),Ed)]),wg=Od(bg,yu.init,yg,xg,function(){return vt.some(im)}),Sg=Od([Ju("onSpace"),Ju("onEnter"),Ju("onShiftEnter"),Ju("onLeft"),Ju("onRight"),Ju("onTab"),Ju("onShiftTab"),Ju("onUp"),Ju("onDown"),Ju("onEscape"),To("stopSpaceKeyup",!1),xo("focusIn")],yu.init,function(t,n,e){return[Hl(Il(yd),e.onSpace),Hl(Rl([kd,Il(vd)]),e.onEnter),Hl(Rl([Vl,Il(vd)]),e.onShiftEnter),Hl(Rl([Vl,Il(hd)]),e.onShiftTab),Hl(Rl([kd,Il(hd)]),e.onTab),Hl(Il(wd),e.onUp),Hl(Il(Cd),e.onDown),Hl(Il(xd),e.onLeft),Hl(Il(Sd),e.onRight),Hl(Il(yd),e.onSpace),Hl(Il(bd),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[Hl(Il(yd),Ed)]:[]},function(t){return t.focusIn}),Cg=qm.schema(),kg=Km.schema(),Og=sg.schema(),_g=ug.schema(),Tg=vg.schema(),Eg=Jm.schema(),Dg=wg.schema(),Bg=Sg.schema(),Mg=xa({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Cg,cyclic:kg,flow:Og,flatgrid:_g,matrix:Tg,execution:Eg,menu:Dg,special:Bg}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element,n.element)},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){nt(e,"setGridSize")?e.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:$m}),Ag=function(t,n){return t.components()},Fg=ya({fields:[],name:"replacing",apis:Object.freeze({__proto__:null,append:function(t,n,e,o){cm(t,0,_e,o)},prepend:function(t,n,e,o){cm(t,0,Bn,o)},remove:sm,replaceAt:lm,replaceBy:function(n,t,e,o,r){return P(Ag(n),o).bind(function(t){return lm(n,0,0,t,r)})},set:function(n,t,e,o){_a(function(){var t=M(o,n.getSystem().build);Fs(n,t)},n.element)},contents:Ag})}),Ig=Object.freeze({__proto__:null,focus:dm,blur:function(t,n){n.ignore||t.element.dom.blur()},isFocused:function(t){return Ca(t.element)}}),Rg=Object.freeze({__proto__:null,exhibit:function(t,n){return Nr(n.ignore?{}:{attributes:{tabindex:"-1"}})},events:function(e){return nu([Cr(Di(),function(t,n){dm(t,e),n.stop()})].concat(e.stopMousedown?[Cr(di(),function(t,n){n.event.prevent()})]:[]))}}),Vg=ya({fields:[Ku("onFocus"),To("stopMousedown",!1),To("ignore",!1)],name:"focusing",active:Rg,apis:Ig}),Pg=Object.freeze({__proto__:null,onLoad:bm,toggle:pm,isOn:function(t,n,e){return e.get()},on:hm,off:vm,set:Gm}),Hg=Object.freeze({__proto__:null,exhibit:function(){return Nr({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=pm,iu(function(t){r(t,e,o)})),u=va(t,n,bm);return nu(ft([t.toggleOnExecute?[i]:[],[u]]))}}),zg=ya({fields:[To("selected",!1),xo("toggleClass"),To("toggleOnExecute",!0),Eo("aria",{mode:"none"},so("mode",{pressed:[To("syncWithExpanded",!1),Zu("update",function(t,n,e){on(t.element,"aria-pressed",e),n.syncWithExpanded&&Xm(t,0,e)})],checked:[Zu("update",function(t,n,e){on(t.element,"aria-checked",e)})],expanded:[Zu("update",Xm)],selected:[Zu("update",function(t,n,e){on(t.element,"aria-selected",e)})],none:[Zu("update",st)]}))],name:"toggling",active:Hg,apis:Pg,state:{init:function(){var n=Vo(!1);return{get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(!1)},readState:function(){return n.get()}}}}}),Ng="alloy.item-hover",Lg="alloy.item-focus",Wg=rt(Ng),Ug=rt(Lg),jg=[fo("data"),fo("components"),fo("dom"),To("hasSubmenu",!1),xo("toggling"),Ef("itemBehaviours",[zg,Vg,Mg,Tf]),To("ignoreFocus",!1),To("domModification",{}),Zu("builder",function(t){return{dom:t.dom,domModification:lt(lt({},t.domModification),{attributes:lt(lt(lt({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Df(t.itemBehaviours,[t.toggling.fold(zg.revoke,function(t){return zg.config(lt({aria:{mode:"checked"}},t))}),Vg.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){Sm(t)}}),Mg.config({mode:"execution"}),Tf.config({store:{mode:"memory",initialValue:t.data}}),fm("item-type-events",H(H([],ym(),!0),[Cr(hi(),wm),Cr(Ii(),Vg.focus)],!1))]),components:t.components,eventOrder:t.eventOrder}}),To("eventOrder",{})],Gg=[fo("dom"),fo("components"),Zu("builder",function(t){return{dom:t.dom,components:t.components,events:nu([Cr(Ii(),function(t,n){n.stop()})])}})],Xg=rt("item-widget"),Yg=rt([qf({name:"widget",overrides:function(n){return{behaviours:tc([Tf.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:st}})])}}})]),qg=so("type",{widget:[fo("uid"),fo("data"),fo("components"),fo("dom"),To("autofocus",!1),To("ignoreFocus",!1),Ef("widgetBehaviours",[Tf,Vg,Mg]),To("domModification",{}),bl(Yg()),Zu("builder",function(e){function o(t){return fl(t,e,"widget").map(function(t){return Mg.focusIn(t),t})}function t(t,n){return Cl(n.event.target)||e.autofocus&&n.setSource(t.element),vt.none()}var n=sl(Xg(),e,Yg()),r=ll(Xg(),e,n.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:nu([iu(function(t,n){o(t).each(function(t){n.stop()})}),Cr(hi(),wm),Cr(Ii(),function(t,n){e.autofocus?o(t):Vg.focus(t)})]),behaviours:Df(e.widgetBehaviours,[Tf.config({store:{mode:"memory",initialValue:e.data}}),Vg.config({ignore:e.ignoreFocus,onFocus:function(t){Sm(t)}}),Mg.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:oc(),onLeft:t,onRight:t,onEscape:function(t,n){return Vg.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element),vt.none()):(Vg.focus(t),vt.some(!0))}})])}})],item:jg,separator:Gg}),Kg=rt([$f({factory:{sketch:function(t){var n=co("menu.spec item",qg,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return Tt(n,"uid")?n:lt(lt({},n),{uid:gu("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Jg=rt([fo("value"),fo("items"),fo("dom"),fo("components"),To("eventOrder",{}),$s("menuBehaviours",[gd,Tf,ud,Mg]),Eo("movement",{mode:"menu",moveOnTab:!0},so("mode",{grid:[Fa(),Zu("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[Zu("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),fo("rowSelector")],menu:[To("moveOnTab",!0),Zu("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),mo("markers",Aa()),To("fakeFocus",!1),To("focusManager",Nl()),Ku("onHighlight")]),$g=rt("alloy.menu-focus"),Qg=Sl({name:"Menu",configFields:Jg(),partFields:Kg(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:Zs(t.menuBehaviours,[gd.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),Tf.config({store:{mode:"memory",initialValue:t.value}}),ud.config({find:vt.some}),Mg.config(t.movement.config(t,t.movement))]),events:nu([Cr(Ug(),function(n,e){var t=e.event;n.getSystem().getByDom(t.target).each(function(t){gd.highlight(n,t),e.stop(),vr(n,$g(),{menu:n,item:t})})}),Cr(Wg(),function(t,n){var e=n.event.item;gd.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Zg=function(e,o,r,t){return tt(r,t).bind(function(t){return tt(e,t).bind(function(t){var n=Zg(e,o,r,t);return vt.some([t].concat(n))})}).getOr([])},tp=function(){function a(t){return n(t).bind(Ym)}function e(t){return tt(c.get(),t)}var c=Vo({}),i=Vo({}),s=Vo({}),l=dc(),u=Vo({}),n=function(t){return tt(i.get(),t)};return{setMenuBuilt:function(t,n){var e;i.set(lt(lt({},i.get()),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){l.set(t),c.set(e),i.set(n),u.set(o);var r=function(t,n){var e={};J(t,function(t,n){St(t,function(t){e[t]=n})});var o=n,r=_t(n,function(t,n){return{k:t,v:n}}),i=dt(r,function(t,n){return[n].concat(Zg(e,o,r,n))});return dt(e,function(t){return tt(i,t).getOr([t])})}(o,e);s.set(r)},expand:function(e){return tt(c.get(),e).map(function(t){var n=tt(s.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return tt(s.get(),t)},collapse:function(t){return tt(s.get(),t).bind(function(t){return 1<t.length?vt.some(t.slice(1)):vt.none()})},lookupMenu:n,lookupItem:e,otherMenus:function(t){var n=u.get();return W(kt(n),t)},getPrimary:function(){return l.get().bind(a)},getMenus:function(){return i.get()},clear:function(){c.set({}),i.set({}),s.set({}),l.clear()},isClear:function(){return l.get().isNone()},getTriggeringPath:function(t,u){var n=F(e(t).toArray(),function(t){return a(t).isSome()});return tt(s.get(),t).bind(function(t){var e=L(n.concat(t));return function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return vt.none();n.push(o.getOrDie())}return vt.some(n)}(z(e,function(t,n){return o=t,r=u,i=e.slice(0,n+1),a(o).bind(function(n){return e=o,Q(c.get(),function(t,n){return t===e}).bind(function(t){return r(t).map(function(t){return{triggeredMenu:n,triggeringItem:t,triggeringPath:i}})});var e}).fold(function(){return mt(l.get(),t)?[]:[vt.none()]},function(t){return[vt.some(t)]});var o,r,i}))})}}},np=Ym,ep=rt("collapse-item"),op=wl({name:"TieredMenu",configFields:[Qu("onExecute"),Qu("onEscape"),$u("onOpenMenu"),$u("onOpenSubmenu"),Ku("onRepositionMenu"),Ku("onCollapseMenu"),To("highlightImmediately",!0),vo("data",[fo("primary"),fo("menus"),fo("expansions")]),To("fakeFocus",!1),Ku("onHighlight"),Ku("onHover"),Xu(),fo("dom"),To("navigateOnHover",!0),To("stayInDom",!1),$s("tmenuBehaviours",[Mg,gd,ud,Fg]),To("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:function(a,t){function e(t){var o,r,n=(o=t,r=a.data.primary,dt(a.data.menus,function(t,n){function e(){return Qg.sketch(lt(lt({},t),{value:n,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?Ll:Nl)()}))}return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),e=dt(a.data.menus,function(t,n){return z(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})});return g.setContents(a.data.primary,n,a.data.expansions,e),g.getPrimary()}function c(t){return Tf.getValue(t).value}function u(n,t){gd.highlight(n,t),gd.getHighlighted(t).orThunk(function(){return gd.getFirst(t)}).each(function(t){yr(n,t.element,Ii())})}function s(n,t){return et(M(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?vt.some(t.menu):vt.none()})}))}function l(n,t,e){var o=s(t,t.otherMenus(e));St(o,function(t){Jr(t.element,[a.markers.backgroundMenu]),a.stayInDom||Fg.remove(n,t)})}function f(t,o){var n;J((n=t,r.get().getOrThunk(function(){var e={},t=F(ps(n.element,"."+a.markers.item),function(t){return"true"===rn(t,"aria-haspopup")});return St(t,function(t){n.getSystem().getByDom(t).each(function(t){var n=c(t);e[n]=t})}),r.set(e),e})),function(t,n){var e=wt(o,n);on(t.element,"aria-expanded",e)})}function d(o,r,i){return vt.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return vt.none();var n=t.menu,e=s(r,i.slice(1));return St(e,function(t){Xr(t.element,a.markers.backgroundMenu)}),he(n.element)||Fg.append(o,Tu(n)),Jr(n.element,[a.markers.backgroundMenu]),u(o,n),l(o,r,i),vt.some(n)})})}var m,n,r=dc(),g=tp();function i(r,i,u){if(void 0===u&&(u=m.HighlightSubmenu),i.hasConfigured(dd)&&dd.isDisabled(i))return vt.some(i);var t=c(i);return g.expand(t).bind(function(o){return f(r,o),vt.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return g.setMenuBuilt(n,o),o}(r,e,t);return he(n.element)||Fg.append(r,Tu(n)),a.onOpenSubmenu(r,i,n,L(o)),u===m.HighlightSubmenu?(gd.highlightFirst(n),d(r,g,o)):(gd.dehighlightAll(n),vt.some(i))})})})}function o(n,e){var t=c(e);return g.collapse(t).bind(function(t){return f(n,t),d(n,g,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})}function p(e){return function(n,t){return Iu(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOptional().bind(function(t){return e(n,t).map(_)})})}}function h(t){return gd.getHighlighted(t).bind(gd.getHighlighted)}(n=m={})[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var v=nu([Cr($g(),function(e,o){var t=o.event.item;g.lookupItem(c(t)).each(function(){var t=o.event.menu;gd.highlight(e,t);var n=c(o.event.item);g.refresh(n).each(function(t){return l(e,g,t)})})}),iu(function(n,t){var e=t.event.target;n.getSystem().getByDom(e).each(function(t){0===c(t).indexOf("collapse-item")&&o(n,t),i(n,t,m.HighlightSubmenu).fold(function(){a.onExecute(n,t)},st)})}),eu(function(n,t){e(n).each(function(t){Fg.append(n,Tu(t)),a.onOpenMenu(n,t),a.highlightImmediately&&u(n,t)})})].concat(a.navigateOnHover?[Cr(Wg(),function(t,n){var e=n.event.item,o=t,r=c(e);g.refresh(r).bind(function(t){return f(o,t),d(o,g,t)}),i(t,e,m.HighlightParent),a.onHover(t,e)})]:[])),b={collapseMenu:function(n){h(n).each(function(t){o(n,t)})},highlightPrimary:function(n){g.getPrimary().each(function(t){u(n,t)})},repositionMenus:function(o){g.getPrimary().bind(function(n){return h(o).bind(function(t){var n=c(t),e=et(M(Z(g.getMenus()),np));return g.getTriggeringPath(n,function(t){return n=t,K(e,function(t){return t.getSystem().isConnected()?V(gd.getCandidates(t),function(t){return c(t)===n}):vt.none()});var n})}).map(function(t){return{primary:n,triggeringPath:t}})}).fold(function(){vt.from(o.components()[0]).filter(function(t){return"menu"===rn(t.element,"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var n=t.primary,e=t.triggeringPath;a.onRepositionMenu(o,n,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Zs(a.tmenuBehaviours,[Mg.config({mode:"special",onRight:p(function(t,n){return Cl(n.element)?vt.none():i(t,n,m.HighlightSubmenu)}),onLeft:p(function(t,n){return Cl(n.element)?vt.none():o(t,n)}),onEscape:p(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){g.getPrimary().each(function(t){yr(n,t.element,Ii())})}}),gd.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),ud.config({find:function(t){return gd.getHighlighted(t)}}),Fg.config({})]),eventOrder:a.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:cr(t,n),expansions:{}}},collapseItem:function(t){return{value:Fr(ep()),meta:{text:t}}}}}),rp=wl({name:"InlineView",configFields:[fo("lazySink"),Ku("onShow"),Ku("onHide"),ko("onEscape"),$s("inlineBehaviours",[hf,Tf,rc]),_o("fireDismissalEventInstead",[To("event",Xi())]),_o("fireRepositionEventInstead",[To("event",Yi())]),To("getRelated",vt.none),To("isExtraPart",O),To("eventOrder",vt.none)],factory:function(d,t){function n(e){hf.isOpen(e)&&Tf.getValue(e).each(function(t){switch(t.mode){case"menu":hf.getState(e).each(op.repositionMenus);break;case"position":var n=d.lazySink(e).getOrDie();sf.positionWithinBounds(n,e,t.config,t.getBounds())}})}function o(t,n,e,o){i(t,n,e,function(){return o.map(function(t){return Me(t)})})}function r(t,n,e,o){var r,i,u,a,c,s=(r=d,i=t,u=n,a=o,c="horizontal"===e.type?{layouts:{onLtr:pa,onRtl:ha}}:{},op.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,highlightImmediately:e.menu.highlightImmediately,onEscape:function(){return hf.close(i),r.onEscape.map(function(t){return t(i)}),vt.some(!0)},onExecute:function(){return vt.some(!0)},onOpenMenu:function(t,n){sf.positionWithinBounds(l().getOrDie(),n,u,a())},onOpenSubmenu:function(t,n,e,o){var r=l().getOrDie();sf.position(r,e,{anchor:lt({type:"submenu",item:n},f(o))})},onRepositionMenu:function(t,n,e){var o=l().getOrDie();sf.positionWithinBounds(o,n,u,a()),St(e,function(t){var n=f(t.triggeringPath);sf.position(o,t.triggeredMenu,{anchor:lt({type:"submenu",item:t.triggeringItem},n)})})}}));function l(){return r.lazySink(i)}function f(t){return 2===t.length?c:{}}hf.open(t,s),Tf.setValue(t,vt.some({mode:"menu",menu:s}))}var i=function(t,n,e,o){var r=d.lazySink(t).getOrDie();hf.openWhileCloaked(t,n,function(){return sf.positionWithinBounds(r,t,e,o())}),Tf.setValue(t,vt.some({mode:"position",config:e,getBounds:o}))},e={setContent:function(t,n){hf.setContent(t,n)},showAt:function(t,n,e){o(t,n,e,vt.none())},showWithin:o,showWithinBounds:i,showMenuAt:function(t,n,e){r(t,n,e,vt.none)},showMenuWithinBounds:r,hide:function(t){hf.isOpen(t)&&(Tf.setValue(t,vt.none()),hf.close(t))},getContent:function(t){return hf.getState(t)},reposition:n,isOpen:hf.isOpen};return{uid:d.uid,dom:d.dom,behaviours:Zs(d.inlineBehaviours,[hf.config({isPartOf:function(t,n,e){return Hu(n,e)||(o=e,d.getRelated(t).exists(function(t){return Hu(t,o)}));var o},getAttachPoint:function(t){return d.lazySink(t).getOrDie()},onOpen:function(t){d.onShow(t)},onClose:function(t){d.onHide(t)}}),Tf.config({store:{mode:"memory",initialValue:vt.none()}}),rc.config({channels:lt(lt({},js(lt({isExtraPart:t.isExtraPart},d.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),Gs(lt(lt({},d.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:n})))})]),eventOrder:d.eventOrder,apis:e}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),ip="layout-inset",up=function(t,n,e){return na(Cm(t,n),t.y,e.insetNorth(),za(),"north",oa(t,{top:2}),ip)},ap=function(t,n,e){return na(Cm(t,n),Om(t,n),e.insetSouth(),Na(),"south",oa(t,{bottom:3}),ip)},cp=tinymce.util.Tools.resolve("tinymce.util.Delay"),sp=wl({name:"Button",factory:function(t){function e(n){return tt(t.dom,"attributes").bind(function(t){return tt(t,n)})}var n=xm(t.action),o=t.dom.tag;return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:Df(t.buttonBehaviours,[Vg.config({}),Mg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var t=e("type").getOr("button"),n=e("role").map(function(t){return{role:t}}).getOr({});return lt({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[To("uid",void 0),fo("dom"),To("components",[]),Ef("buttonBehaviours",[Vg,Mg]),xo("action"),xo("role"),To("eventOrder",{})]}),lp=tinymce.util.Tools.resolve("tinymce.util.I18n"),fp={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},dp="temporary-placeholder",mp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},gp=wl({name:"Notification",factory:function(n){function e(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}}function o(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}}var t,r,i,u,a=Pm({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:tc([Fg.config({})])}),c=Pm({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:tc([Fg.config({})])}),s={updateProgress:function(t,n){t.getSystem().isConnected()&&c.getOpt(t).each(function(t){Fg.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(n)]},o(n)])})},updateText:function(t,n){var e;t.getSystem().isConnected()&&(e=a.get(t),Fg.set(e,[oi(n)]))}},l=ft([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return vt.from(mp[t])}).toArray()]),f=Pm(sp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[jm("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":n.translationProvider("Close")}},n.iconProvider)],action:function(t){n.onAction(t)}})),d=[(t=l,r={tag:"div",classes:["tox-notification__icon"]},i=n.iconProvider,u=i(),Um(r,V(t,function(t){return Tt(u,zm(t,u))}).getOr(dp),u,vt.none())),{dom:{tag:"div",classes:["tox-notification__body"]},components:[a.asSpec()],behaviours:tc([Fg.config({})])}];return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},behaviours:tc([Vg.config({}),fm("notification-events",[Cr(vi(),function(t){f.getOpt(t).each(Vg.focus)})])]),components:d.concat(n.progress?[c.asSpec()]:[]).concat(n.closeButton?[f.asSpec()]:[]),apis:s}},configFields:[xo("level"),fo("progress"),fo("icon"),fo("onAction"),fo("text"),fo("iconProvider"),fo("translationProvider"),Ao("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function pp(e,o){function r(){b(i)||(clearTimeout(i),i=null)}var i=null;return{cancel:r,throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r(),i=setTimeout(function(){i=null,e.apply(null,t)},o)}}}function hp(o,t,n,e,r){var i=Ep(o,function(t){return(n=o).isBlock(e=t)||wt(["BR","IMG","HR","INPUT"],e.nodeName)||"false"===n.getContentEditable(e);var n,e});return vt.from(i.backwards(t,n,e,r))}function vp(e,n){return Dp(At.fromDom(e.selection.getNode())).getOrThunk(function(){var i,u,t=At.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());return _e(t,At.fromDom(n.extractContents())),n.insertNode(t.dom),Yt(t).each(function(t){return t.dom.normalize()}),i=gs,(u=function(t){for(var n=Kt(t),e=n.length-1;0<=e;e--){var o=n[e];if(i(o))return vt.some(o);var r=u(o);if(r.isSome())return r}return vt.none()})(t).map(function(t){var n;e.selection.setCursorLocation(t.dom,"img"===Ft(n=t)?1:ms(n).fold(function(){return Kt(n).length},function(t){return t.length}))}),t})}function bp(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function yp(t){return""!==t&&-1!==" \xa0\f\n\r\t\v".indexOf(t)}function xp(t,n){return t.substring(n.length)}function wp(o,t,r,n){return void 0===n&&(n=0),Dp(At.fromDom(t.startContainer)).fold(function(){return function(t,o,i,r){if(void 0===r&&(r=0),!o.collapsed||3!==o.startContainer.nodeType)return vt.none();var n=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return hp(t,o.startContainer,o.startOffset,function(t,r,n){return function(t,n){for(var e=r-1;0<=e;e--){var o=t.charAt(e);if(yp(o))return vt.none();if(o===n)break}return vt.some(e)}(n,i).getOr(r)},n).bind(function(t){var n=o.cloneRange();if(n.setStart(t.container,t.offset),n.setEnd(o.endContainer,o.endOffset),n.collapsed)return vt.none();var e=bp(n);return 0!==e.lastIndexOf(i)||xp(e,i).length<r?vt.none():vt.some({text:xp(e,i),range:n,triggerChar:i})})}(o,t,r,n)},function(t){var n=o.createRng();n.selectNode(t.dom);var e=bp(n);return vt.some({range:n,text:xp(e,r),triggerChar:r})})}function Sp(t,n){return{container:t,offset:n}}function Cp(t){return uo("toolbarbutton",Pp,t)}function kp(t){return uo("ToggleButton",zp,t)}function Op(n,t,e,o){void 0===o&&(o={});var r=t(),i=n.selection.getRng().startContainer.nodeValue,u=F(r.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOrThunk(function(){return e=n.dom,function(t){var n=Ap(t.startContainer,t.startOffset);return!hp(e,n.container,n.offset,function(t,n){return 0===n?-1:n},e.getRoot()).filter(function(t){return!yp(t.container.data.charAt(t.offset-1))}).isSome()};var e})(e.range,i,e.text)});if(0===u.length)return vt.none();var a=Bp.all(M(u,function(n){return n.fetch(e.text,n.maxResults,o).then(function(t){return{matchText:e.text,items:t,columns:n.columns,onAction:n.onAction,highlightOn:n.highlightOn}})}));return vt.some({lookupData:a,context:e})}var _p,Tp,Ep=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),Dp=function(t){return Iu(t,"[data-mce-autocompleter]")},Bp=tinymce.util.Tools.resolve("tinymce.util.Promise"),Mp=function(t){if(3===t.nodeType)return Sp(t,t.data.length);var n=t.childNodes;return 0<n.length?Mp(n[n.length-1]):Sp(t,n.length)},Ap=function(t,n){var e=t.childNodes;return 0<e.length&&n<e.length?Ap(e[n],0):0<e.length&&1===t.nodeType&&e.length===n?Mp(e[e.length-1]):Sp(t,n)},Fp=Jo([go("type"),Co("text")]),Ip=Jo([To("type","autocompleteitem"),To("active",!1),To("disabled",!1),To("meta",{}),go("value"),Co("text"),Co("icon")]),Rp=Jo([go("type"),go("ch"),Do("minChars",1),To("columns",1),Do("maxResults",10),ko("matches"),ho("fetch"),ho("onAction"),Io("highlightOn",[],tr)]),Vp=[Ao("disabled",!1),Co("tooltip"),Co("icon"),Co("text"),Fo("onSetup",function(){return st})],Pp=Jo([go("type"),ho("onAction")].concat(Vp)),Hp=[Ao("active",!1)].concat(Vp),zp=Jo(Hp.concat([go("type"),ho("onAction")])),Np=[Fo("predicate",O),Mo("scope","node",["node","editor"]),Mo("position","selection",["node","selection","line"])],Lp=Vp.concat([To("type","contextformbutton"),To("primary",!1),ho("onAction"),ar("original",h)]),Wp=Hp.concat([To("type","contextformbutton"),To("primary",!1),ho("onAction"),ar("original",h)]),Up=Vp.concat([To("type","contextformbutton")]),jp=Hp.concat([To("type","contextformtogglebutton")]),Gp=so("type",{contextformbutton:Lp,contextformtogglebutton:Wp}),Xp=Jo([To("type","contextform"),Fo("initValue",rt("")),Co("label"),yo("commands",Gp),wo("launch",so("type",{contextformbutton:Up,contextformtogglebutton:jp}))].concat(Np)),Yp=Jo([To("type","contexttoolbar"),go("items")].concat(Np));function qp(t){return tt(kh,t).getOr(wh)}function Kp(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}}function Jp(t){var n=Kp(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:qp(t)}}function $p(t,n,e){return{dom:{tag:"div",classes:ft([[Kp(e).tieredMenu]])},markers:Jp(e)}}function Qp(n,e){return function(t){return M(B(t,e),function(t){return{dom:n,components:t}})}}function Zp(t,e){var o=[],r=[];return St(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],Tt(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),M(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})}function th(n,e){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[Qg.parts.items({preprocess:function(t){return"auto"!==n&&1<n?Qp({tag:"div",classes:["tox-collection__group"]},n)(t):Zp(t,function(t,n){return"separator"===e[n].type})}})]}}function nh(t){return T(t,function(t){return"icon"in t&&void 0!==t.icon})}function eh(t){return console.error(ir(t)),console.log(t),vt.none()}function oh(t,n,e,o,r){var i,u=(i=e,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Qg.parts.items({preprocess:function(t){return Zp(t,function(t,n){return"separator"===i[n].type})}})]});return{value:t,dom:u.dom,components:u.components,items:e}}function rh(t,n,e,o,r){var i,u;return"color"===r?{value:t,dom:(i={dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Qg.parts.items({preprocess:"auto"!==o?Qp({tag:"div",classes:["tox-swatches__row"]},o):h})]}]}).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(i=th(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:t,dom:(i=th(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:t,dom:(i=th(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:{tag:"div",classes:ft([[(u=Kp(r)).menu,"tox-menu-"+o+"-column"],n?[u.hasIcons]:[]])},components:Ah,items:e}:{value:t,dom:(i={dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Qg.parts.items({preprocess:Qp({tag:"div",classes:["tox-collection__group"]},o)})]}).dom,components:i.components,items:e}}function ih(t,o,n){var r=ps(t.element,"."+n);if(0<r.length){var e=P(r,function(t){var n=t.dom.getBoundingClientRect().top,e=r[0].dom.getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return vt.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return vt.none()}function uh(t,n,e){t.getSystem().broadcastOn([qh],{})}function ah(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))}function ch(t){return t.getParam("width",tv.DOM.getStyle(t.getElement(),"width"))}function sh(t){return vt.from(t.getParam("min_width")).filter(u)}function lh(t){return vt.from(t.getParam("min_height")).filter(u)}function fh(t){return vt.from(t.getParam("max_width")).filter(u)}function dh(t){return vt.from(t.getParam("max_height")).filter(u)}function mh(t){return!1!==t.getParam("menubar",!0,"boolean")}function gh(t){var n=t.getParam("toolbar",!0),e=!0===n,o=y(n),r=c(n)&&0<n.length;return!ev(t)&&(r||o||e)}function ph(n){var t=F(D(9,function(t){return n.getParam("toolbar"+(t+1),!1,"string")}),function(t){return"string"==typeof t});return 0<t.length?vt.some(t):vt.none()}(Tp=_p={})[Tp.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Tp[Tp.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var hh,vh,bh,yh,xh=_p,wh="tox-menu-nav__js",Sh="tox-collection__item",Ch="tox-swatch",kh={normal:wh,color:Ch},Oh="tox-collection__item--enabled",_h="tox-collection__item-icon",Th="tox-collection__item-label",Eh="tox-collection__item-caret",Dh="tox-collection__item--active",Bh="tox-collection__item-container",Mh="tox-collection__item-container--row",Ah=[Qg.parts.items({})],Fh=[go("type"),go("src"),Co("alt"),Io("classes",[],tr)],Ih=Jo(Fh),Rh=[go("type"),go("text"),Co("name"),Io("classes",["tox-collection__item-label"],tr)],Vh=Jo(Rh),Ph=Qe(function(){return oo("type",{cardimage:Ih,cardtext:Vh,cardcontainer:Hh})}),Hh=Jo([go("type"),Bo("direction","horizontal"),Bo("align","left"),Bo("valign","middle"),yo("items",Ph)]),zh=[Ao("disabled",!1),Co("text"),Co("shortcut"),ur("value","value",Ue(function(){return Fr("menuitem-value")}),Qo()),To("meta",{})],Nh=Jo([go("type"),Co("label"),yo("items",Ph),Fo("onSetup",function(){return st}),Fo("onAction",st)].concat(zh)),Lh=Jo([go("type"),Ao("active",!1),Co("icon")].concat(zh)),Wh=[go("type"),go("fancytype"),Fo("onAction",st)],Uh=so("fancytype",{inserttable:[To("initData",{})].concat(Wh),colorswatch:[Ro("initData",{},[Ao("allowCustomColors",!0),Oo("colors",Qo())])].concat(Wh)}),jh=Jo([go("type"),Fo("onSetup",function(){return st}),Fo("onAction",st),Co("icon")].concat(zh)),Gh=Jo([go("type"),ho("getSubmenuItems"),Fo("onSetup",function(){return st}),Co("icon")].concat(zh)),Xh=Jo([go("type"),Co("icon"),Ao("active",!1),Fo("onSetup",function(){return st}),ho("onAction")].concat(zh)),Yh=function(t){return n=Fr("unnamed-events"),tc([fm(n,t)]);var n},qh=Fr("tooltip.exclusive"),Kh=Fr("tooltip.show"),Jh=Fr("tooltip.hide"),$h=Object.freeze({__proto__:null,hideAllExclusive:uh,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&Fg.set(t,o)})}}),Qh=Object.freeze({__proto__:null,events:function(r,i){function e(n){i.getTooltip().each(function(t){Vs(t),r.onHide(n,t),i.clearTooltip()}),i.clearTimer()}return nu(ft([[Cr(Kh,function(o){i.resetTimer(function(){var t,n,e=o;i.isShowing()||(uh(e),t=r.lazySink(e).getOrDie(),n=e.getSystem().build({dom:r.tooltipDom,components:r.tooltipComponents,events:nu("normal"===r.mode?[Cr(hi(),function(t){hr(e,Kh)}),Cr(gi(),function(t){hr(e,Jh)})]:[]),behaviours:tc([Fg.config({})])}),i.setTooltip(n),Is(t,n),r.onShow(e,n),sf.position(t,n,{anchor:r.anchor(e)}))},r.delay)}),Cr(Jh,function(t){i.resetTimer(function(){e(t)},r.delay)}),Cr(Ai(),function(t,n){n.universal||wt(n.channels,qh)&&e(t)}),ou(function(t){e(t)})],"normal"===r.mode?[Cr(vi(),function(t){hr(t,Kh)}),Cr(Bi(),function(t){hr(t,Jh)}),Cr(hi(),function(t){hr(t,Kh)}),Cr(gi(),function(t){hr(t,Jh)})]:[Cr(Qi(),function(t,n){hr(t,Kh)}),Cr(Zi(),function(t){hr(t,Jh)})]]))}}),Zh=ya({fields:[fo("lazySink"),fo("tooltipDom"),To("exclusive",!0),To("tooltipComponents",[]),To("delay",300),Mo("mode","normal",["normal","follow-highlight"]),To("anchor",function(t){return{type:"hotspot",hotspot:t,layouts:{onLtr:rt([Ja,Ka,Ga,Ya,Xa,qa]),onRtl:rt([Ja,Ka,Ga,Ya,Xa,qa])}}}),Ku("onHide"),Ku("onShow")],name:"tooltipping",active:Qh,state:Object.freeze({__proto__:null,init:function(){function e(){o.on(clearTimeout)}var o=dc(),t=dc(),n=rt("not-implemented");return xu({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:e,resetTimer:function(t,n){e(),o.set(setTimeout(t,n))},readState:n})}}),apis:$h}),tv=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),nv=tinymce.util.Tools.resolve("tinymce.EditorManager"),ev=function(t){return ph(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},_)};function ov(t){return t.getParam("toolbar_mode","","string")}function rv(t){return t.getParam("toolbar_location",bh.auto,"string")}function iv(t){return rv(t)===bh.bottom}function uv(t){if(!t.inline)return vt.none();var n=t.getParam("fixed_toolbar_container","","string");if(0<n.length)return Fu(ve(),n);var e=t.getParam("fixed_toolbar_container_target");return d(e)?vt.some(At.fromDom(e)):vt.none()}function av(t){return t.inline&&uv(t).isSome()}function cv(t){return uv(t).getOrThunk(function(){return tn(me(At.fromDom(t.getElement())))})}function sv(t){return t.inline&&!mh(t)&&!gh(t)&&!ev(t)}function lv(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!av(t)&&!sv(t)}function fv(t,n){var e=t.outerContainer.element;n&&(t.mothership.broadcastOn([vf()],{target:e}),t.uiMothership.broadcastOn([vf()],{target:e})),t.mothership.broadcastOn([Yv],{readonly:n}),t.uiMothership.broadcastOn([Yv],{readonly:n})}function dv(t,n){t.on("init",function(){t.mode.isReadOnly()&&fv(n,!0)}),t.on("SwitchMode",function(){return fv(n,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")}function mv(){var t;return rc.config({channels:((t={})[Yv]={schema:qv,onReceive:function(t,n){dd.set(t,n.readonly)}},t)})}function gv(t,n){var e=t.getApi(n);return function(t){t(e)}}function pv(e,o){return eu(function(t){gv(e,t)(function(t){var n=e.onSetup(t);m(n)&&o.set(n)})})}function hv(n,e){return ou(function(t){return gv(n,t)(e.get())})}function vv(t,n,e,o){var r,i,u=Vo(st);return{type:"item",dom:n.dom,components:Qv(n.optComponents),data:t.data,eventOrder:$v,hasSubmenu:t.triggersSubmenu,itemBehaviours:tc([fm("item-events",[(r=t,i=e,iu(function(t,n){gv(r,t)(r.onAction),r.triggersSubmenu||i!==xh.CLOSE_ON_EXECUTE||(hr(t,Pi()),n.stop())})),pv(t,u),hv(t,u)]),dd.config({disabled:function(){return t.disabled||o.isDisabled()},disableClass:"tox-collection__item--state-disabled"}),mv(),Fg.config({})].concat(t.itemBehaviours))}}function bv(t){return{value:t.value,meta:lt({text:t.text.getOr("")},t.meta)}}function yv(t,n,e){return jm(t,{tag:"div",classes:e=void 0===e?[_h]:e},n)}function xv(t){return{dom:{tag:"div",classes:[Th]},components:[oi(lp.translate(t))]}}function wv(t,n){return{dom:{tag:"div",classes:n,innerHtml:t}}}function Sv(t,n){return{dom:{tag:"div",classes:[Th]},components:[{dom:{tag:t.tag,styles:t.styles},components:[oi(lp.translate(n))]}]}}function Cv(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(e=Zv.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},n=M(t.split("+"),function(t){var n=t.toLowerCase().trim();return Tt(e,n)?e[n]:t}),Zv.mac?n.join(""):n.join("+"))}};var e,n}function kv(t){return yv("checkmark",t,["tox-collection__item-checkmark"])}function Ov(t){var n=t.map(function(t){return{attributes:{title:lp.translate(t)}}}).getOr({});return lt({tag:"div",classes:[wh,Sh]},n)}function _v(t,n,e,o){return void 0===o&&(o=vt.none()),"color"===t.presets?(r=n,i=o,c=t.ariaLabel,s=t.value,{dom:(u=t.iconContent.map(function(t){return n=r.icons,e=i,Nm(t,o=n()).or(e).getOrThunk(Hm(o));var n,e,o}).getOr(""),a={tag:"div",attributes:c.map(function(t){return{title:r.translate(t)}}).getOr({}),classes:["tox-swatch"]},lt(lt({},a),"custom"===s?{tag:"button",classes:H(H([],a.classes,!0),["tox-swatches__picker-btn"],!1),innerHtml:u}:"remove"===s?{classes:H(H([],a.classes,!0),["tox-swatch--remove"],!1),innerHtml:u}:{attributes:lt(lt({},a.attributes),{"data-mce-color":s}),styles:{"background-color":s}})),optComponents:[]}):(l=t,f=n,d=o,m={tag:"div",classes:[_h]},g=e?l.iconContent.map(function(t){return jm(t,m,f.icons,d)}).orThunk(function(){return vt.some({dom:m})}):vt.none(),p=l.checkMark,h=vt.from(l.meta).fold(function(){return xv},function(t){return Tt(t,"style")?S(Sv,t.style):xv}),v=l.htmlContent.fold(function(){return l.textContent.map(h)},function(t){return vt.some(wv(t,[Th]))}),{dom:Ov(l.ariaLabel),optComponents:[g,v,l.shortcutContent.map(Cv),p,l.caret]});var r,i,u,a,c,s,l,f,d,m,g,p,h,v}function Tv(t,n){return tt(t,"tooltipWorker").map(function(e){return[Zh.config({lazySink:n.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{type:"submenu",item:t,overrides:{maxHeightFunction:Ic}}},mode:"follow-highlight",onShow:function(n,t){e(function(t){Zh.setComponents(n,[ku({element:At.fromDom(t)})])})}})]}).getOr([])}function Ev(t,n){var e=lp.translate(t),o=tv.DOM.encode(e);if(0<n.length){var r=new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return o.replace(r,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return o}function Dv(t){return{value:t}}function Bv(t){return eb.test(t)||ob.test(t)}function Mv(t){var n=t.toString(16);return(1===n.length?"0"+n:n).toUpperCase()}function Av(t){return Dv(Mv(t.red)+Mv(t.green)+Mv(t.blue))}function Fv(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}}function Iv(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255}function Rv(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100,i=ib(0,rb(i,1)),u=ib(0,rb(u,1));if(0===i)return Fv(n=e=o=ub(255*u),e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return Fv(n=ub(255*(n+l)),e=ub(255*(e+l)),o=ub(255*(o+l)),1)}function Vv(t){var n,e,o=(n={value:t.value.replace(eb,function(t,n,e,o){return n+n+e+e+o+o})},null===(e=ob.exec(n.value))?["FFFFFF","FF","FF","FF"]:e);return Fv(parseInt(o[1],16),parseInt(o[2],16),parseInt(o[3],16),1)}function Pv(t,n,e,o){return Fv(parseInt(t,10),parseInt(n,10),parseInt(e,10),parseFloat(o))}function Hv(t){if("transparent"===t)return vt.some(Fv(0,0,0,0));var n=ab.exec(t);if(null!==n)return vt.some(Pv(n[1],n[2],n[3],"1"));var e=cb.exec(t);return null!==e?vt.some(Pv(e[1],e[2],e[3],e[4])):vt.none()}function zv(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"}function Nv(t,n){return t.fire("ResizeContent",n)}function Lv(t,n,e){return{hue:t,saturation:n,value:e}}function Wv(t){var n,e,o=0,r=t.red/255,i=t.green/255,u=t.blue/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?Lv(0,0,100*(o=a)):(e=60*((r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),n=(c-a)/c,o=c,Lv(Math.round(e),Math.round(100*n),Math.round(100*o)))}function Uv(t){return Av(Rv(t))}function jv(o){return(Bv(n=o)?vt.some({value:(Dt(t=n,"#")?t.substring("#".length):t).toUpperCase()}):vt.none()).orThunk(function(){return Hv(o).map(Av)}).getOrThunk(function(){var t=document.createElement("canvas");t.height=1,t.width=1;var n=t.getContext("2d");n.clearRect(0,0,t.width,t.height),n.fillStyle="#FFFFFF",n.fillStyle=o,n.fillRect(0,0,1,1);var e=n.getImageData(0,0,1,1).data;return Av(Fv(e[0],e[1],e[2],e[3]))});var t,n}(vh=hh=hh||{}).default="wrap",vh.floating="floating",vh.sliding="sliding",vh.scrolling="scrolling",(yh=bh=bh||{}).auto="auto",yh.top="top",yh.bottom="bottom";function Gv(t){return dd.config({disabled:t,disableClass:"tox-tbtn--disabled"})}var Xv,Yv="silver.readonly",qv=Jo([mo("readonly",nr)]),Kv=function(t){return dd.config({disabled:t})},Jv=function(t){return dd.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},$v=((Xv={})[Fi()]=["disabling","alloy.base.behaviour","toggling","item-events"],Xv),Qv=et,Zv=tinymce.util.Tools.resolve("tinymce.Env"),tb=function(t,a){return M(t,function(t){switch(t.type){case"cardcontainer":return r=tb((o=t).items,a),i="vertical"===o.direction?"tox-collection__item-container--column":Mh,u="left"===o.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right",{dom:{tag:"div",classes:[Bh,i,u,function(){switch(o.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}}()]},components:r};case"cardimage":return e=t.src,{dom:{tag:"img",classes:t.classes,attributes:{src:e,alt:t.alt.getOr("")}}};case"cardtext":var n=t.name.exists(function(t){return wt(a.cardText.highlightOn,t)})?vt.from(a.cardText.matchText).getOr(""):"";return wv(Ev(t.text,n),t.classes)}var e,o,r,i,u})},nb=il(Xg(),Yg()),eb=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,ob=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,rb=Math.min,ib=Math.max,ub=Math.round,ab=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,cb=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,sb=Fv(255,0,0,1),lb=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),fb="tinymce-custom-colors";function db(t){return!1!==t.getParam("custom_colors")}function mb(t){var n=t.getParam("color_map");return void 0!==n?function(t){for(var n=[],e=0;e<t.length;e+=2)n.push({text:t[e+1],value:"#"+jv(t[e]).value,type:"choiceitem"});return n}(n):Eb}function gb(t){Db.add(t)}function pb(t){var n,e=(n=mb(t).length,Math.max(5,Math.ceil(Math.sqrt(n))));return t.getParam("color_cols",e,"number")}function hb(t){var n="choiceitem",e={type:n,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return t?[e,{type:n,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}function vb(n,e,t,o){"custom"===t?Bb(n)(function(t){t.each(function(t){gb(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},"#000000"):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))}function bb(t,n){return t.concat(M(Db.state(),function(t){return{type:Tb,text:t,value:t}}).concat(hb(n)))}function yb(n,e){return function(t){t(bb(n,e))}}function xb(t,n,e){t.setIconFill("forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",e)}function wb(i,e,u,t,o){i.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){var o,r;return vt.from((o=u,i.dom.getParents(i.selection.getStart(),function(t){var n;(n=t.style["forecolor"===o?"color":"background-color"])&&(r=r||n)}),r)).bind(function(t){return Hv(t).map(function(t){var n=Av(t).value;return ut(e.toLowerCase(),n)})}).getOr(!1)},columns:pb(i),fetch:yb(mb(i),db(i)),onAction:function(t){null!==o.get()&&vb(i,u,o.get(),st)},onItemAction:function(t,n){vb(i,u,n,function(t){o.set(t),i.fire("TextColorChange",{name:e,color:t})})},onSetup:function(n){function t(t){t.name===e&&xb(n,t.name,t.color)}return null!==o.get()&&xb(n,e,o.get()),i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})}function Sb(n,t,e,o){n.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){vb(n,e,t.value,st)}}]}})}function Cb(t,n,e,o,r,i,u,a){return rh(t,nh(n),Mb(n,e,o,"color"!==r?"normal":"color",i,u,a),o,r)}function kb(t,n){var e=Jp(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}}var Ob,_b,Tb="choiceitem",Eb=[{type:Tb,text:"Light Green",value:"#BFEDD2"},{type:Tb,text:"Light Yellow",value:"#FBEEB8"},{type:Tb,text:"Light Red",value:"#F8CAC6"},{type:Tb,text:"Light Purple",value:"#ECCAFA"},{type:Tb,text:"Light Blue",value:"#C2E0F4"},{type:Tb,text:"Green",value:"#2DC26B"},{type:Tb,text:"Yellow",value:"#F1C40F"},{type:Tb,text:"Red",value:"#E03E2D"},{type:Tb,text:"Purple",value:"#B96AD9"},{type:Tb,text:"Blue",value:"#3598DB"},{type:Tb,text:"Dark Turquoise",value:"#169179"},{type:Tb,text:"Orange",value:"#E67E23"},{type:Tb,text:"Dark Red",value:"#BA372A"},{type:Tb,text:"Dark Purple",value:"#843FA1"},{type:Tb,text:"Dark Blue",value:"#236FA1"},{type:Tb,text:"Light Gray",value:"#ECF0F1"},{type:Tb,text:"Medium Gray",value:"#CED4D9"},{type:Tb,text:"Gray",value:"#95A5A6"},{type:Tb,text:"Dark Gray",value:"#7E8C8D"},{type:Tb,text:"Navy Blue",value:"#34495E"},{type:Tb,text:"Black",value:"#000000"},{type:Tb,text:"White",value:"#ffffff"}],Db=function(e){void 0===e&&(e=10);function o(t){i.splice(t,1)}var t,n=lb.getItem(fb),r=y(n)?JSON.parse(n):[],i=e-(t=r).length<0?t.slice(0,e):t;return{add:function(t){var n;(-1===(n=p(i,t))?vt.none():vt.some(n)).each(o),i.unshift(t),i.length>e&&i.pop(),lb.setItem(fb,JSON.stringify(i))},state:function(){return i.slice(0)}}}(10),Bb=function(r){return function(e,t){var o=!1;r.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:{colorpicker:t},onAction:function(t,n){"hex-valid"===n.name&&(o=n.value)},onSubmit:function(t){var n=t.getData().colorpicker;o?(e(vt.from(n)),t.close()):r.windowManager.alert(r.translate(["Invalid hex color code: {0}",n]))},onClose:st,onCancel:function(){e(vt.none())}})}},Mb=function(e,o,r,i,u,a,c){return et(M(e,function(n){return"choiceitem"===n.type?uo("choicemenuitem",Lh,n).fold(eh,function(t){return vt.some(function(n,t,e,o,r,i,u,a){void 0===a&&(a=!0);var c=_v({presets:e,textContent:t?n.text:vt.none(),htmlContent:vt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:vt.none(),checkMark:t?vt.some(kv(u.icons)):vt.none(),caret:vt.none(),value:n.value},u,a);return Xo(vv({data:bv(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){zg.set(n,t)},isActive:function(){return zg.isOn(n)},isDisabled:function(){return dd.isDisabled(n)},setDisabled:function(t){return dd.set(n,t)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),st},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:Oh,toggleOnExecute:!1,selected:n.active}})}(t,1===r,i,o,a(n.value),u,c,nh(e)))}):vt.none()}))},Ab=Fr("cell-over"),Fb=Fr("cell-execute"),Ib={inserttable:function(o){var t=Fr("size-label"),i=function(t){for(var n=[],e=0;e<10;e++){for(var o=[],r=0;r<10;r++)o.push(function(n,e,t){function o(t){return vr(t,Fb,{row:n,col:e})}function r(t,n){n.stop(),o(t)}var i;return _u({dom:{tag:"div",attributes:((i={role:"button"})["aria-labelledby"]=t,i)},behaviours:tc([fm("insert-table-picker-cell",[Cr(hi(),Vg.focus),Cr(Fi(),o),Cr(Ci(),r),Cr(Ri(),r)]),zg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Vg.config({onFocus:function(t){return vr(t,Ab,{row:n,col:e})}})])})}(e,r,t));n.push(o)}return n}(t),u=Pm({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[oi("0x0")],behaviours:tc([Fg.config({})])});return{type:"widget",data:{value:Fr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[nb.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:z(i,function(t){return M(t,Tu)}).concat(u.asSpec()),behaviours:tc([fm("insert-table-picker",[Tr(Ab,function(t,n,e){var o=e.event.row,r=e.event.col;!function(t,n,e){for(var o=0;o<10;o++)for(var r=0;r<10;r++)zg.set(t[o][r],o<=n&&r<=e)}(i,o,r),Fg.set(u.get(t),[oi(r+1+"x"+(o+1))])}),Tr(Fb,function(t,n,e){o.onAction({numRows:e.event.row+1,numColumns:e.event.col+1}),hr(t,Pi())})]),Mg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function(n,t){var e,o,r,i=(o=t,r=(e=n).initData.allowCustomColors&&o.colorinput.hasCustomColors(),e.initData.colors.fold(function(){return bb(o.colorinput.getColors(),r)},function(t){return t.concat(hb(r))})),u=t.colorinput.getColorCols(),a=Cb(Fr("menu-value"),i,function(t){n.onAction({value:t})},u,"color",xh.CLOSE_ON_EXECUTE,O,t.shared.providers),c=lt(lt({},a),{markers:Jp("color"),movement:kb(u,"color")});return{type:"widget",data:{value:Fr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[nb.widget(Qg.sketch(c))]}}},Rb=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:lt({tag:"div",classes:[Sh,"tox-collection__group-heading"]},n),components:[]}},Vb=function(t,n,e,o){void 0===o&&(o=!0);var r=_v({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,caret:vt.none(),checkMark:vt.none(),shortcutContent:t.shortcut},e,o);return vv({data:bv(t),getApi:function(n){return{isDisabled:function(){return dd.isDisabled(n)},setDisabled:function(t){return dd.set(n,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e)},Pb=function(t,n,e,o,r){void 0===o&&(o=!0);var i=(r=void 0!==r&&r)?yv("chevron-down",e.icons,[Eh]):yv("chevron-right",e.icons,[Eh]),u=_v({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,caret:vt.some(i),checkMark:vt.none(),shortcutContent:t.shortcut},e,o);return vv({data:bv(t),getApi:function(n){return{isDisabled:function(){return dd.isDisabled(n)},setDisabled:function(t){return dd.set(n,t)}}},disabled:t.disabled,onAction:st,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,n,e)},Hb=function(t,n,e,o){void 0===o&&(o=!0);var r=_v({iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,checkMark:vt.some(kv(e.icons)),caret:vt.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return Xo(vv({data:bv(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){zg.set(n,t)},isActive:function(){return zg.isOn(n)},isDisabled:function(){return dd.isDisabled(n)},setDisabled:function(t){return dd.set(n,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e),{toggling:{toggleClass:Oh,toggleOnExecute:!1,selected:t.active}})},zb=function(n,e){return tt(Ib,n.fancytype).map(function(t){return t(n,e)})};function Nb(t,u,a,n,c,s,l){var e=1===n,o=!e||nh(t);return et(M(t,function(t){switch(t.type){case"separator":return uo("Autocompleter.Separator",Fp,t).fold(eh,function(t){return vt.some(Rb(t))});case"cardmenuitem":return uo("cardmenuitem",Nh,t).fold(eh,function(n){return vt.some((t=lt(lt({},n),{onAction:function(t){n.onAction(t),a(n.value,n.meta)}}),e=c,o=s,r={itemBehaviours:Tv(n.meta,s),cardText:{matchText:u,highlightOn:l}},i={dom:Ov(t.label),optComponents:[vt.some({dom:{tag:"div",classes:[Bh,Mh]},components:tb(t.items,r)})]},vv({data:bv(lt({text:vt.none()},t)),disabled:t.disabled,getApi:function(e){return{isDisabled:function(){return dd.isDisabled(e)},setDisabled:function(n){dd.set(e,n),St(ps(e.element,"*"),function(t){e.getSystem().getByDom(t).each(function(t){t.hasConfigured(dd)&&dd.set(t,n)})})}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:vt.from(r.itemBehaviours).getOr([])},i,e,o.providers)));var t,e,o,r,i});default:return uo("Autocompleter.Item",Ip,t).fold(eh,function(t){return vt.some(function(n,e,t,o,r,i,u,a){void 0===a&&(a=!0);var c=_v({presets:o,textContent:vt.none(),htmlContent:t?n.text.map(function(t){return Ev(t,e)}):vt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:vt.none(),checkMark:vt.none(),caret:vt.none(),value:n.value},u.providers,a,n.icon);return vv({data:bv(n),disabled:n.disabled,getApi:rt({}),onAction:function(t){return r(n.value,n.meta)},onSetup:rt(st),triggersSubmenu:!1,itemBehaviours:Tv(n.meta,u)},c,i,u.providers)}(t,u,e,"normal",a,c,s,o))})}}))}function Lb(t,n,e,o,r){var i=nh(n),u=et(M(n,function(t){function n(t){return function(t,n,e,o,r){function i(t){return r?lt(lt({},t),{shortcut:vt.none(),icon:t.text.isSome()?vt.none():t.icon}):t}var u=e.shared.providers;switch(t.type){case"menuitem":return uo("menuitem",jh,t).fold(eh,function(t){return vt.some(Vb(i(t),n,u,o))});case"nestedmenuitem":return uo("nestedmenuitem",Gh,t).fold(eh,function(t){return vt.some(Pb(i(t),n,u,o,r))});case"togglemenuitem":return uo("togglemenuitem",Xh,t).fold(eh,function(t){return vt.some(Hb(i(t),n,u,o))});case"separator":return uo("separatormenuitem",Fp,t).fold(eh,function(t){return vt.some(Rb(t))});case"fancymenuitem":return uo("fancymenuitem",Uh,t).fold(eh,function(t){return zb(i(t),e)});default:return console.error("Unknown item in general menu",t),vt.none()}}(t,e,o,r?!Tt(t,"text"):i,r)}return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?n(lt(lt({},t),{disabled:!0})):n(t)}));return(r?oh:rh)(t,i,u,1,"normal")}function Wb(t){return op.singleData(t.value,t)}function Ub(t,n,e){return Iu(t,n,e).isSome()}function jb(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=setTimeout(function(){e.apply(null,t),r=null},o)}}}function Gb(t){var n=t.raw;return void 0===n.touches||1!==n.touches.length?vt.none():vt.some(n.touches[0])}function Xb(){return se().browser.isFirefox()}function Yb(n,t){var e,o,r,i,u,a,c,s,l,f=lt({stopBackspace:!0},t),d=(u=f,a=dc(),c=Vo(!1),s=jb(function(t){u.triggerEvent(Vi(),t),c.set(!0)},400),l=sr([{key:ci(),value:function(e){return Gb(e).each(function(t){s.cancel();var n={x:t.clientX,y:t.clientY,target:e.target};s.schedule(e),c.set(!1),a.set(n)}),vt.none()}},{key:si(),value:function(t){return s.cancel(),Gb(t).each(function(i){a.on(function(t){var n=i,e=t,o=Math.abs(n.clientX-e.x),r=Math.abs(n.clientY-e.y);(5<o||5<r)&&a.clear()})}),vt.none()}},{key:li(),value:function(n){return s.cancel(),a.get().filter(function(t){return Lt(t.target,n.target)}).map(function(t){return c.get()?(n.prevent(),!1):u.triggerEvent(Ri(),n)})}}]),{fireIfReady:function(n,t){return tt(l,t).bind(function(t){return t(n)})}}),m=M(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return mc(n,t,function(n){d.fireIfReady(n,t).each(function(t){t&&n.kill()}),f.triggerEvent(t,n)&&n.kill()})}),g=dc(),p=mc(n,"paste",function(n){d.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),f.triggerEvent("paste",n)&&n.kill(),g.set(setTimeout(function(){f.triggerEvent(Mi(),n)},0))}),h=mc(n,"keydown",function(t){var n;f.triggerEvent("keydown",t)?t.kill():!f.stopBackspace||(n=t).raw.which!==pd[0]||wt(["input","textarea"],Ft(n.target))||Ub(n.target,'[contenteditable="true"]')||t.prevent()}),v=(e=n,o=function(t){f.triggerEvent("focusin",t)&&t.kill()},Xb()?gc(e,"focus",o):mc(e,"focusin",o)),b=dc(),y=(r=n,i=function(t){f.triggerEvent("focusout",t)&&t.kill(),b.set(setTimeout(function(){f.triggerEvent(Bi(),t)},0))},Xb()?gc(r,"blur",i):mc(r,"focusout",i));return{unbind:function(){St(m,function(t){t.unbind()}),h.unbind(),v.unbind(),y.unbind(),p.unbind(),g.on(clearTimeout),b.on(clearTimeout)}}}function qb(t,n){return Vo(tt(t,"target").getOr(n))}function Kb(t,o,n,e,r,i){var u,a,c=t(o,e),s=(u=Vo(!1),a=Vo(!1),{stop:function(){u.set(!0)},cut:function(){a.set(!0)},isStopped:u.get,isCut:a.get,event:n,setSource:r.set,getSource:r.get});return c.fold(function(){return i.logEventNoHandlers(o,e),oy.complete()},function(n){var e=n.descHandler;return Lr(e)(s),s.isStopped()?(i.logEventStopped(o,n.element,e.purpose),oy.stopped()):s.isCut()?(i.logEventCut(o,n.element,e.purpose),oy.complete()):Yt(n.element).fold(function(){return i.logNoParent(o,n.element,e.purpose),oy.complete()},function(t){return i.logEventResponse(o,n.element,e.purpose),oy.resume(t)})})}function Jb(){function r(t){Ir(t.element).each(function(t){delete a[t],i.unregisterId(t)})}var u,i=(u={},{registerId:function(r,i,t){J(t,function(t,n){var e,o=void 0!==u[n]?u[n]:{};o[i]={cHandler:S.apply(void 0,[(e=t).handler].concat(r)),purpose:e.purpose},u[n]=o})},unregisterId:function(e){J(u,function(t,n){Tt(t,e)&&delete t[e]})},filterByType:function(t){return tt(u,t).map(function(t){return $(t,function(t,n){return{id:n,descHandler:t}})}).getOr([])},find:function(t,n,e){return tt(u,n).bind(function(o){return dr(e,function(t){return n=o,Ir(e=t).bind(function(t){return tt(n,t)}).map(function(t){return{element:e,descHandler:t}});var n,e},t)})}}),a={};return{find:function(t,n,e){return i.find(t,n,e)},filter:function(t){return i.filterByType(t)},register:function(t){var e,o=Ir((e=t).element).getOrThunk(function(){return t=e.element,n=Fr(fu+"uid-"),mu(t,n),n;var t,n});nt(a,o)&&function(t){var n=a[o];if(n!==t)throw new Error('The tagId "'+o+'" is already used by: '+Ar(n.element)+"\nCannot use it for: "+Ar(t.element)+"\nThe conflicting element is"+(he(n.element)?" ":" not ")+"already in the DOM");r(t)}(t),i.registerId([t],o,t.events),a[o]=t},unregister:r,getById:function(t){return tt(a,t)}}}function $b(e){function o(n){return Yt(e.element).fold(_,function(t){return Lt(n,t)})}function s(t,n){return i.find(o,t,n)}function r(n){var t=i.filter(Ai());St(t,function(t){Lr(t.descHandler)(n)})}var i=Jb(),t=Yb(e.element,{triggerEvent:function(n,e){return Nu(n,e.target,function(t){return iy(s,n,e,e.target,t)})}}),u={debugInfo:rt("real"),triggerEvent:function(n,e,o){Nu(n,e,function(t){return iy(s,n,o,e,t)})},triggerFocus:function(a,c){Ir(a).fold(function(){Sa(a)},function(t){Nu(Di(),a,function(t){var n,e,o=s,r=Di(),i=t,u=qb(n={originator:c,kill:st,prevent:st,target:a},e=a);return Kb(o,r,n,e,u,i),!1})})},triggerEscape:function(t,n){u.triggerEvent("keydown",t.element,n.event)},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:_u,addToGui:function(t){c(t)},removeFromGui:function(t){l(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){a(t)},broadcast:function(t){f(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:_},n=function(t){t.connect(u),Xn(t.element)||(i.register(t),St(t.components(),n),u.triggerEvent(zi(),t.element,{target:t.element}))},a=function(t){Xn(t.element)||(St(t.components(),a),i.unregister(t)),t.disconnect()},c=function(t){Is(e,t)},l=function(t){Vs(t)},f=function(t){r({universal:!0,data:t})},d=function(t,n){r({universal:!1,channels:t,data:n})},m=function(t,n){var e,o,r=i.filter(t);return o={stop:function(){e.set(!0)},cut:st,isStopped:(e=Vo(!1)).get,isCut:O,event:n,setSource:k("Cannot set source of a broadcasted event"),getSource:k("Cannot get source of a broadcasted event")},St(r,function(t){Lr(t.descHandler)(o)}),o.isStopped()},g=function(t){return i.getById(t).fold(function(){return Re.error(new Error('Could not find component with uid: "'+t+'" in system.'))},Re.value)},p=function(t){var n=Ir(t).getOr("not found");return g(n)};return n(e),{root:e,element:e.element,destroy:function(){t.unbind(),Te(e.element)},add:c,remove:l,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:a,broadcast:f,broadcastOn:d,broadcastEvent:m}}function Qb(t,n,e,o){var r=my(t,n,e,o);return sy.sketch(r)}function Zb(t,n){return sy.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})}function ty(t){return tc([Vg.config({onFocus:t.selectOnFocus?function(t){var n=t.element,e=$r(n);n.dom.setSelectionRange(0,e.length)}:st})])}function ny(t){return{tag:t.tag,attributes:lt({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}}(_b=Ob={})[_b.ContentFocus=0]="ContentFocus",_b[_b.UiFocus=1]="UiFocus";function ey(f,c){function e(){return r.get().isSome()}function s(){e()&&rp.hide(d)}var o,t,r=dc(),l=Vo(!1),d=_u(rp.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:tc([fm("dismissAutocompleter",[Cr(Xi(),function(){return m()})])]),lazySink:c.getSink})),m=function(){var t;e()&&(t=r.get().map(function(t){return t.element}),Dp(t.getOr(At.fromDom(f.selection.getNode()))).each(Fn),s(),r.clear(),l.set(!1))},u=Rt(function(){return e=dt(f.ui.registry.getAll().popups,function(t){return uo("Autocompleter",Rp,t).fold(function(t){throw new Error(ir(t))},h)}),t=$(e,function(t){return t.ch}),n={},St(t,function(t){n[t]={}}),o=kt(n),r=Z(e),{dataset:e,triggerChars:o,lookupByChar:function(n){return F(r,function(t){return t.ch===n})}};var t,n,e,o,r}),g=function(t){var n=t;r.get().map(function(t){return wp(f.dom,f.selection.getRng(),t.triggerChar).bind(function(t){return Op(f,u,t,n)})}).getOrThunk(function(){return n=f,t=(e=u)(),o=n.selection.getRng(),r=n.dom,i=o,K(t.triggerChars,function(t){return wp(r,i,t)}).bind(function(t){return Op(n,e,t)});var n,e,t,o,r,i}).fold(m,function(a){var t,n=a.context;e()||(t=vp(f,n.range),r.set({triggerChar:n.triggerChar,element:t,matchLength:n.text.length}),l.set(!1)),a.lookupData.then(function(u){r.get().map(function(t){var n,e,o,r,i=a.context;t.triggerChar===i.triggerChar&&(e=i.triggerChar,r=K(o=u,function(t){return vt.from(t.columns)}).getOr(1),0<(n=z(o,function(i){return Nb(i.items,i.matchText,function(o,r){var t=f.selection.getRng();wp(f.dom,t,e).fold(function(){return console.error("Lost context. Cursor probably moved")},function(t){var n=t.range,e={hide:function(){m()},reload:function(t){s(),g(t)}};l.set(!0),i.onAction(e,n,o,r),l.set(!1)})},r,xh.BUBBLE_TO_SANDBOX,c,i.highlightOn)})).length?function(t,n,e,o){t.matchLength=n.text.length;var r,i,u,a,c,s,l=K(e,function(t){return vt.from(t.columns)}).getOr(1);rp.showAt(d,Qg.sketch((r=rh("autocompleter-value",!0,o,l,"normal"),i=l,a=((u=Ob.ContentFocus)===Ob.ContentFocus?Ll:Nl)(),c=kb(i,"normal"),s=Jp("normal"),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:s.selectedItem,item:s.item},movement:c,fakeFocus:u===Ob.ContentFocus,focusManager:a,menuBehaviours:Yh("auto"!==i?[]:[eu(function(o,t){ih(o,4,s.item).each(function(t){var n=t.numColumns,e=t.numRows;Mg.setGridSize(o,e,n)})})])})),{anchor:{type:"node",root:At.fromDom(f.getBody()),node:vt.from(t.element)}}),rp.getContent(d).each(gd.highlightFirst)}(t,i,u,n):(10<=i.text.length-t.matchLength?m:s)())})})})},n={onKeypress:pp(function(t){27!==t.which&&g()},50),cancelIfNecessary:m,isMenuOpen:function(){return rp.isOpen(d)},isActive:e,isProcessingAction:l.get,getView:function(){return rp.getContent(d)}};function i(t,n){vr(t,yi(),{raw:n})}!1===f.hasPlugin("rtc")&&(o=n,(t=f).on("keypress compositionend",o.onKeypress.throttle),t.on("remove",o.onKeypress.cancel),t.on("keydown",function(n){function t(){return o.getView().bind(gd.getHighlighted)}8===n.which&&o.onKeypress.throttle(n),o.isActive()&&(27===n.which&&o.cancelIfNecessary(),o.isMenuOpen()?13===n.which?(t().each(br),n.preventDefault()):40===n.which?(t().fold(function(){o.getView().each(gd.highlightFirst)},function(t){i(t,n)}),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){i(t,n),n.preventDefault(),n.stopImmediatePropagation()}):13!==n.which&&38!==n.which&&40!==n.which||o.cancelIfNecessary())}),t.on("NodeChange",function(t){o.isActive()&&!o.isProcessingAction()&&Dp(At.fromDom(t.element)).isNone()&&o.cancelIfNecessary()}))}var oy=Po([{stopped:[]},{resume:["element"]},{complete:[]}]),ry=function(n,e,o,t,r,i){return Kb(n,e,o,t,r,i).fold(_,function(t){return ry(n,e,o,t,r,i)},O)},iy=function(t,n,e,o,r){var i=qb(e,o);return ry(t,n,e,o,i,r)},uy=wl({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=s(n,["attributes"]);return{uid:t.uid,dom:lt({tag:"div",attributes:lt({role:"presentation"},e)},o),components:t.components,behaviours:Qs(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[To("components",[]),$s("containerBehaviours",[]),To("events",{}),To("domModification",{}),To("eventOrder",{})]}),ay=rt([To("prefix","form-field"),$s("fieldBehaviours",[ud,Tf])]),cy=rt([Jf({schema:[fo("dom")],name:"label"}),Jf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[fo("text")],name:"aria-descriptor"}),qf({factory:{sketch:function(t){var e,o,n=(e=["factory"],o={},J(t,function(t,n){wt(e,n)||(o[n]=t)}),o);return t.factory.sketch(n)}},schema:[fo("factory")],name:"field"})]),sy=Sl({name:"FormField",configFields:ay(),partFields:cy(),factory:function(r,t,n,e){var o=Zs(r.fieldBehaviours,[ud.config({find:function(t){return fl(t,r,"field")}}),Tf.config({store:{mode:"manual",getValue:function(t){return ud.getCurrent(t).bind(Tf.getValue)},setValue:function(t,n){ud.getCurrent(t).each(function(t){Tf.setValue(t,n)})}}})]),i=nu([eu(function(t,n){var o=ml(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=Fr(r.prefix);o.label().each(function(t){on(t.element,"for",n),on(e.element,"id",n)}),o["aria-descriptor"]().each(function(t){var n=Fr(r.prefix);on(t.element,"id",n),on(e.element,"aria-describedby",n)})})})]);return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:{getField:function(t){return fl(t,r,"field")},getLabel:function(t){return fl(t,r,"label")}}}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),ly=Object.freeze({__proto__:null,exhibit:function(t,n){return Nr({attributes:sr([{key:n.tabAttr,value:"true"}])})}}),fy=ya({fields:[To("tabAttr","data-alloy-tabstop")],name:"tabstopping",active:ly}),dy=tinymce.util.Tools.resolve("tinymce.html.Entities"),my=function(t,n,e,o){return{dom:gy(e),components:t.toArray().concat([n]),fieldBehaviours:tc(o)}},gy=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},py=Fr("form-component-change"),hy=Fr("form-close"),vy=Fr("form-cancel"),by=Fr("form-action"),yy=Fr("form-submit"),xy=Fr("form-block"),wy=Fr("form-unblock"),Sy=Fr("form-tabchange"),Cy=Fr("form-resize"),ky=rt([xo("data"),To("inputAttributes",{}),To("inputStyles",{}),To("tag","input"),To("inputClasses",[]),Ku("onSetValue"),To("styles",{}),To("eventOrder",{}),$s("inputBehaviours",[Tf,Vg]),To("selectOnFocus",!0)]),Oy=wl({name:"Input",configFields:ky(),factory:function(t,n){return{uid:t.uid,dom:ny(t),components:[],behaviours:lt(lt({},ty(e=t)),Zs(e.inputBehaviours,[Tf.config({store:lt(lt({mode:"manual"},e.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return $r(t.element)},setValue:function(t,n){$r(t.element)!==n&&Qr(t.element,n)}}),onSetValue:e.onSetValue})])),eventOrder:t.eventOrder};var e}}),_y={},Ty={exports:_y};function Ey(t){setTimeout(function(){throw t},0)}function Dy(t){var n=Ft(t);return wt(Wy,n)}function By(t,n){Yr(n.getRoot(t).getOr(t.element),n.invalidClass),n.notify.each(function(n){Dy(t.element)&&on(t.element,"aria-invalid",!1),n.getContainer(t).each(function(t){Mr(t,n.validHtml)}),n.onValid(t)})}function My(n,t,e,o){Xr(t.getRoot(n).getOr(n.element),t.invalidClass),t.notify.each(function(t){Dy(n.element)&&on(n.element,"aria-invalid",!0),t.getContainer(n).each(function(t){Mr(t,o)}),t.onInvalid(n,o)})}function Ay(n,t,e){return t.validator.fold(function(){return Ly(Re.value(!0))},function(t){return t.validate(n)})}function Fy(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),Ay(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return My(n,e,0,t),Re.error(t)},function(t){return By(n,e),Re.value(t)}):Re.error("No longer in system")})}!function(){var t=this,n=function(){var t,n,e,o={exports:{}};function r(){}function i(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],f(t,this)}function u(e,o){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,i._immediateFn(function(){var t,n=1===e._state?o.onFulfilled:o.onRejected;if(null!==n){try{t=n(e._value)}catch(t){return void c(o.promise,t)}a(o.promise,t)}else(1===e._state?a:c)(o.promise,e._value)})):e._deferreds.push(o)}function a(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void s(n);if("function"==typeof e)return void f((o=e,r=t,function(){o.apply(r,arguments)}),n)}n._state=1,n._value=t,s(n)}catch(t){c(n,t)}var o,r}function c(t,n){t._state=2,t._value=n,s(t)}function s(t){2===t._state&&0===t._deferreds.length&&i._immediateFn(function(){t._handled||i._unhandledRejectionFn(t._value)});for(var n=0,e=t._deferreds.length;n<e;n++)u(t,t._deferreds[n]);t._deferreds=null}function l(t,n,e){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=e}function f(t,n){var e=!1;try{t(function(t){e||(e=!0,a(n,t))},function(t){e||(e=!0,c(n,t))})}catch(t){if(e)return;e=!0,c(n,t)}}t=o,n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=setTimeout,i.prototype.catch=function(t){return this.then(null,t)},i.prototype.then=function(t,n){var e=new this.constructor(r);return u(this,new l(t,n,e)),e},i.all=function(t){var a=Array.prototype.slice.call(t);return new i(function(r,i){if(0===a.length)return r([]);for(var u=a.length,t=0;t<a.length;t++)!function n(e,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var o=t.then;if("function"==typeof o)return o.call(t,function(t){n(e,t)},i),0}a[e]=t,0==--u&&r(a)}catch(t){i(t)}}(t,a[t])})},i.resolve=function(n){return n&&"object"==typeof n&&n.constructor===i?n:new i(function(t){t(n)})},i.reject=function(e){return new i(function(t,n){n(e)})},i.race=function(r){return new i(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},i._immediateFn="function"==typeof setImmediate?function(t){setImmediate(t)}:function(t){e(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},t.exports?t.exports=i:n.Promise||(n.Promise=i);var d=o.exports;return{boltExport:("undefined"!=typeof window?window:Function("return this;")()).Promise||d}};"object"==typeof _y&&void 0!==Ty?Ty.exports=n():(t="undefined"!=typeof globalThis?globalThis:t||self).EphoxContactWrapper=n()}();var Iy,Ry,Vy=Ty.exports.boltExport,Py=function(t){function o(t){r()?i(t):n.push(t)}var e=vt.none(),n=[],r=function(){return e.isSome()},i=function(n){e.each(function(t){setTimeout(function(){n(t)},0)})};return t(function(t){r()||(e=vt.some(t),St(n,i),n=[])}),{get:o,map:function(e){return Py(function(n){o(function(t){n(e(t))})})},isReady:r}},Hy={nu:Py,pure:function(n){return Py(function(t){t(n)})}},zy=function(e){function t(t){e().then(t,Ey)}return{map:function(t){return zy(function(){return e().then(t)})},bind:function(n){return zy(function(){return e().then(function(t){return n(t).toPromise()})})},anonBind:function(t){return zy(function(){return e().then(function(){return t.toPromise()})})},toLazy:function(){return Hy.nu(t)},toCached:function(){var t=null;return zy(function(){return t=null===t?e():t})},toPromise:e,get:t}},Ny=function(t){return zy(function(){return new Vy(t)})},Ly=function(t){return zy(function(){return Vy.resolve(t)})},Wy=["input","textarea"],Uy=Object.freeze({__proto__:null,markValid:By,markInvalid:My,query:Ay,run:Fy,isInvalid:function(t,n){return qr(n.getRoot(t).getOr(t.element),n.invalidClass)}}),jy=Object.freeze({__proto__:null,events:function(n,t){return n.validator.map(function(t){return nu([Cr(t.onEvent,function(t){Fy(t,n).get(h)})].concat(t.validateOnLoad?[eu(function(t){Fy(t,n).get(st)})]:[]))}).getOr({})}}),Gy=ya({fields:[fo("invalidClass"),To("getRoot",vt.none),_o("notify",[To("aria","alert"),To("getContainer",vt.none),To("validHtml",""),Ku("onValid"),Ku("onInvalid"),Ku("onValidate")]),_o("validator",[fo("validate"),To("onEvent","input"),To("validateOnLoad",!0)])],name:"invalidating",active:jy,apis:Uy,extra:{validation:function(e){return function(t){var n=Tf.getValue(t);return Ly(e(n))}}}}),Xy=Object.freeze({__proto__:null,getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),Yy=ya({fields:[mo("others",io(Re.value,Qo()))],name:"coupling",apis:Xy,state:Object.freeze({__proto__:null,init:function(){var i={},t=rt({});return xu({readState:t,getOrCreate:function(e,o,r){var t=kt(o.others);if(t)return tt(i,r).getOrThunk(function(){var t=tt(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),qy=rt("sink"),Ky=rt(Jf({name:qy(),overrides:rt({dom:{tag:"div"},behaviours:tc([sf.config({useFixed:_})]),events:nu([Er(yi()),Er(di()),Er(Ci())])})}));function Jy(t,n){var e=t.getHotspot(n).getOr(n),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{type:"hotspot",hotspot:e,overrides:o}},function(t){return{type:"hotspot",hotspot:e,overrides:o,layouts:t}})}function $y(t,n,e,o,r,i,u){var a,c=Jy(t,e),s=e,l=o,f=r,d=u,m=n,g=(0,(a=t).fetch)(s).map(m),p=sw(s,a);return g.map(function(t){return t.bind(function(t){return vt.from(op.sketch(lt(lt({},f.menu()),{uid:gu(""),data:t,highlightImmediately:d===Iy.HighlightFirst,onOpenMenu:function(t,n){var e=p().getOrDie();sf.position(e,n,{anchor:c}),hf.decloak(l)},onOpenSubmenu:function(t,n,e){var o=p().getOrDie();sf.position(o,e,{anchor:{type:"submenu",item:n}}),hf.decloak(l)},onRepositionMenu:function(t,n,e){var o=p().getOrDie();sf.position(o,n,{anchor:c}),St(e,function(t){sf.position(o,t.triggeredMenu,{anchor:{type:"submenu",item:t.triggeringItem}})})},onEscape:function(){return Vg.focus(s),hf.close(l),vt.some(!0)}})))})}).map(function(t){return t.fold(function(){hf.isOpen(o)&&hf.close(o)},function(t){hf.cloak(o),hf.open(o,t),i(o)}),o})}function Qy(t,n,e,o,r,i){var u=Yy.getCoupled(e,"sandbox");return(hf.isOpen(u)?function(t,n,e,o,r,i,u){return hf.close(o),Ly(o)}:$y)(t,n,e,u,o,r,i)}function Zy(t){hf.getState(t).each(function(t){op.repositionMenus(t)})}function tx(s,l,f){var d=Ru(),t=sw(l,s);return{dom:{tag:"div",classes:s.sandboxClasses,attributes:{id:d.id,role:"listbox"}},behaviours:Df(s.sandboxBehaviours,[Tf.config({store:{mode:"memory",initialValue:l}}),hf.config({onOpen:function(t,n){var e,o,r,i,u,a,c=Jy(s,l);d.link(l.element),s.matchWidth&&(e=c.hotspot,o=n,r=s.useMinWidth,u=ud.getCurrent(o).getOr(o),a=kn(e.element),r?fn(u.element,"min-width",a+"px"):(i=u.element,ke.set(i,a))),s.onOpen(c,t,n),void 0!==f&&void 0!==f.onOpen&&f.onOpen(t,n)},onClose:function(t,n){d.unlink(l.element),void 0!==f&&void 0!==f.onClose&&f.onClose(t,n)},isPartOf:function(t,n,e){return Hu(n,e)||Hu(l,e)},getAttachPoint:function(){return t().getOrDie()}}),ud.config({find:function(t){return hf.getState(t).bind(function(t){return ud.getCurrent(t)})}}),rc.config({channels:lt(lt({},js({isExtraPart:O})),Gs({doReposition:Zy}))})])}}function nx(t){Zy(Yy.getCoupled(t,"sandbox"))}function ex(){return[To("sandboxClasses",[]),Ef("sandboxBehaviours",[ud,rc,hf,Tf])]}function ox(n){return Jf({name:n+"-edge",overrides:function(t){return t.model.manager.edgeActions[n].fold(function(){return{}},function(o){return{events:nu([kr(ci(),function(t,n,e){return o(t,e)},[t]),kr(di(),function(t,n,e){return o(t,e)},[t]),kr(mi(),function(t,n,e){e.mouseIsDown.get()&&o(t,e)},[t])])}})}})}function rx(t){var n=t.event.raw;return-1===n.type.indexOf("touch")?void 0!==n.clientX?vt.some(n).map(function(t){return Se(t.clientX,t.clientY)}):vt.none():void 0!==n.touches&&1===n.touches.length?vt.some(n.touches[0]).map(function(t){return Se(t.clientX,t.clientY)}):vt.none()}function ix(t){return t.model.minX}function ux(t){return t.model.minY}function ax(t){return t.model.minX-1}function cx(t){return t.model.minY-1}function sx(t){return t.model.maxX}function lx(t){return t.model.maxY}function fx(t){return t.model.maxX+1}function dx(t){return t.model.maxY+1}function mx(t,n,e){return n(t)-e(t)}function gx(t){return mx(t,sx,ix)}function px(t){return mx(t,lx,ux)}function hx(t){return gx(t)/2}function vx(t){return px(t)/2}function bx(t){return t.stepSize}function yx(t){return t.snapToGrid}function xx(t){return t.snapStart}function wx(t){return t.rounded}function Sx(t,n){return void 0!==t[n+"-edge"]}function Cx(t){return Sx(t,"left")}function kx(t){return Sx(t,"right")}function Ox(t){return Sx(t,"top")}function _x(t){return Sx(t,"bottom")}function Tx(t){return t.model.value.get()}function Ex(t,n){return{x:t,y:n}}function Dx(t,n){vr(t,_w(),{value:n})}function Bx(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)}function Mx(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)}function Ax(t,n,e){return Math.max(n,Math.min(e,t))}function Fx(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=l?e+1:e;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w=Ax((x=f,Math.min(d,Math.max(r,x))-x)/m*o+n,g,p);return u&&n<=w&&w<=e?(h=w,v=n,b=e,y=i,a.fold(function(){var t=Math.round((h-v)/y)*y;return Ax(v+t,v-1,b+1)},function(t){var n=Math.round((h-t)%y/y),e=Math.floor((h-t)/y),o=Math.floor((b-t)/y),r=Math.min(o,e+n);return Math.max(t,t+r*y)})):c?Math.round(w):w}function Ix(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:l:(r-n)/o*c}function Rx(t){return t.element.dom.getBoundingClientRect()}function Vx(t){return Rx(t)[Tw]}function Px(t){return Rx(t).right}function Hx(t){return Rx(t).top}function zx(t){return Rx(t).bottom}function Nx(t){return Rx(t).width}function Lx(t){return Rx(t).height}function Wx(t,n){var e=Rx(t),o=Rx(n);return(e[Tw]+e.right)/2-o[Tw]}function Ux(t,n){var e=Rx(t),o=Rx(n);return(e.top+e.bottom)/2-o.top}function jx(t,n){vr(t,_w(),{value:n})}function Gx(t,n,e){return Fx({min:ix(n),max:sx(n),range:gx(n),value:e,step:bx(n),snap:yx(n),snapStart:xx(n),rounded:wx(n),hasMinEdge:Cx(n),hasMaxEdge:kx(n),minBound:Vx(t),maxBound:Px(t),screenRange:Nx(t)})}function Xx(r){return function(t,n){return jx(t,{x:o=(0<r?Mx:Bx)(Tx(e=n).x,ix(e),sx(e),bx(e))}),vt.some(o).map(_);var e,o}}function Yx(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g=(a=i,c=e,s=o,l=r,f=Nx(u=n),d=s.bind(function(t){return vt.some(Wx(t,u))}).getOr(0),m=l.bind(function(t){return vt.some(Wx(t,u))}).getOr(f),Ix({min:ix(a),max:sx(a),range:gx(a),value:c,hasMinEdge:Cx(a),hasMaxEdge:kx(a),minBound:Vx(u),minOffset:0,maxBound:Px(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m}));return Vx(n)-Vx(t)+g}function qx(t,n){vr(t,_w(),{value:n})}function Kx(t,n,e){return Fx({min:ux(n),max:lx(n),range:px(n),value:e,step:bx(n),snap:yx(n),snapStart:xx(n),rounded:wx(n),hasMinEdge:Ox(n),hasMaxEdge:_x(n),minBound:Hx(t),maxBound:zx(t),screenRange:Lx(t)})}function Jx(r){return function(t,n){return qx(t,{y:o=(0<r?Mx:Bx)(Tx(e=n).y,ux(e),lx(e),bx(e))}),vt.some(o).map(_);var e,o}}function $x(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g=(a=i,c=e,s=o,l=r,f=Lx(u=n),d=s.bind(function(t){return vt.some(Ux(t,u))}).getOr(0),m=l.bind(function(t){return vt.some(Ux(t,u))}).getOr(f),Ix({min:ux(a),max:lx(a),range:px(a),value:c,hasMinEdge:Ox(a),hasMaxEdge:_x(a),minBound:Hx(u),minOffset:0,maxBound:zx(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m}));return Hx(n)-Hx(t)+g}function Qx(t,n){vr(t,_w(),{value:n})}function Zx(t,n){return{x:t,y:n}}function t0(u,a){return function(t,n){return o=n,r=0<u?Mx:Bx,Qx(t,Zx(i=(e=a)?Tx(o).x:r(Tx(o).x,ix(o),sx(o),bx(o)),e?r(Tx(o).y,ux(o),lx(o),bx(o)):Tx(o).y)),vt.some(i).map(_);var e,o,r,i}}function n0(t){return"<alloy.field."+t+">"}function e0(f,d,m,g){function p(t,n,e,o,r){var i,u,a=f(eS+"range"),c=[sy.parts.label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),sy.parts.field({data:r,factory:Oy,inputAttributes:lt({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[d("textfield")],inputBehaviours:tc([(i=n,u=t,Gy.config({invalidClass:d("invalid"),notify:{onValidate:function(t){vr(t,nS,{type:i})},onValid:function(t){vr(t,Zw,{type:i,value:Tf.getValue(t)})},onInvalid:function(t){vr(t,tS,{type:i,value:Tf.getValue(t)})}},validator:{validate:function(t){var n=Tf.getValue(t),e=u(n)?Re.value(!0):Re.error(f("aria.input.invalid"));return Ly(e)},validateOnLoad:!1}})),fy.config({})]),onSetValue:function(t){Gy.isInvalid(t)&&Gy.run(t).get(st)}})],s="hex"!==n?[sy.parts["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}}function h(t,n){var e=n.red,o=n.green,r=n.blue;Tf.setValue(t,{red:e,green:o,blue:r})}function v(t,n){b.getOpt(t).each(function(t){fn(t.element,"background-color","#"+n.value)})}var b=Pm({dom:{tag:"div",classes:[d("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return wl({factory:function(){function a(t){return o[t].get()}function c(t,n){o[t].set(n)}function n(t,n){var e=n.event;"hex"!==e.type?c(e.type,vt.none()):g(t)}function e(t,n){var r,e,o,i,u=n.event;"hex"===u.type?function(t,n){m(t);var e=Dv(n);c("hex",vt.some(n));var o=Vv(e);h(t,o),s(o),vr(t,qw,{hex:e}),v(t,e)}(t,u.value):(r=t,e=u.type,o=u.value,i=parseInt(o,10),c(e,vt.some(i)),a("red").bind(function(e){return a("green").bind(function(n){return a("blue").map(function(t){return Fv(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=Av(t),Qw.getField(n,"hex").each(function(t){Vg.isFocused(t)||Tf.setValue(n,{hex:e.value})}),e);vr(r,qw,{hex:o}),v(r,o)}))}function t(t){return{label:f(eS+t+".label"),description:f(eS+t+".description")}}function s(t){var n=t.red,e=t.green,o=t.blue;c("red",vt.some(n)),c("green",vt.some(e)),c("blue",vt.some(o))}var o={red:Vo(vt.some(255)),green:Vo(vt.some(255)),blue:Vo(vt.some(255)),hex:Vo(vt.some("ffffff"))},r=t("red"),i=t("green"),u=t("blue"),l=t("hex");return Xo(Qw.sketch(function(t){return{dom:{tag:"form",classes:[d("rgb-form")],attributes:{"aria-label":f("aria.color.picker")}},components:[t.field("red",sy.sketch(p(Iv,"red",r.label,r.description,255))),t.field("green",sy.sketch(p(Iv,"green",i.label,i.description,255))),t.field("blue",sy.sketch(p(Iv,"blue",u.label,u.description,255))),t.field("hex",sy.sketch(p(Bv,"hex",l.label,l.description,"ffffff"))),b.asSpec()],formBehaviours:tc([Gy.config({invalidClass:d("form-invalid")}),fm("rgb-form-events",[Cr(Zw,e),Cr(tS,n),Cr(nS,n)])])}}),{apis:{updateHex:function(t,n){var e;Tf.setValue(t,{hex:n.value}),h(t,e=Vv(n)),s(e),v(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})}function o0(x,w){return wl({name:"ColourPicker",configFields:[fo("dom"),To("onValidHex",st),To("onInvalidHex",st)],factory:function(t){function n(t,n,e){v.getOpt(t).each(function(t){g.setHue(t,e)})}function e(t,n){b.getOpt(t).each(function(t){m.updateHex(t,n)})}function r(n,e,o,t){var r=o,i=Vv(e);p.paletteRgba.set(i),p.paletteHue.set(r),St(t,function(t){t(n,e,o)})}var o,i,u,a,c,s,l,f,d,m=e0(x,w,t.onValidHex,t.onInvalidHex),g=(l=w,f=Yw.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[l("sv-palette-spectrum")]}}),d=Yw.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[l("sv-palette-thumb")],innerHtml:"<div class="+l("sv-palette-inner-thumb")+' role="presentation"></div>'}}),wl({factory:function(t){var n=rt({x:0,y:0}),e=tc([ud.config({find:vt.some}),Vg.config({})]);return Yw.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[l("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[f,d],onChange:function(t,n,e){vr(t,Jw,{value:e})},onInit:function(t,n,e,o){y(e.element.dom,zv(sb))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:function(t,n,e){var o=e;y(n.components()[0].element.dom,zv(Rv(Lv(o,100,100))))},setThumb:function(t,n,e){var o=n,r=Wv(Vv(e));Yw.setValue(o,{x:r.saturation,y:100-r.value})}},extraApis:{}})),p={paletteRgba:Vo(sb),paletteHue:Vo(0)},h=Pm((i=Yw.parts.spectrum({dom:{tag:"div",classes:[(o=w)("hue-slider-spectrum")],attributes:{role:"presentation"}}}),u=Yw.parts.thumb({dom:{tag:"div",classes:[o("hue-slider-thumb")],attributes:{role:"presentation"}}}),Yw.sketch({dom:{tag:"div",classes:[o("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:rt({y:0})},components:[i,u],sliderBehaviours:tc([Vg.config({})]),onChange:function(t,n,e){vr(t,Kw,{value:e})}}))),v=Pm(g.sketch({})),b=Pm(m.sketch({}));function y(t,n){var e,o,r=t.width,i=t.height,u=t.getContext("2d");null!==u&&(u.fillStyle=n,u.fillRect(0,0,r,i),(e=u.createLinearGradient(0,0,r,0)).addColorStop(0,"rgba(255,255,255,1)"),e.addColorStop(1,"rgba(255,255,255,0)"),u.fillStyle=e,u.fillRect(0,0,r,i),(o=u.createLinearGradient(0,0,0,i)).addColorStop(0,"rgba(0,0,0,0)"),o.addColorStop(1,"rgba(0,0,0,1)"),u.fillStyle=o,u.fillRect(0,0,r,i))}return{uid:t.uid,dom:t.dom,components:[v.asSpec(),h.asSpec(),b.asSpec()],behaviours:tc([fm("colour-picker-events",[Cr(qw,(s=[n,function(t,n,e){h.getOpt(t).each(function(t){Yw.setValue(t,{y:100-e/360*100})})},function(t,n){v.getOpt(t).each(function(t){g.setThumb(t,n)})}],function(t,n){var e=n.event.hex;r(t,e,Wv(Vv(e)).hue,s)})),Cr(Jw,(c=[e],function(t,n){var e=n.event.value,o=p.paletteHue.get();r(t,Uv(Lv(o,e.x,100-e.y)),o,c)})),Cr(Kw,(a=[n,e],function(t,n){var e=(100-n.event.value.y)/100*360,o=Wv(p.paletteRgba.get());r(t,Uv(Lv(e,o.saturation,o.value)),e,a)}))]),ud.config({find:function(t){return b.getOpt(t)}}),Mg.config({mode:"acyclic"})])}}})}function r0(t){return iS[t]}function i0(t,n,e){return Tf.config(Xo({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))}function u0(r,i){function n(t,n){n.stop()}function e(t){return function(n,e){St(t,function(t){t(n,e)})}}function o(t,n){var e;dd.isDisabled(t)||(e=n.event.raw,a(t,e.dataTransfer.files))}function u(t,n){var e=n.event.raw.target;a(t,e.files)}function a(t,n){var e,o;Tf.setValue(t,(e=n,o=aS.explode(i.getSetting("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string")),F(Ct(e),function(n){return T(o,function(t){return Bt(n.name.toLowerCase(),"."+t.toLowerCase())})}))),vr(t,py,{name:r.name})}var c=Pm({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:tc([fm("input-file-events",[Er(Ci()),Er(Ri())])])});return Qb(r.label.map(function(t){return Zb(t,i)}),sy.parts.field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:tc([fS([]),ow(),dd.config({}),zg.config({toggleClass:"dragenter",toggleOnExecute:!1}),fm("dropzone-events",[Cr("dragenter",e([n,zg.toggle])),Cr("dragleave",e([n,zg.toggle])),Cr("dragover",n),Cr("drop",e([n,o])),Cr(Si(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:i.translate("Drop an image here")}},sp.sketch({dom:{tag:"button",innerHtml:i.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element.dom.click()},buttonBehaviours:tc([fy.config({}),Kv(i.isDisabled),mv()])})]}]}}}}),["tox-form__group--stretched"],[])}function a0(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:tc([Vg.config({ignore:!0}),fy.config({})])}}function c0(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[a0([dS]),t,a0([mS])],behaviours:tc([rS(1)])}}function s0(t,n){vr(t,yi(),{raw:{which:9,shiftKey:n}})}function l0(t,n){var e=n.element;qr(e,dS)?s0(t,!0):qr(e,mS)&&s0(t,!1)}function f0(t){return Ub(t,["."+dS,"."+mS].join(","),O)}function d0(t,n){return hS(document.createElement("canvas"),t,n)}function m0(t){var n=d0(t.width,t.height);return pS(n).drawImage(t,0,0),n}function g0(t){return t.naturalWidth||t.width}function p0(t){return t.naturalHeight||t.height}function h0(t,o,r){return o=o||"image/png",m(HTMLCanvasElement.prototype.toBlob)?new Vy(function(n,e){t.toBlob(function(t){t?n(t):e()},o,r)}):(g=t.toDataURL(o,r),new Vy(function(t,n){!function(){var t=g.split(","),n=/data:([^;]+)/.exec(t[0]);if(!n)return vt.none();for(var e=n[1],o=t[1],r=atob(o),i=r.length,u=Math.ceil(i/1024),a=new Array(u),c=0;c<u;++c){for(var s=1024*c,l=Math.min(1024+s,i),f=new Array(l-s),d=s,m=0;d<l;++m,++d)f[m]=r[d].charCodeAt(0);a[c]=new Uint8Array(f)}return vt.some(new Blob(a,{type:e}))}().fold(function(){n("uri is not base64: "+g)},t)}));var g}function v0(t,n,e){function o(n,e){return t.then(function(t){return t.toDataURL(n||"image/png",e)})}return{getType:rt(n.type),toBlob:function(){return Vy.resolve(n)},toDataURL:rt(e),toBase64:function(){return e.split(",")[1]},toAdjustedBlob:function(n,e){return t.then(function(t){return h0(t,n,e)})},toAdjustedDataURL:o,toAdjustedBase64:function(t,n){return o(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(m0)}}}function b0(n,t){return h0(n,t).then(function(t){return v0(Vy.resolve(n),t,n.toDataURL())})}function y0(n){return e=n,new Vy(function(t){var n=new FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)}).then(function(t){return v0((a=n,new Vy(function(t,n){function e(){r.removeEventListener("load",i),r.removeEventListener("error",u)}var o=URL.createObjectURL(a),r=new Image,i=function(){e(),t(r)},u=function(){e(),n("Unable to load data of type "+a.type+": "+o)};r.addEventListener("load",i),r.addEventListener("error",u),r.src=o,r.complete&&setTimeout(i,0)}).then(function(t){vS(t);var n=d0(g0(t),p0(t));return pS(n).drawImage(t,0,0),n})),n,t);var a});var e}function x0(t,n,e){var o="string"==typeof t?parseFloat(t):t;return e<o?o=e:o<n&&(o=n),o}function w0(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}function S0(t,n){for(var e=[],o=new Array(25),r=0;r<5;r++){for(var i=0;i<5;i++)e[i]=n[i+5*r];for(i=0;i<5;i++){for(var u=0,a=0;a<5;a++)u+=t[i+5*a]*e[a];o[i+5*r]=u}}return o}function C0(n,e){return n.toCanvas().then(function(t){return yS(t,n.getType(),e)})}function k0(e){return function(t,n){return C0(t,e(w0(),n))}}function O0(t,n){void 0===n&&(n=2);var e=Math.pow(10,n),o=Math.round(t*e);return Math.ceil(o/e)}function _0(t){return xS(t)}function T0(t){return CS(t)}function E0(t,n){return kS(t,n)}function D0(t,n){return wS(t,n)}function B0(t,n){return SS(t,n)}function M0(t,n){return o=n,(e=t).toCanvas().then(function(t){return ES(t,e.getType(),o)});var e,o}function A0(t,n){return o=n,(e=t).toCanvas().then(function(t){return TS(t,e.getType(),o)});var e,o}function F0(t,n,e){return jm(t,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:e},n)}function I0(t,n){return F0(t,n,[])}function R0(t,n){return F0(t,n,[Fg.config({})])}function V0(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:tc([Fg.config({})])}}function P0(n,e,o){function t(t,n){var e=Tf.getValue(t);return Vg.focus(e),vr(e,"keydown",{raw:n.event.raw}),dw.close(e),vt.some(!0)}var r=Vo(st),i=n.text.map(function(t){return Pm(V0(t,e,o.providers))}),u=n.icon.map(function(t){return Pm(R0(t,o.providers.icons))}),a=n.role.fold(function(){return{}},function(t){return{role:t}}),c=n.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}}),s=jm("chevron-down",{tag:"div",classes:[e+"__select-chevron"]},o.providers.icons);return Pm(dw.sketch(lt(lt(lt({},n.uid?{uid:n.uid}:{}),a),{dom:{tag:"button",classes:[e,e+"--select"].concat(M(n.classes,function(t){return e+"--"+t})),attributes:lt({},c)},components:Qv([u.map(function(t){return t.asSpec()}),i.map(function(t){return t.asSpec()}),vt.some(s)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:tc(H(H([],n.dropdownBehaviours,!0),[Kv(function(){return n.disabled||o.providers.isDisabled()}),mv(),mw.config({}),Fg.config({}),fm("dropdown-events",[pv(n,r),hv(n,r)]),fm("menubutton-update-display-text",[Cr(AS,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){Fg.set(t,[oi(o.providers.translate(e.event.text))])})}),Cr(FS,function(n,e){u.bind(function(t){return t.getOpt(n)}).each(function(t){Fg.set(t,[R0(e.event.icon,o.providers.icons)])})})])],!1)),eventOrder:Xo(MS,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:tc([Mg.config({mode:"special",onLeft:t,onRight:t})]),lazySink:o.getSink,toggleClass:e+"--active",parts:{menu:$p(0,n.columns,n.presets)},fetch:function(t){return Ny(S(n.fetch,t))}}))).asSpec()}function H0(t){return"separator"===t.type}function z0(t,e,o,n){var r=Fr("primary-menu"),i=RS(t,o.shared.providers.menuItems());if(0===i.items.length)return vt.none();var u=Lb(r,i.items,e,o,n),a=dt(i.menus,function(t,n){return Lb(n,t,e,o,!1)}),c=Xo(a,cr(r,u));return vt.from(op.tieredData(r,c,i.expansions))}function N0(e){return{isDisabled:function(){return dd.isDisabled(e)},setDisabled:function(t){return dd.set(e,t)},setActive:function(t){var n=e.element;t?(Xr(n,"tox-tbtn--enabled"),on(n,"aria-pressed",!0)):(Yr(n,"tox-tbtn--enabled"),cn(n,"aria-pressed"))},isActive:function(){return qr(e.element,"tox-tbtn--enabled")}}}function L0(e,t,o,n){return P0({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:function(t,n){e.fetch(function(t){n(z0(t,xh.CLOSE_ON_EXECUTE,o,!1))})},onSetup:e.onSetup,getApi:N0,columns:1,presets:"normal",classes:[],dropdownBehaviours:[fy.config({})]},t,o.shared)}function W0(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold(function(){return{}},function(t){return{action:t}}),a=lt({buttonBehaviours:tc([Kv(function(){return t.disabled||i.isDisabled()}),mv(),fy.config({}),fm("button press",[Sr("click"),Sr("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=Xo(a,{dom:o});return Xo(c,{components:r})}function U0(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return I0(t,e.icons)});return W0(t,n,o,r,Qv([i]),e)}function j0(t,n,e,o){void 0===o&&(o=[]);var r=U0(t,vt.some(n),e,o);return sp.sketch(r)}function G0(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map(function(t){return I0(t,e.icons)}):vt.none(),a=u.isSome()?Qv([u]):[],c=u.isSome()?{}:{innerHtml:i},s=H(H(H(H([],t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],!0),u.isSome()?["tox-button--icon"]:[],!0),t.borderless?["tox-button--naked"]:[],!0),r,!0);return W0(t,n,o,lt(lt({tag:"button",classes:s},c),{attributes:{title:i}}),a,e)}function X0(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=G0(t,vt.some(n),e,o,r);return sp.sketch(i)}function Y0(n,e){return function(t){"custom"===e?vr(t,by,{name:n,value:{}}):"submit"===e?hr(t,yy):"cancel"===e?hr(t,vy):console.error("Unknown button type: ",e)}}function q0(n,t,e){if("menu"===t){var o=n,r=Pm(L0(lt(lt({},n),{onSetup:function(t){return t.setDisabled(n.disabled),st},fetch:(i=o.items,u=function(){return r},a=e,function(t){t(M(i,function(t){var n,e,o=t.text.fold(function(){return{}},function(t){return{text:t}});return lt(lt({type:t.type,active:!1},o),{onAction:function(t){var n=!t.isActive();t.setActive(n),e.storage.set(n),a.shared.getSink().each(function(t){u().getOpt(t).each(function(t){Sa(t.element),vr(t,by,{name:e.name,value:e.storage.get()})})})},onSetup:(n=e=t,function(t){t.setActive(n.storage.get())})})}))})}),"tox-tbtn",e,vt.none()));return r.asSpec()}var i,u,a;if("custom"===t||"cancel"===t||"submit"===t){var c=Y0(n.name,t);return X0(lt(lt({},n),{borderless:!1}),c,e.shared.providers,[])}console.error("Unknown footer button type: ",t)}function K0(t,n){return qf({factory:sy,name:t,overrides:function(o){return{fieldBehaviours:tc([fm("coupled-input-behaviour",[Cr(wi(),function(e){fl(e,o,n).bind(ud.getCurrent).each(function(n){fl(e,o,"lock").each(function(t){zg.isOn(t)&&o.onLockedChange(e,n,t)})})})])])}}})}function J0(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return Re.error(t);var e=parseFloat(n[1]),o=n[2];return Re.value({value:e,unit:o})}function $0(t,n){function e(t){return Tt(o,t)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1};return t.unit===n?vt.some(t.value):e(t.unit)&&e(n)?o[t.unit]===o[n]?vt.some(t.value):vt.some(t.value/o[t.unit]*o[n]):vt.none()}function Q0(t){return vt.none()}function Z0(o,n){function t(t){return jm(t,{tag:"span",classes:["tox-icon","tox-lock-icon__"+t]},n.icons)}function e(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}}function r(e){return sy.parts.field({factory:Oy,inputClasses:["tox-textfield"],inputBehaviours:tc([dd.config({disabled:function(){return o.disabled||n.isDisabled()}}),mv(),fy.config({}),fm("size-input-events",[Cr(vi(),function(t,n){vr(t,u,{isField1:e})}),Cr(Si(),function(t,n){vr(t,py,{name:o.name})})])]),selectOnFocus:!1})}function i(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}}var l=Q0,u=Fr("ratio-event"),a=HS.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[t("lock"),t("unlock")],buttonBehaviours:tc([dd.config({disabled:function(){return o.disabled||n.isDisabled()}}),mv(),fy.config({})])}),c=HS.parts.field1(e([sy.parts.label(i("Width")),r(!0)])),s=HS.parts.field2(e([sy.parts.label(i("Height")),r(!1)]));return HS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([i("&nbsp;"),a])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){J0(Tf.getValue(t)).each(function(t){l(t).each(function(t){var n,e,o,r;Tf.setValue(i,(r=-1!==(r=(n=t).value.toFixed((e=n.unit)in(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4})?o[e]:1)).indexOf(".")?r.replace(/\.?0*$/,""):r)+n.unit)})})},coupledFieldBehaviours:tc([dd.config({disabled:function(){return o.disabled||n.isDisabled()},onDisabled:function(t){HS.getField1(t).bind(sy.getField).each(dd.disable),HS.getField2(t).bind(sy.getField).each(dd.disable),HS.getLock(t).each(dd.disable)},onEnabled:function(t){HS.getField1(t).bind(sy.getField).each(dd.enable),HS.getField2(t).bind(sy.getField).each(dd.enable),HS.getLock(t).each(dd.enable)}}),mv(),fm("size-input-events2",[Cr(u,function(t,n){var e,o,r,i=n.event.isField1,u=i?HS.getField1(t):HS.getField2(t),a=i?HS.getField2(t):HS.getField1(t),c=u.map(Tf.getValue).getOr(""),s=a.map(Tf.getValue).getOr("");e=s,o=J0(c).toOptional(),r=J0(e).toOptional(),l=Et(o,r,function(t,o){return $0(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return $0(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(Q0)}).getOr(Q0)})])])})}function tw(f,c){function t(t,n,e,o){return Pm(X0({name:t,text:t,disabled:e,primary:o,icon:vt.none(),borderless:!1},n,c))}function n(t,n,e,o){return Pm(j0({name:t,icon:vt.some(t),tooltip:vt.some(n),disabled:o,primary:!1,borderless:!1},e,c))}function d(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(dd)&&dd.disable(n)})}function m(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(dd)&&dd.enable(n)})}function r(t,n,e){vr(t,n,e)}function i(t){return hr(t,US.disable()),0}function u(t){return hr(t,US.enable()),0}function g(t,n){i(t),r(t,zS.transform(),{transform:n}),u(t)}function e(t){return function(){q.getOpt(t).each(function(t){Fg.set(t,[Y])})}}function s(t,n){i(t),r(t,zS.transformApply(),{transform:n,swap:e(t)}),u(t)}function p(){return t("Back",function(t){return r(t,zS.back(),{swap:e(t)})},!1,!1)}function o(){return Pm({dom:{tag:"div",classes:["tox-spacer"]},behaviours:tc([dd.config({})])})}function h(){return t("Apply",function(t){return r(t,zS.apply(),{swap:e(t)})},!0,!0)}function v(n,e){return function(t){return n(t,e)}}function a(t,n){var e,o=n;i(e=t),r(e,zS.tempTransform(),{transform:o}),u(e)}function b(t,n,e,o,r){var i=Yw.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=Yw.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=Yw.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Pm(Yw.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:rt({x:o})},components:[i,u,a],sliderBehaviours:tc([Vg.config({})]),onChoose:n}))}function l(t,n,e,o,r){var i,u,a,c,s,l,f=(i=t,u=n,a=e,c=o,s=r,[p(),(l=u,b(i,function(t,n,e){g(t,v(l,e.x/100))},a,c,s)),h()]);return uy.sketch({dom:w,components:f.map(function(t){return t.asSpec()}),containerBehaviours:tc([fm("image-tools-filter-panel-buttons-events",[Cr(US.disable(),function(t,n){d(f,t)}),Cr(US.enable(),function(t,n){m(f,t)})])])})}function y(t){return b(t,function(l,t,n){var e=z.getOpt(l),o=L.getOpt(l),r=N.getOpt(l);e.each(function(s){o.each(function(c){r.each(function(t){var o,r,i,n=Tf.getValue(s).x/100,e=Tf.getValue(t).x/100,u=Tf.getValue(c).x/100,a=(o=n,r=e,i=u,function(t){return C0(t,(n=r,e=i,S0(w0(),[x0(o,0,2),0,0,0,0,0,x0(n,0,2),0,0,0,0,0,x0(e,0,2),0,0,0,0,0,1,0,0,0,0,0,1])));var n,e});g(l,a)})})})},0,100,200)}function x(n,e,o){return function(t){r(t,zS.swap(),{transform:e,swap:function(){q.getOpt(t).each(function(t){Fg.set(t,[n]),o(t)})}})}}var w={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},S=st,C=[p(),o(),t("Apply",function(t){s(t,function(t){var n,e,o,r,i,u,a,c,s,l=f.getRect();return n=l.x,e=l.y,o=l.w,r=l.h,u=n,a=e,c=o,s=r,(i=t).toCanvas().then(function(t){return DS(t,i.getType(),u,a,c,s)})}),f.hideCrop()},!1,!0)],k=uy.sketch({dom:w,components:C.map(function(t){return t.asSpec()}),containerBehaviours:tc([fm("image-tools-crop-buttons-events",[Cr(US.disable(),function(t,n){d(C,t)}),Cr(US.enable(),function(t,n){m(C,t)})])])}),O=Pm(Z0({name:"size",label:vt.none(),constrain:!0,disabled:!1},c)),_=[p(),o(),O,o(),t("Apply",function(a){O.getOpt(a).each(function(t){var r,i,n=Tf.getValue(t),e=parseInt(n.width,10),o=parseInt(n.height,10),u=(r=e,i=o,function(t){return e=r,o=i,(n=t).toCanvas().then(function(t){return OS(t,e,o).then(function(t){return b0(t,n.getType())})});var n,e,o});s(a,u)})},!1,!0)],T=uy.sketch({dom:w,components:_.map(function(t){return t.asSpec()}),containerBehaviours:tc([fm("image-tools-resize-buttons-events",[Cr(US.disable(),function(t,n){d(_,t)}),Cr(US.enable(),function(t,n){m(_,t)})])])}),E=v(M0,"h"),D=v(M0,"v"),B=v(A0,-90),M=v(A0,90),A=[p(),o(),n("flip-horizontally","Flip horizontally",function(t){a(t,E)},!1),n("flip-vertically","Flip vertically",function(t){a(t,D)},!1),n("rotate-left","Rotate counterclockwise",function(t){a(t,B)},!1),n("rotate-right","Rotate clockwise",function(t){a(t,M)},!1),o(),h()],F=uy.sketch({dom:w,components:A.map(function(t){return t.asSpec()}),containerBehaviours:tc([fm("image-tools-fliprotate-buttons-events",[Cr(US.disable(),function(t,n){d(A,t)}),Cr(US.enable(),function(t,n){m(A,t)})])])}),I=[p(),o(),h()],R=uy.sketch({dom:w,components:I.map(function(t){return t.asSpec()})}),V=l("Brightness",D0,-100,0,100),P=l("Contrast",B0,-100,0,100),H=l("Gamma",E0,-100,0,100),z=y("R"),N=y("G"),L=y("B"),W=[p(),z,N,L,h()],U=uy.sketch({dom:w,components:W.map(function(t){return t.asSpec()})}),j=vt.some(T0),G=vt.some(_0),X=[n("crop","Crop",x(k,vt.none(),function(t){f.showCrop()}),!1),n("resize","Resize",x(T,vt.none(),function(t){O.getOpt(t).each(function(t){var n=f.getMeasurements(),e=n.width,o=n.height;Tf.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",x(F,vt.none(),S),!1),n("brightness","Brightness",x(V,vt.none(),S),!1),n("sharpen","Sharpen",x(R,j,S),!1),n("contrast","Contrast",x(P,vt.none(),S),!1),n("color-levels","Color levels",x(U,vt.none(),S),!1),n("gamma","Gamma",x(H,vt.none(),S),!1),n("invert","Invert",x(R,G,S),!1)],Y=uy.sketch({dom:w,components:X.map(function(t){return t.asSpec()})}),q=Pm(uy.sketch({dom:{tag:"div"},components:[Y],containerBehaviours:tc([Fg.config({})])}));return{memContainer:q,getApplyButton:function(t){return q.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}}function nw(t){var n,e;if(t.changedTouches)for(n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]}(Ry=Iy=Iy||{})[Ry.HighlightFirst=0]="HighlightFirst",Ry[Ry.HighlightNone=1]="HighlightNone";function ew(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:Zs(o.formBehaviours,[Tf.config({store:{mode:"manual",getValue:function(t){return dt(gl(t,o),function(t,o){return t().bind(function(t){var n=ud.getCurrent(t),e=new Error("Cannot find a current component to extract the value from for form part '"+o+"': "+Ar(t.element));return n.fold(function(){return Re.error(e)},Re.value)}).map(Tf.getValue)})},setValue:function(e,t){J(t,function(n,t){fl(e,o,t).each(function(t){ud.getCurrent(t).each(function(t){Tf.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return fl(t,o,n).bind(ud.getCurrent)}}}}function ow(){return ud.config({find:vt.some})}function rw(t){return n=Br,e=Mr,i0(t,function(t){return n(t.element)},function(t,n){return e(t.element,n)});var n,e}var iw,uw,aw,cw,sw=function(n,t){return n.getSystem().getByUid(t.uid+"-"+qy()).map(function(t){return function(){return Re.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return Re.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},lw=rt([fo("dom"),fo("fetch"),Ku("onOpen"),Ju("onExecute"),To("getHotspot",vt.some),To("getAnchorOverrides",rt({})),Nc(),$s("dropdownBehaviours",[zg,Yy,Mg,Vg]),fo("toggleClass"),To("eventOrder",{}),xo("lazySink"),To("matchWidth",!1),To("useMinWidth",!1),xo("role")].concat(ex())),fw=rt([Kf({schema:[Xu()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),Ky()]),dw=Sl({name:"Dropdown",configFields:lw(),partFields:fw(),factory:function(n,t,e,o){function r(t){hf.getState(t).each(function(t){op.highlightPrimary(t)})}function i(t,n){return br(t),vt.some(!0)}var u,a={expand:function(t){zg.isOn(t)||Qy(n,h,t,o,st,Iy.HighlightNone).get(st)},open:function(t){zg.isOn(t)||Qy(n,h,t,o,st,Iy.HighlightFirst).get(st)},isOpen:zg.isOn,close:function(t){zg.isOn(t)&&Qy(n,h,t,o,st,Iy.HighlightFirst).get(st)},repositionMenus:function(t){zg.isOn(t)&&nx(t)}};return{uid:n.uid,dom:n.dom,components:t,behaviours:Zs(n.dropdownBehaviours,[zg.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),Yy.config({others:{sandbox:function(t){return tx(n,t,{onOpen:function(){return zg.on(t)},onClose:function(){return zg.off(t)}})}}}),Mg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(t,n){return dw.isOpen(t)?r(Yy.getCoupled(t,"sandbox")):dw.open(t),vt.some(!0)},onEscape:function(t,n){return dw.isOpen(t)?(dw.close(t),vt.some(!0)):vt.none()}}),Vg.config({})]),events:xm(vt.some(function(t){Qy(n,h,t,o,r,Iy.HighlightFirst).get(st)})),eventOrder:lt(lt({},n.eventOrder),((u={})[Fi()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:a,domModification:{attributes:lt(lt({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}})),"button"===n.dom.tag?{type:tt(n.dom,"attributes").bind(function(t){return tt(t,"type")}).getOr("button")}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),mw=ya({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:function(){return nu([wr(Ti(),_)])},exhibit:function(){return Nr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),gw=Fr("color-input-change"),pw=Fr("color-swatch-change"),hw=Fr("color-picker-cancel"),vw=Jf({schema:[fo("dom")],name:"label"}),bw=ox("top-left"),yw=ox("top"),xw=ox("top-right"),ww=ox("right"),Sw=ox("bottom-right"),Cw=ox("bottom"),kw=ox("bottom-left"),Ow=[vw,ox("left"),ww,yw,Cw,bw,xw,kw,Sw,qf({name:"thumb",defaults:rt({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:nu([_r(ci(),t,"spectrum"),_r(si(),t,"spectrum"),_r(li(),t,"spectrum"),_r(di(),t,"spectrum"),_r(mi(),t,"spectrum"),_r(pi(),t,"spectrum")])}}}),qf({schema:[ar("mouseIsDown",function(){return Vo(!1)})],name:"spectrum",overrides:function(e){function o(n,t){return r.getValueFromEvent(t).map(function(t){return r.setValueFrom(n,e,t)})}var r=e.model.manager;return{behaviours:tc([Mg.config({mode:"special",onLeft:function(t){return r.onLeft(t,e)},onRight:function(t){return r.onRight(t,e)},onUp:function(t){return r.onUp(t,e)},onDown:function(t){return r.onDown(t,e)}}),Vg.config({})]),events:nu([Cr(ci(),o),Cr(si(),o),Cr(di(),o),Cr(mi(),function(t,n){e.mouseIsDown.get()&&o(t,n)})])}}})],_w=rt("slider.change.value"),Tw="left",Ew=Xx(-1),Dw=Xx(1),Bw=vt.none,Mw=vt.none,Aw={"top-left":vt.none(),top:vt.none(),"top-right":vt.none(),right:vt.some(function(t,n){Dx(t,{x:fx(n)})}),"bottom-right":vt.none(),bottom:vt.none(),"bottom-left":vt.none(),left:vt.some(function(t,n){Dx(t,{x:ax(n)})})},Fw=Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=Gx(t,n,e);return jx(t,{x:o}),o},setToMin:function(t,n){jx(t,{x:ix(n)})},setToMax:function(t,n){jx(t,{x:sx(n)})},findValueOfOffset:Gx,getValueFromEvent:function(t){return rx(t).map(function(t){return t.left})},findPositionOfValue:Yx,setPositionFromValue:function(t,n,e,o){var r=Tx(e),i=Yx(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=kn(n.element)/2;fn(n.element,"left",i-u+"px")},onLeft:Ew,onRight:Dw,onUp:Bw,onDown:Mw,edgeActions:Aw}),Iw=vt.none,Rw=vt.none,Vw=Jx(-1),Pw=Jx(1),Hw={"top-left":vt.none(),top:vt.some(function(t,n){Dx(t,{y:cx(n)})}),"top-right":vt.none(),right:vt.none(),"bottom-right":vt.none(),bottom:vt.some(function(t,n){Dx(t,{y:dx(n)})}),"bottom-left":vt.none(),left:vt.none()},zw=Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=Kx(t,n,e);return qx(t,{y:o}),o},setToMin:function(t,n){qx(t,{y:ux(n)})},setToMax:function(t,n){qx(t,{y:lx(n)})},findValueOfOffset:Kx,getValueFromEvent:function(t){return rx(t).map(function(t){return t.top})},findPositionOfValue:$x,setPositionFromValue:function(t,n,e,o){var r=Tx(e),i=$x(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),u=xn(n.element)/2;fn(n.element,"top",i-u+"px")},onLeft:Iw,onRight:Rw,onUp:Vw,onDown:Pw,edgeActions:Hw}),Nw=rx,Lw=t0(-1,!1),Ww=t0(1,!1),Uw=t0(-1,!0),jw=t0(1,!0),Gw={"top-left":vt.some(function(t,n){Dx(t,Ex(ax(n),cx(n)))}),top:vt.some(function(t,n){Dx(t,Ex(hx(n),cx(n)))}),"top-right":vt.some(function(t,n){Dx(t,Ex(fx(n),cx(n)))}),right:vt.some(function(t,n){Dx(t,Ex(fx(n),vx(n)))}),"bottom-right":vt.some(function(t,n){Dx(t,Ex(fx(n),dx(n)))}),bottom:vt.some(function(t,n){Dx(t,Ex(hx(n),dx(n)))}),"bottom-left":vt.some(function(t,n){Dx(t,Ex(ax(n),dx(n)))}),left:vt.some(function(t,n){Dx(t,Ex(ax(n),vx(n)))})},Xw=Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=Zx(Gx(t,n,e.left),Kx(t,n,e.top));return Qx(t,o),o},setToMin:function(t,n){Qx(t,Zx(ix(n),ux(n)))},setToMax:function(t,n){Qx(t,Zx(sx(n),lx(n)))},getValueFromEvent:Nw,setPositionFromValue:function(t,n,e,o){var r=Tx(e),i=Yx(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=$x(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),a=kn(n.element)/2,c=xn(n.element)/2;fn(n.element,"left",i-a+"px"),fn(n.element,"top",u-c+"px")},onLeft:Lw,onRight:Ww,onUp:Uw,onDown:jw,edgeActions:Gw}),Yw=Sl({name:"Slider",configFields:[To("stepSize",1),To("onChange",st),To("onChoose",st),To("onInit",st),To("onDragStart",st),To("onDragEnd",st),To("snapToGrid",!1),To("rounded",!0),xo("snapStart"),mo("model",so("mode",{x:[To("minX",0),To("maxX",100),ar("value",function(t){return Vo(t.mode.minX)}),fo("getInitialValue"),Zu("manager",Fw)],y:[To("minY",0),To("maxY",100),ar("value",function(t){return Vo(t.mode.minY)}),fo("getInitialValue"),Zu("manager",zw)],xy:[To("minX",0),To("maxX",100),To("minY",0),To("maxY",100),ar("value",function(t){return Vo({x:t.mode.minX,y:t.mode.minY})}),fo("getInitialValue"),Zu("manager",Xw)]})),$s("sliderBehaviours",[Mg,Tf]),ar("mouseIsDown",function(){return Vo(!1)})],partFields:Ow,factory:function(i,t,n,e){function u(t){return dl(t,i,"thumb")}function a(t){return dl(t,i,"spectrum")}function o(t){return fl(t,i,"left-edge")}function r(t){return fl(t,i,"right-edge")}function c(t){return fl(t,i,"top-edge")}function s(t){return fl(t,i,"bottom-edge")}function l(t,n){v.setPositionFromValue(t,n,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function f(t,n){h.value.set(n),l(t,u(t))}function d(e){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&fl(e,i,"thumb").each(function(t){var n=h.value.get();i.onChoose(e,t,n)})}function m(t,n){n.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))}function g(t,n){n.stop(),i.onDragEnd(t,u(t)),d(t)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:t,behaviours:Zs(i.sliderBehaviours,[Mg.config({mode:"special",focusIn:function(t){return fl(t,i,"spectrum").map(Mg.focusIn).map(_)}}),Tf.config({store:{mode:"manual",getValue:function(t){return h.value.get()}}}),rc.config({channels:((p={})[yf()]={onReceive:d},p)})]),events:nu([Cr(_w(),function(t,n){!function(t,n){f(t,n);var e=u(t);i.onChange(t,e,n),vt.some(!0)}(t,n.event.value)}),eu(function(t,n){var e=h.getInitialValue();h.value.set(e);var o=u(t);l(t,o);var r=a(t);i.onInit(t,o,r,h.value.get())}),Cr(ci(),m),Cr(li(),g),Cr(di(),m),Cr(pi(),g)]),apis:{resetToMin:function(t){v.setToMin(t,i)},resetToMax:function(t){v.setToMax(t,i)},setValue:f,refresh:l},domModification:{styles:{position:"relative"}}}},apis:{setValue:function(t,n,e){t.setValue(n,e)},resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),qw=Fr("rgb-hex-update"),Kw=Fr("slider-update"),Jw=Fr("palette-update"),$w=[$s("formBehaviours",[Tf])],Qw={getField:Hr(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n={field:function(t,n){return e.push(t),ul("form",n0(t),n)},record:rt(e=[])},o=t(n),r=M(n.record(),function(t){return qf({name:t,pname:n0(t)})});return xl("form",$w,r,ew,o)}},Zw=Fr("valid-input"),tS=Fr("invalid-input"),nS=Fr("validating-input"),eS="colorcustom.rgb.",oS=function(t){return ud.config({find:t.getOpt})},rS=function(t){return ud.config({find:function(n){return Jt(n.element,t).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}})},iS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},uS=tinymce.util.Tools.resolve("tinymce.Resource"),aS=tinymce.util.Tools.resolve("tinymce.util.Tools"),cS=Jo([To("preprocess",h),To("postprocess",h)]),sS=function(r,t){var i=co("RepresentingConfigs.memento processors",cS,t);return Tf.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=Tf.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);Tf.setValue(o,e)}}})},lS=i0,fS=function(t){return Tf.config({store:{mode:"memory",initialValue:t}})},dS=Fr("alloy-fake-before-tabstop"),mS=Fr("alloy-fake-after-tabstop"),gS=!(se().browser.isIE()||se().browser.isEdge()),pS=function(t){return t.getContext("2d")},hS=function(t,n,e){return t.width=n,t.height=e,t},vS=function(t){URL.revokeObjectURL(t.src)},bS=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10],yS=function(t,n,T){var e=pS(t),o=function(t){for(var n,e,o,r,i=t.data,u=T[0],a=T[1],c=T[2],s=T[3],l=T[4],f=T[5],d=T[6],m=T[7],g=T[8],p=T[9],h=T[10],v=T[11],b=T[12],y=T[13],x=T[14],w=T[15],S=T[16],C=T[17],k=T[18],O=T[19],_=0;_<i.length;_+=4)n=i[_],e=i[_+1],o=i[_+2],r=i[_+3],i[_]=n*u+e*a+o*c+r*s+l,i[_+1]=n*f+e*d+o*m+r*g+p,i[_+2]=n*h+e*v+o*b+r*y+x,i[_+3]=n*w+e*S+o*C+r*k+O;return t}(e.getImageData(0,0,t.width,t.height));return e.putImageData(o,0,0),b0(t,n)},xS=(iw=[-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1],function(t){return C0(t,iw)}),wS=k0(function(t,n){return S0(t,[1,0,0,0,n=x0(255*n,-255,255),0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])}),SS=k0(function(t,n){var e;return n=x0(n,-1,1),S0(t,[(e=(n*=100)<0?127+n/100*127:127*(0==(e=n%1)?bS[n]:bS[Math.floor(n)]*(1-e)+bS[Math.floor(n)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),CS=(uw=[0,-1,0,-1,5,-1,0,-1,0],function(t){return a=uw,(u=t).toCanvas().then(function(t){return n=t,e=u.getType(),o=a,r=pS(n),i=function(t,n,e){for(var o=function(t,n,e){return e<t?t=e:t<n&&(t=n),t},r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return n}(r.getImageData(0,0,n.width,n.height),r.getImageData(0,0,n.width,n.height),o),r.putImageData(i,0,0),b0(n,e);var n,e,o,r,i});var u,a}),kS=(aw=function(t,n){return 255*Math.pow(t/255,1-n)},function(n,e){return n.toCanvas().then(function(t){return function(t,n,e){for(var o=pS(t),r=new Array(256),i=0;i<r.length;i++)r[i]=aw(i,e);var u=function(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),b0(t,n)}(t,n.getType(),e)})}),OS=function(t,n,e){var o=g0(t),r=p0(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=_S(t,i,u);return a?c.then(function(t){return OS(t,n,e)}):c},_S=function(u,a,c){return new Vy(function(t){var n=g0(u),e=p0(u),o=Math.floor(n*a),r=Math.floor(e*c),i=d0(o,r);pS(i).drawImage(u,0,0,n,e,0,0,o,r),t(i)})},TS=function(t,n,e){var o=(e<0?360+e:e)*Math.PI/180,r=t.width,i=t.height,u=Math.sin(o),a=Math.cos(o),c=O0(Math.abs(r*a)+Math.abs(i*u)),s=O0(Math.abs(r*u)+Math.abs(i*a)),l=d0(c,s),f=pS(l);return f.translate(c/2,s/2),f.rotate(o),f.drawImage(t,-r/2,-i/2),b0(l,n)},ES=function(t,n,e){var o=d0(t.width,t.height),r=pS(o);return"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0)),b0(o,n)},DS=function(t,n,e,o,r,i){var u=d0(r,i);return pS(u).drawImage(t,-e,-o),b0(u,n)},BS=Fr("toolbar.button.execute"),MS=((cw={})[Fi()]=["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],cw),AS=Fr("update-menu-text"),FS=Fr("update-menu-icon"),IS={type:"separator"},RS=function(t,l){var n,e,o;return I((n=y(t)?t.split(" "):t,e=l,0<(o=R(n,function(t,n){return y(n)?""===n?t:"|"===n?0<t.length&&!H0(t[t.length-1])?t.concat([IS]):t:Tt(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[])).length&&H0(o[o.length-1])&&o.pop(),o),function(t,n){var e,o,r,i,u,a,c=function(t){if(H0(t))return t;var n=tt(t,"value").getOrThunk(function(){return Fr("generated-menu-item")});return Xo({value:n},t)}(n),s=(o=l,Tt(e=c,"getSubmenuItems")?(i=o,u=(r=e).getSubmenuItems(),a=RS(u,i),{item:r,menus:Xo(a.menus,cr(r.value,a.items)),expansions:Xo(a.expansions,cr(r.value,r.value))}):{item:e,menus:{},expansions:{}});return{menus:Xo(t.menus,s.menus),items:[s.item].concat(t.items),expansions:Xo(t.expansions,s.expansions)}},{menus:{},expansions:{},items:[]})},VS=rt([To("field1Name","field1"),To("field2Name","field2"),$u("onLockedChange"),Yu(["lockClass"]),To("locked",!1),Ef("coupledFieldBehaviours",[ud,Tf])]),PS=rt([K0("field1","field2"),K0("field2","field1"),qf({factory:sp,schema:[fo("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:tc([zg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),HS=Sl({name:"FormCoupledInputs",configFields:VS(),partFields:PS(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:Df(o.coupledFieldBehaviours,[ud.config({find:vt.some}),Tf.config({store:{mode:"manual",getValue:function(t){var n=hl(t,o,["field1","field2"]),e={};return e[o.field1Name]=Tf.getValue(n.field1()),e[o.field2Name]=Tf.getValue(n.field2()),e},setValue:function(t,n){var e=hl(t,o,["field1","field2"]);nt(n,o.field1Name)&&Tf.setValue(e.field1(),n[o.field1Name]),nt(n,o.field2Name)&&Tf.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return fl(t,o,"field1")},getField2:function(t){return fl(t,o,"field2")},getLock:function(t){return fl(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),zS={undo:rt(Fr("undo")),redo:rt(Fr("redo")),zoom:rt(Fr("zoom")),back:rt(Fr("back")),apply:rt(Fr("apply")),swap:rt(Fr("swap")),transform:rt(Fr("transform")),tempTransform:rt(Fr("temp-transform")),transformApply:rt(Fr("transform-apply"))},NS=rt("save-state"),LS=rt("disable"),WS=rt("enable"),US={formActionEvent:by,saveState:NS,disable:LS,enable:WS},jS=tinymce.util.Tools.resolve("tinymce.geom.Rect"),GS=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),XS=tinymce.util.Tools.resolve("tinymce.util.Observable"),YS=tinymce.util.Tools.resolve("tinymce.util.VK");function qS(t,d){function m(t){if(nw(t),t.button!==p)return x(t);t.deltaX=t.screenX-h,t.deltaY=t.screenY-v,t.preventDefault(),d.drag(t)}var g,p,h,v,b=d.document||document,y=b.getElementById(d.handle||t),x=function(t){nw(t),GS(b).off("mousemove touchmove",m).off("mouseup touchend",x),g.remove(),d.stop&&d.stop(t)};return GS(y).on("mousedown touchstart",function(t){var n,e,o,r,i,u,a,c,s,l=(n=Math.max,e=b.documentElement,o=b.body,r=n(e.scrollWidth,o.scrollWidth),i=n(e.clientWidth,o.clientWidth),u=n(e.offsetWidth,o.offsetWidth),a=n(e.scrollHeight,o.scrollHeight),c=n(e.clientHeight,o.clientHeight),{width:r<u?i:r,height:a<n(e.offsetHeight,o.offsetHeight)?c:a});nw(t),t.preventDefault(),p=t.button;var f=y;h=t.screenX,v=t.screenY,s=window.getComputedStyle?window.getComputedStyle(f,null).getPropertyValue("cursor"):f.runtimeStyle.cursor,g=GS("<div></div>").css({position:"absolute",top:0,left:0,width:l.width,height:l.height,zIndex:2147483647,opacity:1e-4,cursor:s}).appendTo(b.body),GS(b).on("mousemove touchmove",m).on("mouseup touchend",x),d.start(t)}),{destroy:function(){GS(y).off()}}}function KS(t,n,e,o,r){return j0({name:t,icon:vt.some(n),disabled:e,tooltip:vt.some(t),primary:!1,borderless:!1},o,r)}function JS(t,n){n?dd.enable(t):dd.disable(t)}var $S=0,QS=function(s,e,l,o,r){function f(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}}function u(t,n,e,o){var r,i=n.x,u=n.y,a=n.w,c=n.h;i+=e*t.deltaX,u+=o*t.deltaY,a+=e*t.deltaW,c+=o*t.deltaH,s=jS.clamp({x:i,y:u,w:a=a<20?20:a,h:c=c<20?20:c},l,"move"===t.name),r=f(l,s),p.fire("updateRect",{rect:r}),g(r)}function i(t){n(s=t)}function n(n){function t(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),GS("#"+c+"-"+t,o).css({left:n.x,top:n.y,width:n.w,height:n.h})}aS.each(d,function(t){GS("#"+c+"-"+t.name,o).css({left:n.w*t.xMul+n.x,top:n.h*t.yMul+n.y})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)}var t,a="tox-",c="tox-crid-"+$S++,d=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],m=["top","right","bottom","left"],g=function(t){var n;i((n=l,{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}))};GS('<div id="'+c+'" class="'+a+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),aS.each(m,function(t){GS("#"+c,o).append('<div id="'+c+"-"+t+'"class="'+a+'croprect-block" style="display: none" data-mce-bogus="all">')}),aS.each(d,function(t){GS("#"+c,o).append('<div id="'+c+"-"+t.name+'" class="'+a+"croprect-handle "+a+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false" title="'+t.label+'">')}),t=aS.map(d,function(n){var e;return qS(c,{document:o.ownerDocument,handle:c+"-"+n.name,start:function(){e=s},drag:function(t){u(n,e,t.deltaX,t.deltaY)}})}),n(s),GS(o).on("focusin focusout",function(t){GS(t.target).attr("aria-grabbed","focus"===t.type?"true":"false")}),GS(o).on("keydown",function(n){var i;function t(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),u(i,e,o,r)}switch(aS.each(d,function(t){if(n.target.id===c+"-"+t.name)return i=t,!1}),n.keyCode){case YS.LEFT:t(n,0,s,-10,0);break;case YS.RIGHT:t(n,0,s,10,0);break;case YS.UP:t(n,0,s,0,-10);break;case YS.DOWN:t(n,0,s,0,10);break;case YS.ENTER:case YS.SPACEBAR:n.preventDefault(),r()}});var p=aS.extend({toggleVisibility:function(t){var n=aS.map(d,function(t){return"#"+c+"-"+t.name}).concat(aS.map(m,function(t){return"#"+c+"-"+t})).join(",");t?GS(n,o).show():GS(n,o).hide()},setClampRect:function(t){l=t,n(s)},setRect:i,getInnerRect:function(){return f(l,s)},setInnerRect:g,setViewPortRect:function(t){e=t,n(s)},destroy:function(){aS.each(t,function(t){t.destroy()}),t=[]}},XS);return p};function ZS(t){var e,o,n,r,i=Vo(t),u=dc(),a=(o=-1,{data:e=[],add:function(t){var n=e.splice(++o);return e.push(t),{state:t,removed:n}},undo:function(){if(n())return e[--o]},redo:function(){if(r())return e[++o]},canUndo:n=function(){return 0<o},canRedo:r=function(){return-1!==o&&o<e.length-1}});function c(t){i.set(t)}function s(t){URL.revokeObjectURL(t.url)}function l(t){var n=f(t);c(n);var e=a.add(n).removed;return aS.each(e,s),n.url}a.add(t);function f(t){return{blob:t,url:URL.createObjectURL(t)}}function d(){u.on(s),u.clear()}return{getBlobState:function(){return i.get()},setBlobState:c,addBlobState:l,getTempState:function(){return u.get().getOrThunk(i.get)},updateTempState:function(t){var n=f(t);return d(),u.set(n),n.url},addTempState:function(t){var n=f(t);return u.set(n),n.url},applyTempState:function(n){return u.get().fold(st,function(t){l(t.blob),n()})},destroyTempState:d,undo:function(){var t=a.undo();return c(t),t.url},redo:function(){var t=a.redo();return c(t),t.url},getHistoryStates:function(){return{undoEnabled:a.canUndo(),redoEnabled:a.canRedo()}}}}function tC(t,n){function i(t){var n=b.getHistoryStates();S.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),vr(t,US.formActionEvent,{name:US.saveState(),value:n.undoEnabled})}function u(t){return t.toBlob()}function a(t){vr(t,US.formActionEvent,{name:US.disable(),value:{}})}function c(n,t,e,o,r){a(n),y0(t).then(e).then(u).then(o).then(function(t){return x(n,t)}).then(function(){i(n),r(),y(n)}).catch(function(t){console.log(t),n.getSystem().isConnected()&&y(n)})}function r(t,n,e){c(t,b.getBlobState().blob,n,function(t){return b.updateTempState(t)},e)}function s(t){var n=b.getBlobState().url;return b.destroyTempState(),i(t),n}var e,o,l,f,d,m,g,p,h,v,b=ZS(t.currentState),y=function(t){C.getApplyButton(t).each(function(t){dd.enable(t)}),vr(t,US.formActionEvent,{name:US.enable(),value:{}})},x=function(t,n){return a(t),w.updateSrc(t,n)},w=(f=t.currentState.url,d=Pm({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),m=Vo(1),g=dc(),p=Vo({x:0,y:0,w:1,h:1}),h=Vo({x:0,y:0,w:1,h:1}),{memContainer:v=Pm(uy.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[d.asSpec(),{dom:{tag:"img",attributes:{src:f}}},{dom:{tag:"div"},behaviours:tc([fm("image-panel-crop-events",[eu(function(t){v.getOpt(t).each(function(t){var n=t.element.dom,e=QS({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,st);e.toggleVisibility(!1),e.on("updateRect",function(t){var n=t.rect,e=m.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};p.set(o)}),g.set(e)})})])])}],containerBehaviours:tc([Fg.config({}),fm("image-panel-events",[eu(function(t){O(t,f)})])])})),updateSrc:O,zoom:function(t,n){var e=m.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);m.set(o),v.getOpt(t).each(function(t){var n=t.components()[1].element;k(t,n)})},showCrop:function(){g.on(function(t){t.toggleVisibility(!0)})},hideCrop:function(){g.on(function(t){t.toggleVisibility(!1)})},getRect:function(){return p.get()},getMeasurements:function(){var t=h.get();return{width:t.w,height:t.h}}}),S=(o=Pm(KS("Undo","undo",!0,function(t){vr(t,zS.undo(),{direction:1})},e=n)),l=Pm(KS("Redo","redo",!0,function(t){vr(t,zS.redo(),{direction:1})},e)),{container:uy.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),l.asSpec(),KS("Zoom in","zoom-in",!1,function(t){vr(t,zS.zoom(),{direction:1})},e),KS("Zoom out","zoom-out",!1,function(t){vr(t,zS.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){JS(t,n)}),l.getOpt(t).each(function(t){JS(t,e)})}}),C=tw(w,n);function k(t,s){v.getOpt(t).each(function(t){var e=m.get(),o=kn(t.element),r=xn(t.element),i=s.dom.naturalWidth*e,u=s.dom.naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};dn(s,n),d.getOpt(t).each(function(t){dn(t.element,n)}),g.on(function(t){var n=p.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})}function O(t,n){var e,i=At.fromTag("img");return on(i,"src",n),e=i.dom,new Bp(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)}).then(function(){t.getSystem().isConnected()&&v.getOpt(t).map(function(t){var n=ku({element:i});Fg.replaceAt(t,1,vt.some(n));var e=h.get(),o={x:0,y:0,w:i.dom.naturalWidth,h:i.dom.naturalHeight};h.set(o);var u,r=jS.inflate(o,-20,-20);p.set(r),e.w===o.w&&e.h===o.h||(u=i,v.getOpt(t).each(function(t){var n=kn(t.element),e=xn(t.element),o=u.dom.naturalWidth,r=u.dom.naturalHeight,i=Math.min(n/o,e/r);1<=i?m.set(1):m.set(i)})),k(t,i)})})}return{dom:{tag:"div",attributes:{role:"presentation"}},components:[C.memContainer.asSpec(),w.memContainer.asSpec(),S.container],behaviours:tc([Tf.config({store:{mode:"manual",getValue:function(){return b.getBlobState()}}}),fm("image-tools-events",[Cr(zS.undo(),function(n,t){var e=b.undo();x(n,e).then(function(t){y(n),i(n)})}),Cr(zS.redo(),function(n,t){var e=b.redo();x(n,e).then(function(t){y(n),i(n)})}),Cr(zS.zoom(),function(t,n){var e=n.event.direction;w.zoom(t,e)}),Cr(zS.back(),function(t,n){var e,o=s(e=t);x(e,o).then(function(t){y(e)}),(0,n.event.swap)(),w.hideCrop()}),Cr(zS.apply(),function(t,n){b.applyTempState(function(){s(t),(0,n.event.swap)()})}),Cr(zS.transform(),function(t,n){return r(t,n.event.transform,st)}),Cr(zS.tempTransform(),function(t,n){var e=n.event.transform;c(t,b.getTempState().blob,e,function(t){return b.addTempState(t)},st)}),Cr(zS.transformApply(),function(t,n){var e=t,o=n.event.transform,r=n.event.swap,i=b.getBlobState().blob;c(e,i,o,function(t){var n=b.addBlobState(t);return s(e),n},r)}),Cr(zS.swap(),function(n,t){S.updateButtonUndoStates(n,!1,!1);var e=t.event.transform,o=t.event.swap;e.fold(function(){o()},function(t){r(n,t,o)})})]),ow()])}}function nC(t){return!Tt(t,"items")}function eC(t,n){function e(t){return{dom:{tag:"td",innerHtml:n.translate(t)}}}return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:M(t.header,function(t){return{dom:{tag:"th",innerHtml:n.translate(t)}}})}]},{dom:{tag:"tbody"},components:M(t.cells,function(t){return{dom:{tag:"tr"},components:M(t,e)}})}],behaviours:tc([fy.config({}),Vg.config({})])}}function oC(e,n){var t=e.label.map(function(t){return Zb(t,n)}),o=[dd.config({disabled:function(){return e.disabled||n.isDisabled()}}),mv(),Mg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return hr(t,yy),vt.some(!0)}}),fm("textfield-change",[Cr(wi(),function(t,n){vr(t,py,{name:e.name})}),Cr(Mi(),function(t,n){vr(t,py,{name:e.name})})]),fy.config({})],r=e.validation.map(function(o){return Gy.config({getRoot:function(t){return Yt(t.element)},invalidClass:"tox-invalid",validator:{validate:function(t){var n=Tf.getValue(t),e=o.validator(n);return Ly(!0===e?Re.value(n):Re.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(rt({}),function(t){return{placeholder:n.translate(t)}}),u=e.inputMode.fold(rt({}),function(t){return{inputmode:t}}),a=lt(lt({},i),u);return Qb(t,sy.parts.field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:tc(ft([o,r])),selectOnFocus:!1,factory:Oy}),(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),[dd.config({disabled:function(){return e.disabled||n.isDisabled()},onDisabled:function(t){sy.getField(t).each(dd.disable)},onEnabled:function(t){sy.getField(t).each(dd.enable)}}),mv()])}function rC(t){var n=Vo(null);return xu({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})}function iC(t,n,e){var o=Tf.getValue(e);Tf.setValue(n,o),u1(n)}function uC(t,n){var e=t.element,o=$r(e),r=e.dom;"number"!==rn(e,"type")&&n(r,o)}function aC(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:st}}function cC(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:void 0},onAction:st}}function sC(t,n){return e=t,M(F(n,function(t){return t.type===e}),aC);var e}function lC(t,n){var e=t.toLowerCase();return F(n,function(t){return ut((void 0!==t.meta&&void 0!==t.meta.text?t.meta:t).text.toLowerCase(),e)||ut(t.value.toLowerCase(),e)})}function fC(u,a,c){function r(t){var n=Tf.getValue(t);c.addToHistory(n.value,u.filetype)}var t,n,e,o,i=a.shared.providers,s=sy.parts.field({factory:l1,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":g1,type:"url"},minChars:0,responseTime:0,fetch:function(t){var e,o,n,r,i=z0((e=u.filetype,o=c,n=Tf.getValue(t),r=void 0!==n.meta.text?n.meta.text:n.value,o.getLinkInformation().fold(function(){return[]},function(t){var n=lC(r,M(o.getHistory(e),function(t){return cC(t,t)}));return"file"===e?R([n,lC(r,sC("header",t.targets)),lC(r,ft([vt.from(t.anchorTop).map(function(t){return cC("<top>",t)}).toArray(),sC("anchor",t.targets),vt.from(t.anchorBottom).map(function(t){return cC("<bottom>",t)}).toArray()]))],function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat(m1,n)},[]):n})),xh.BUBBLE_TO_SANDBOX,a,!1);return Ly(i)},getHotspot:function(t){return p.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(Gy)&&Gy.run(t).get(st)},typeaheadBehaviours:tc(ft([c.getValidationHandler().map(function(e){return Gy.config({getRoot:function(t){return Yt(t.element)},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){f.getOpt(t).each(function(t){on(t.element,"title",i.translate(n))})}},validator:{validate:function(t){var n=Tf.getValue(t);return d1(function(o){e({type:u.filetype,url:n.value},function(t){var n,e;"invalid"===t.status?(n=Re.error(t.message),o(n)):(e=Re.value(t.message),o(e))})})},validateOnLoad:!1}})}).toArray(),[dd.config({disabled:function(){return u.disabled||i.isDisabled()}}),fy.config({}),fm("urlinput-events",ft(["file"===u.filetype?[Cr(wi(),function(t){vr(t,py,{name:u.name})})]:[],[Cr(Si(),function(t){vr(t,py,{name:u.name}),r(t)}),Cr(Mi(),function(t){vr(t,py,{name:u.name}),r(t)})]]))]])),eventOrder:((t={})[wi()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:a.shared.getSink,parts:{menu:$p(0,0,"normal")},onExecute:function(t,n,e){vr(n,yy,{})},onItemExecute:function(t,n,e,o){r(t),vr(t,py,{name:u.name})}}),l=u.label.map(function(t){return Zb(t,i)}),f=Pm((n="invalid",e=vt.some(g1),jm("warning",{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+n],attributes:lt({title:i.translate(o=void 0===o?n:o),"aria-live":"polite"},e.fold(function(){return{}},function(t){return{id:t}}))},i.icons))),d=Pm({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[f.asSpec()]}),m=c.getUrlPicker(u.filetype),g=Fr("browser.url.event"),p=Pm({dom:{tag:"div",classes:["tox-control-wrap"]},components:[s,d.asSpec()],behaviours:tc([dd.config({disabled:function(){return u.disabled||i.isDisabled()}})])}),h=Pm(X0({name:u.name,icon:vt.some("browse"),text:u.label.getOr(""),disabled:u.disabled,primary:!1,borderless:!0},function(t){return hr(t,g)},i,[],["tox-browse-url"]));return sy.sketch({dom:gy([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:ft([[p.asSpec()],m.map(function(){return h.asSpec()}).toArray()])}]),fieldBehaviours:tc([dd.config({disabled:function(){return u.disabled||i.isDisabled()},onDisabled:function(t){sy.getField(t).each(dd.disable),h.getOpt(t).each(dd.disable)},onEnabled:function(t){sy.getField(t).each(dd.enable),h.getOpt(t).each(dd.enable)}}),mv(),fm("url-input-events",[Cr(g,function(o){ud.getCurrent(o).each(function(n){var t=Tf.getValue(n),e=lt({fieldname:u.name},t);m.each(function(t){t(e).get(function(t){Tf.setValue(n,t),vr(o,py,{name:u.name})})})})})])])})}function dC(r){return function(n,e,o){return tt(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}}function mC(n,t,e){var o=Xo(e,{shared:{interpreter:function(t){return v1(n,t,o)}}});return v1(n,t,o)}function gC(t,n,e){function o(){return At.fromDom(t.getContentAreaContainer())}function r(){return p||!e()}var i,u,a,c,s,l,f,d,m,g,p=av(t);return{inlineDialog:(f=o,d=n,m=r,g={maxHeightFunction:Ic()},function(){return m()?{type:"node",root:tn(f()),node:vt.from(f()),bubble:xc(12,12,b1),layouts:{onRtl:function(){return[Bm]},onLtr:function(){return[Dm]}},overrides:g}:{type:"hotspot",hotspot:d(),bubble:xc(-12,12,b1),layouts:{onRtl:function(){return[Ga]},onLtr:function(){return[Xa]}},overrides:g}}),banner:(c=o,s=n,l=r,function(){return l()?{type:"node",root:tn(c()),node:vt.from(c()),layouts:{onRtl:function(){return[up]},onLtr:function(){return[up]}}}:{type:"hotspot",hotspot:s(),layouts:{onRtl:function(){return[Ja]},onLtr:function(){return[Ja]}}}}),cursor:(u=t,function(){return{type:"selection",root:a(),getSelection:function(){var t=u.selection.getRng();return vt.some(fs.range(At.fromDom(t.startContainer),t.startOffset,At.fromDom(t.endContainer),t.endOffset))}}}),node:(i=a=function(){return At.fromDom(t.getBody())},function(t){return{type:"node",root:i(),node:t}})}}function pC(i){return vt.from(i.getParam("style_formats")).filter(c).map(function(t){var n,e,o=(n=i,e=x1(t),n.formatter?r(e.customFormats):n.on("init",function(){r(e.customFormats)}),e.formats);function r(t){St(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})}return i.getParam("style_formats_merge",!1,"boolean")?y1.concat(o):o}).getOr(y1)}function hC(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return Xo(t,o)}function vC(c,t,s,l){var f=function(t){return M(t,function(t){var n,e,o,r,i,u=kt(t);if(nt(t,"items")){var a=f(t.items);return Xo(Xo(t,{type:"submenu"}),{getStyleItems:rt(a)})}return nt(t,"format")?hC(t,s,l):1===u.length&&wt(u,"title")?Xo(t,{type:"separator"}):(r={type:"formatter",format:o="custom-"+(e=y((n=t).name)?n.name:Fr(n.title)),isSelected:s(o),getStylePreview:l(o)},i=Xo(n,r),c.formatter.register(e,i),i)})};return f(t)}function bC(e){return function(t){if(d(n=t)&&1===n.nodeType){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}var n;return!1}}function yC(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}}function xC(t){return t.innerText||t.textContent}function wC(t){return t&&"A"===t.nodeName&&void 0!==(t.id||t.name)&&Yk(t)}function SC(t){return t&&/^(H[1-6])$/.test(t.nodeName)}function CC(t){return SC(t)&&Yk(t)}function kC(t){var n,e=t.id||Fr("h");return yC("header",xC(t),"#"+e,SC(n=t)?parseInt(n.nodeName.substr(1),10):0,function(){t.id=e})}function OC(t){var n=t.id||t.name;return yC("anchor",xC(t)||"#"+n,"#"+n,0,st)}function _C(t){return 0<w1(t.title).length}function TC(t){return y(t)&&/^https?/.test(t)}function EC(t){return x(t)&&Q(t,function(t){return!(c(n=t)&&n.length<=5&&N(n,TC));var n}).isNone()}function DC(){var t,n=lb.getItem(O1);if(null===n)return{};try{t=JSON.parse(n)}catch(t){if(t instanceof SyntaxError)return console.log("Local storage "+O1+" was not valid JSON",t),{};throw t}return EC(t)?t:(console.log("Local storage "+O1+" was not valid format",t),{})}function BC(t){return tt(DC(),t).getOr([])}function MC(n,t){var e,o;TC(n)&&(o=F(tt(e=DC(),t).getOr([]),function(t){return t!==n}),e[t]=[n].concat(o).slice(0,5),function(t){if(!EC(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));lb.setItem(O1,JSON.stringify(t))}(e))}function AC(t){return!!t}function FC(t){return dt(aS.makeMap(t,/[, ]/),AC)}function IC(t){return vt.from(t.getParam("file_picker_callback")).filter(m)}function RC(t){return vt.from(t).filter(y).getOrUndefined()}function VC(l){return{getHistory:BC,addToHistory:MC,getLinkInformation:function(){return!1===(t=l).getParam("typeahead_urls")?vt.none():vt.some({targets:k1(t.getBody()),anchorTop:RC(t.getParam("anchor_top","#top")),anchorBottom:RC(t.getParam("anchor_bottom","#bottom"))});var t},getValidationHandler:function(){return vt.from(void 0===(n=(t=l).getParam("file_picker_validator_handler",void 0,"function"))?t.getParam("filepicker_validator_handler",void 0,"function"):n);var t,n},getUrlPicker:function(t){return n=c=l,e=s=t,r=vt.some((o=n).getParam("file_picker_types")).filter(AC),i=vt.some(o.getParam("file_browser_callback_types")).filter(AC),u=r.or(i).map(FC),a=IC(o).fold(O,function(t){return u.fold(_,function(t){return 0<kt(t).length&&t})}),(w(a)?a?IC(n):vt.none():a[e]?IC(n):vt.none()).map(function(o){return function(n){return Ny(function(e){var t=lt({filetype:s,fieldname:n.fieldname},vt.from(n.meta).getOr({}));o.call(c,function(t,n){if(!y(t))throw new Error("Expected value to be string");if(void 0!==n&&!x(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var n,e,o,r,i,u,a,c,s}}}function PC(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p,h=Vo(!1),v={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=Vo(iv(n)?"bottom":"top")).get,setDockingMode:o.set},b={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:lp.translate,isDisabled:function(){return n.mode.isReadOnly()||n.ui.isDisabled()},getSetting:n.getParam.bind(n)},interpreter:function(t){return v1(h1,t,b)},anchors:gC(n,e,v.isPositionedAtTop),header:v,getSink:function(){return Re.value(t)}},urlinput:VC(n),styleselect:(f=Vo([]),d=Vo([]),m=Vo([]),g=Vo([]),p=Vo(!(l=function(t){var n=t.items;return void 0!==n&&0<n.length?z(n,l):[t.format]})),(s=n).on("PreInit",function(t){var n=pC(s),e=vC(s,n,y,x);f.set(e),d.set(z(e,l))}),s.on("addStyleModifications",function(t){var n=vC(s,t.items,y,x);m.set(n),p.set(t.replace),g.set(z(n,l))}),{getData:function(){var t=p.get()?[]:f.get(),n=m.get();return t.concat(n)},getFlattenedKeys:function(){var t=p.get()?[]:d.get(),n=g.get();return t.concat(n)}}),colorinput:{colorPicker:function(t,n){Bb(c)(t,n)},hasCustomColors:function(){return db(a)},getColors:function(){return mb(u)},getColorCols:(i=u=a=c=n,function(){return pb(i)})},dialog:{isDraggableModal:(r=n,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return h.get()},setContextMenuState:function(t){return h.set(t)}};function y(t){return function(){return s.formatter.match(t)}}function x(n){return function(){var t=s.formatter.get(n);return void 0!==t?vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:s.dom.parseStyle(s.formatter.getCssText(n))}):vt.none()}}return b}function HC(t){return(mt(pn(t,"position"),"fixed")?vt.none():qt(t)).orThunk(function(){var e=At.fromTag("span");return Yt(t).bind(function(t){_e(t,e);var n=qt(e);return Te(e),n})})}function zC(t){return HC(t).map(Cn).getOrThunk(function(){return Se(0,0)})}function NC(t,n){var e=t.element;Xr(e,n.transitionClass),Yr(e,n.fadeOutClass),Xr(e,n.fadeInClass),n.onShow(t)}function LC(t,n){var e=t.element;Xr(e,n.transitionClass),Yr(e,n.fadeInClass),Xr(e,n.fadeOutClass),n.onHide(t)}function WC(t,n,e){return N(t,function(t){switch(t){case"bottom":return n.bottom<=e.bottom;case"top":return n.y>=e.y}})}function UC(n,t){return t.getInitialPos().map(function(t){return Be(t.bounds.x,t.bounds.y,kn(n),xn(n))})}function jC(e,o,r){return r.getInitialPos().bind(function(t){switch(r.clearInitialPos(),t.position){case"static":return vt.some(V1.static());case"absolute":var n=HC(e).map(Me).getOrThunk(function(){return Me(ve())});return vt.some(V1.absolute(Ta("absolute",tt(t.style,"left").map(function(t){return o.x-n.x}),tt(t.style,"top").map(function(t){return o.y-n.y}),tt(t.style,"right").map(function(t){return n.right-o.right}),tt(t.style,"bottom").map(function(t){return n.bottom-o.bottom}))));default:return vt.none()}})}function GC(t,n,e){var o,r,i,u=t.element;return mt(pn(u,"position"),"fixed")?(r=n,UC(o=u,i=e).filter(function(t){return WC(i.getModes(),t,r)}).bind(function(t){return jC(o,t,i)})):function(t,n,e){var r,o,i=Me(t);if(WC(e.getModes(),i,n))return vt.none();r=t,o=i,e.setInitialPos({style:function(){var t={},n=r.dom;if(ct(n))for(var e=0;e<n.style.length;e++){var o=n.style.item(e);t[o]=n.style[o]}return t}(),position:gn(r,"position")||"static",bounds:o});var u=Ae(),a=i.x-u.x,c=n.y-u.y,s=u.bottom-n.bottom,l=i.y<=n.y;return vt.some(V1.fixed(Ta("fixed",vt.some(a),l?vt.some(c):vt.none(),vt.none(),l?vt.none():vt.some(s))))}(u,n,e)}function XC(n,t,e){e.setDocked(!1),St(["left","right","top","bottom","position"],function(t){return vn(n.element,t)}),t.onUndocked(n)}function YC(t,n,e,o){var r="fixed"===o.position;e.setDocked(r),Ea(t.element,o),(r?n.onDocked:n.onUndocked)(t)}function qC(o,t,r,i,u){void 0===u&&(u=!1),t.contextual.each(function(e){e.lazyContext(o).each(function(t){var n=t.y<i.bottom&&t.bottom>i.y;n!==r.isVisible()&&(r.setVisible(n),u&&!n?(Kr(o.element,[e.fadeOutClass]),e.onHide(o)):(n?NC:LC)(o,e))})})}function KC(t,n,e){var o,r,i,u,a,c;e.isDocked()&&(r=n,i=e,c=(o=t).element,i.setDocked(!1),UC(a=o.element,u=i).bind(function(t){return jC(a,t,u)}).each(function(t){t.fold(function(){return XC(o,r,i)},function(t){return YC(o,r,i,t)},st)}),i.setVisible(!0),r.contextual.each(function(t){Jr(c,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(o)}),P1(o,r,i))}function JC(t,n){return wt(N1.getModes(t),n)}function $C(r){var i=r.element;Yt(i).each(function(t){var n,e,o="padding-"+N1.getModes(r)[0];N1.isDocked(r)?(n=kn(t),fn(i,"width",n+"px"),fn(t,o,wn(e=i)+(parseInt(gn(e,"margin-top"),10)||0)+(parseInt(gn(e,"margin-bottom"),10)||0)+"px")):(vn(i,"width"),vn(t,o))})}function QC(t,n){n?(Yr(t,W1.fadeOutClass),Kr(t,[W1.transitionClass,W1.fadeInClass])):(Yr(t,W1.fadeInClass),Kr(t,[W1.fadeOutClass,W1.transitionClass]))}function ZC(t,n){var e=At.fromDom(t.getContainer());n?(Xr(e,U1),Yr(e,j1)):(Xr(e,j1),Yr(e,U1))}function tk(u,t){function o(n){r().each(function(t){return n(t.element)})}function n(t){u.inline||$C(t),ZC(u,N1.isDocked(t)),t.getSystem().broadcastOn([bf()],{}),r().each(function(t){return t.getSystem().broadcastOn([bf()],{})})}var e,i=dc(),r=t.getSink,a=u.inline?[]:[rc.config({channels:((e={})[L1()]={onReceive:$C},e)})];return H([Vg.config({}),N1.config({contextual:lt({lazyContext:function(t){var n=wn(t.element),e=u.inline?u.getContentAreaContainer():u.getContainer(),o=Me(At.fromDom(e)),r=o.height-n,i=o.y+(JC(t,"top")?0:n);return vt.some(Be(o.x,i,o.width,r))},onShow:function(){o(function(t){return QC(t,!0)})},onShown:function(r){o(function(t){return Jr(t,[W1.transitionClass,W1.fadeInClass])}),i.get().each(function(t){var n,e=r.element,o=Ut(n=t);ka(o).filter(function(t){return!Lt(n,t)}).filter(function(t){return Lt(t,At.fromDom(o.dom.body))||Wt(e,t)}).each(function(){return Sa(n)}),i.clear()})},onHide:function(t){var n=t.element,e=r;Oa(n).orThunk(function(){return e().toOptional().bind(function(t){return Oa(t.element)})}).fold(i.clear,i.set),o(function(t){return QC(t,!1)})},onHidden:function(){o(function(t){return Jr(t,[W1.transitionClass])})}},W1),lazyViewport:function(t){var n=Ae(),e=u.getParam("toolbar_sticky_offset",0,"number"),o=n.y+(JC(t,"top")?e:0),r=n.height-(JC(t,"bottom")?e:0);return Be(n.x,o,n.width,r)},modes:[t.header.getDockingMode()],onDocked:n,onUndocked:n})],a,!0)}function nk(t){return uo("menubutton",q1,t)}function ek(n,t){return t.getAnimationRoot.fold(function(){return n.element},function(t){return t(n)})}function ok(t){return t.dimension.property}function rk(t,n){return t.dimension.getDimension(n)}function ik(t,n){Jr(ek(t,n),[n.shrinkingClass,n.growingClass])}function uk(t,n){Yr(t.element,n.openClass),Xr(t.element,n.closedClass),fn(t.element,ok(n),"0px"),bn(t.element)}function ak(t,n){Yr(t.element,n.closedClass),Xr(t.element,n.openClass),vn(t.element,ok(n))}function ck(t,n,e,o){e.setCollapsed(),fn(t.element,ok(n),rk(n,t.element)),bn(t.element),ik(t,n),uk(t,n),n.onStartShrink(t),n.onShrunk(t)}function sk(t,n,e){var o=rk(n,t.element);("0px"===o?ck:function(t,n,e,o){var r=o.getOrThunk(function(){return rk(n,t.element)});e.setCollapsed(),fn(t.element,ok(n),r),bn(t.element);var i=ek(t,n);Yr(i,n.growingClass),Xr(i,n.shrinkingClass),uk(t,n),n.onStartShrink(t)})(t,n,e,vt.some(o))}function lk(t,n,e){var o=ek(t,n),r=qr(o,n.shrinkingClass),i=rk(n,t.element);ak(t,n);var u=rk(n,t.element);(r?function(){fn(t.element,ok(n),i),bn(t.element)}:function(){uk(t,n)})(),Yr(o,n.shrinkingClass),Xr(o,n.growingClass),ak(t,n),fn(t.element,ok(n),u),e.setExpanded(),n.onStartGrow(t)}function fk(t,n,e){return!0===qr(ek(t,n),n.growingClass)}function dk(t,n,e){return!0===qr(ek(t,n),n.shrinkingClass)}function mk(t){return"<alloy.field."+t+">"}function gk(t){return{element:function(){return t.element.dom}}}function pk(t,e){ud.getCurrent(t).each(function(t){return Fg.set(t,[(n=e,oO.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:(e=t,r=M(kt(o=n),function(t){var n=o[t],e=ao(uo("sidebar",rO,n));return{name:t,getApi:gk,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}}),M(r,function(t){var n=Vo(st);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Yh([pv(t,n),hv(t,n),Cr(Ki(),function(n,t){var e=t.event;V(r,function(t){return t.name===e.name}).each(function(t){(e.visible?t.onShow:t.onHide)(t.getApi(n))})})])})})),slotBehaviours:Yh([eu(function(t){return oO.hideAllSlots(t)})])};var e,o,r}))]);var n})}function hk(t){return ud.getCurrent(t).bind(function(t){return Z1.isGrowing(t)||Z1.hasGrown(t)?ud.getCurrent(t).bind(function(n){return V(oO.getSlotNames(n),function(t){return oO.isShowing(n,t)})}):vt.none()})}function vk(t){var n=At.fromHtml(t),e=Kt(n),o=R(void 0!==n.dom.attributes?n.dom.attributes:[],function(t,n){var e;return"class"===n.name?t:lt(lt({},t),((e={})[n.name]=n.value,e))},{}),r=Array.prototype.slice.call(n.dom.classList,0),i=0===e.length?{}:{innerHtml:Br(n)};return lt({tag:Ft(n),classes:r,attributes:o},i)}function bk(t){return ud.getCurrent(t).each(function(t){return Sa(t.element)})}function yk(f,d,m){function n(t){var n;!g.get()||"focusin"===(n=t).type&&(n.composed?Y(n.composedPath()):vt.from(n.target)).map(At.fromDom).filter(Gn).exists(function(t){return qr(t,"mce-pastebin")})||(t.preventDefault(),bk(d()),f.editorManager.setActive(f))}var g=Vo(!1),e=dc();function o(t){var n,e,o,r,i,u,a,c,s,l;t!==g.get()&&(g.set(t),n=f,e=d(),o=t,r=m.providers,c=e.element,s=o,l="data-mce-"+(i="tabindex"),vt.from(n.iframeElement).map(At.fromDom).each(function(n){s?(un(n,i).each(function(t){return on(n,l,t)}),on(n,i,-1)):(cn(n,i),un(n,l).each(function(t){on(n,i,t),cn(n,l)}))}),o?(cO.block(e,(a=r,function(t,n){return{dom:{tag:"div",attributes:{"aria-label":a.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:vk('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})),vn(c,"display"),cn(c,"aria-hidden"),n.hasFocus()&&bk(e)):(u=ud.getCurrent(e).exists(function(t){return Ca(t.element)}),cO.unblock(e),fn(c,"display","none"),on(c,"aria-hidden","true"),u&&n.focus()),f.fire("AfterProgressState",{state:t}))}f.inline||f.on("PreInit",function(){f.dom.bind(f.getWin(),"focusin",n),f.on("BeforeExecCommand",function(t){"mcefocus"===t.command.toLowerCase()&&!0!==t.value&&n(t)})}),f.on("ProgressState",function(t){var n;e.on(cp.clearTimeout),u(t.time)?(n=cp.setEditorTimeout(f,function(){return o(t.state)},t.time),e.set(n)):(o(t.state),e.clear())})}function xk(t,n,e){return{within:t,extra:n,withinWidth:e}}function wk(t,n,o){var e,r=(e=function(t,n){var e=o(t);return vt.some({element:t,start:n,finish:n+e,width:e})},R(t,function(n,t){return e(t,n.len).fold(rt(n),function(t){return{len:t.finish,list:n.list.concat([t])}})},{len:0,list:[]}).list),i=F(r,function(t){return t.finish<=n}),u=I(i,function(t,n){return t+n.width},0);return{within:i,extra:r.slice(i.length),withinWidth:u}}function Sk(t){return M(t,function(t){return t.element})}function Ck(t,n){var e=M(n,function(t){return Tu(t)});F1.setGroups(t,e)}function kk(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,C=n.builtGroups.get();0!==C.length&&(o=dl(t,n,"primary"),r=Yy.getCoupled(t,"overflowGroup"),fn(o.element,"visibility","hidden"),u=K(i=C.concat([r]),function(n){return Oa(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}),e([]),Ck(o,i),a=kn(o.element),0===(s=a,l=n.builtGroups.get(),d=r,y=(0===(m=wk(l,s,f=function(t){return kn(t.element)})).extra.length?vt.some(m):vt.none()).getOrThunk(function(){return wk(l,s-f(d),f)}),x=y.within,w=y.extra,S=y.withinWidth,(c=1===w.length&&w[0].width<=f(d)?(b=S,xk(Sk(x.concat(w)),[],b)):1<=w.length?(p=w,h=d,v=S,xk(Sk(x).concat([h]),Sk(p),v)):(g=S,xk(Sk(x),[],g))).extra.length)?(Fg.remove(o,r),e([])):(Ck(o,c.within),e(c.extra)),vn(o.element,"visibility"),bn(o.element),u.each(Vg.focus))}function Ok(t,n){var e=Yy.getCoupled(t,"toolbarSandbox");hf.isOpen(e)?hf.close(e):hf.open(e,n.toolbar())}function _k(t,n,e,o){var r=e.getBounds.map(function(t){return t()}),i=e.lazySink(t).getOrDie();sf.positionWithinBounds(i,n,{anchor:{type:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:dO()}}},r)}function Tk(t,n,e,o,r){F1.setGroups(n,r),_k(t,n,e,o),zg.on(t)}function Ek(t){return M(t,function(t){return Tu(t)})}function Dk(t,e,o){kk(t,o,function(n){o.overflowGroups.set(n),e.getOpt(t).each(function(t){pO.setGroups(t,Ek(n))})})}function Bk(n,e){fl(n,e,"overflow-button").bind(function(){return fl(n,e,"overflow")}).each(function(t){Kk(n,e),Z1.toggleGrow(t)})}function Mk(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:lt({tag:"div",classes:["tox-toolbar__group"]},n),components:[bO.parts.items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:tc([fy.config({}),Vg.config({})])}}function Ak(t){return bO.sketch(Mk(t))}function Fk(e,t){var n=eu(function(t){var n=M(e.initGroups,Ak);F1.setGroups(t,n)});return tc([Jv(e.providers.isDisabled),mv(),Mg.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),fm("toolbar-events",[n])])}function Ik(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":Mk({title:vt.none(),items:[]}),"overflow-button":U0({name:"more",icon:vt.some("more-drawer"),disabled:!1,tooltip:vt.some("More..."),primary:!1,borderless:!1},vt.none(),t.providers)},splitToolbarBehaviours:Fk(t,n)}}function Rk(t){var n=t.cyclicKeying?"cyclic":"acyclic";return F1.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===hh.scrolling?["tox-toolbar--scrolling"]:[])},components:[F1.parts.groups({})],toolbarBehaviours:Fk(t,n)})}function Vk(t){return"string"==typeof t?t.split(" "):t}function Pk(i,u){var a=lt(lt({},MO),u.menus),e=0<kt(u.menus).length,t=F(void 0===u.menubar||!0===u.menubar?Vk("file edit view insert format tools table help"):Vk(!1===u.menubar?"":u.menubar),function(t){var n=Tt(MO,t);return e?n||tt(u.menus,t).exists(function(t){return Tt(t,"items")}):n});return F(M(t,function(t){var n=a[t],e={title:n.title,items:Vk(n.items)},o=u,r=i.getParam("removed_menuitems","").split(/[ ,]/);return{text:e.title,getItems:function(){return z(e.items,function(t){var n=t.toLowerCase();return 0===n.trim().length||T(r,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:o.menuItems[n]?[o.menuItems[n]]:[]})}}}),function(t){return 0<t.getItems().length&&T(t.getItems(),function(t){return"separator"!==t.type})})}function Hk(t){function n(){t._skinLoaded=!0,t.fire("SkinLoaded")}return function(){t.initialized?n():t.on("init",n)}}function zk(e,o,r){return new Bp(function(t,n){r.load(o,t,n),e.on("remove",function(){return r.unload(o)})})}function Nk(t,n){var e,o,r,i,u,a,c,s,l,f=(r=(e=n).getParam("skin"),i=e.getParam("skin_url"),!1!==r&&(o=r||"oxide",i=i?e.documentBaseURI.toAbsolute(i):nv.baseURL+"/skins/ui/"+o),i);f&&n.contentCSS.push(f+(t?"/content.inline":"/content")+".min.css"),!1===n.getParam("skin")==0&&y(f)?Bp.all([zk(n,f+"/skin.min.css",n.ui.styleSheetLoader),(c=n,s=f,l=At.fromDom(c.getElement()),ge(l).isSome()?zk(c,s+"/skin.shadowdom.min.css",tv.DOM.styleSheetLoader):Bp.resolve())]).then(Hk(n),(u=n,a="Skin could not be loaded",function(){return u.fire("SkinLoadError",{message:a})})):Hk(n)()}function Lk(o,r){return function(n){function t(){n.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,n.setActive);e.set(t)}var e=fc();return o.initialized?t():o.once("init",t),function(){o.off("init",t),e.clear()}}}function Wk(o,r,i){return function(t){function n(){return i(t)}function e(){i(t),o.on(r,n)}return o.initialized?e():o.once("init",e),function(){o.off("init",e),o.off(r,n)}}}function Uk(n){return function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("mceToggleFormat",!1,t.format)})}}}function jk(t,n){return function(){return t.execCommand(n)}}function Gk(t,n,e){var u,a,c,o=e.dataset,r="basic"===o.type?function(){return M(o.data,function(t){return hC(t,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:(u=n,a=e,c=function(t,n,e){var o="formatter"===t.type&&a.isInvalid(t);return 0===n?o?[]:i(t,n,!1,e).toArray():i(t,n,o,e).toArray()},{validateItems:s,getFetch:function(e,o){return function(t,n){n(z0(s(o()),xh.CLOSE_ON_EXECUTE,e,!1))}}}),getStyleItems:r};function i(t,n,e,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return vt.some({type:"separator",text:r});if("submenu"!==t.type)return vt.some(lt({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:e,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=z(t.getStyleItems(),function(t){return c(t,n,o)});return 0===n&&i.length<=0?vt.none():vt.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return z(t.getStyleItems(),function(t){return c(t,n,o)})}})}function s(t){var n=a.getCurrentValue(),e=a.shouldHide?0:1;return z(t,function(t){return c(t,e,n)})}}function Xk(t,n,e){var o=Gk(0,n,e),r=o.items,i=o.getStyleItems,u=Wk(t,"NodeChange",function(t){var n=t.getComponent();e.updateText(n)});return P0({text:e.icon.isSome()?vt.none():e.text,icon:e.icon,tooltip:vt.from(e.tooltip),role:vt.none(),fetch:r.getFetch(n,i),onSetup:u,getApi:function(t){return{getComponent:rt(t)}},columns:1,presets:"normal",classes:e.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}function Yk(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return S1(t)}return!1}(t)&&!C1(t)}function qk(r,t){function n(t){return pl(r)}function e(e,o){return function(t,n){return fl(t,r,n).map(function(t){return e(t,n)}).getOr(o)}}function o(t,n){return"true"!==rn(t.element,"aria-hidden")}var i,u=e(o,!1),a=e(function(t,n){var e;o(t)&&(fn(e=t.element,"display","none"),on(e,"aria-hidden","true"),vr(t,Ki(),{name:n,visible:!1}))}),c=(i=a,function(n,t){St(t,function(t){return i(n,t)})}),s=e(function(t,n){var e;o(t)||(vn(e=t.element,"display"),cn(e,"aria-hidden"),vr(t,Ki(),{name:n,visible:!0}))});return{uid:r.uid,dom:r.dom,components:t,behaviours:Qs(r.slotBehaviours),apis:{getSlotNames:n,getSlot:function(t,n){return fl(t,r,n)},isShowing:u,hideSlot:a,hideAllSlots:function(t){return c(t,n())},showSlot:s}}}function Kk(t,n){fl(t,n,"overflow").each(function(e){kk(t,n,function(t){var n=M(t,function(t){return Tu(t)});F1.setGroups(e,n)}),fl(t,n,"overflow-button").each(function(t){Z1.hasGrown(e)&&zg.on(t)}),Z1.refresh(e)})}var Jk,$k,Qk,Zk="data-value",t1=function(n,e,t,o){return M(t,function(t){return nC(t)?{type:"togglemenuitem",text:t.text,value:t.value,active:t.value===o,onAction:function(){Tf.setValue(n,t.value),vr(n,py,{name:e}),Vg.focus(n)}}:{type:"nestedmenuitem",text:t.text,getSubmenuItems:function(){return t1(n,e,t.items,o)}}})},n1=function(t,n){return K(t,function(t){return nC(t)?ot(t.value===n,t):n1(t.items,n)})},e1=wl({name:"HtmlSelect",configFields:[fo("options"),$s("selectBehaviours",[Vg,Tf]),To("selectClasses",[]),To("selectAttributes",{}),xo("data")],factory:function(e,t){var n=M(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return cr("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:Zs(e.selectBehaviours,[Vg.config({}),Tf.config({store:lt({mode:"manual",getValue:function(t){return $r(t.element)},setValue:function(t,n){V(e.options,function(t){return t.value===n}).isSome()&&Qr(t.element,n)}},o)})])}}}),o1=Object.freeze({__proto__:null,events:function(t,n){var e=t.stream.streams.setup(t,n);return nu([Cr(t.event,e),ou(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[Cr(t,function(){return n.cancel()})]}).getOr([])))}}),r1=Object.freeze({__proto__:null,throttle:rC,init:function(t){return t.stream.streams.state(t)}}),i1=ya({fields:[mo("stream",so("mode",{throttle:[fo("delay"),To("stopEvent",!0),Zu("streams",{setup:function(t,n){var e=t.stream,o=pp(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:rC})]})),To("event","input"),xo("cancelEvent"),$u("onStream")],name:"streaming",active:o1,state:r1}),u1=function(t){uC(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},a1=rt("alloy.typeahead.itemexecute"),c1=rt([xo("lazySink"),fo("fetch"),To("minChars",5),To("responseTime",1e3),Ku("onOpen"),To("getHotspot",vt.some),To("getAnchorOverrides",rt({})),To("layouts",vt.none()),To("eventOrder",{}),Ro("model",{},[To("getDisplayText",function(t){return void 0!==t.meta&&void 0!==t.meta.text?t.meta.text:t.value}),To("selectsOver",!0),To("populateFromBrowse",!0)]),Ku("onSetValue"),Ju("onExecute"),Ku("onItemExecute"),To("inputClasses",[]),To("inputAttributes",{}),To("inputStyles",{}),To("matchWidth",!0),To("useMinWidth",!1),To("dismissOnBlur",!0),Yu(["openClass"]),xo("initialData"),$s("typeaheadBehaviours",[Vg,Tf,i1,Mg,zg,Yy]),ar("previewing",function(){return Vo(!0)})].concat(ky()).concat(ex())),s1=rt([Kf({schema:[Xu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){!function(t,n,o){if(t.selectsOver){var e=Tf.getValue(n),r=t.getDisplayText(e),i=Tf.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?vt.some(function(){var e;iC(0,n,o),e=r.length,uC(n,function(t,n){return t.setSelectionRange(e,n.length)})}):vt.none()}return vt.none()}(o.model,t,e).fold(function(){return gd.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&iC(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOptional().map(function(t){return vr(t,a1(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&iC(o.model,t,n)})}}}})]),l1=Sl({name:"Typeahead",configFields:c1(),partFields:s1(),factory:function(r,t,n,i){function e(t,n,e){r.previewing.set(!1);var o=Yy.getCoupled(t,"sandbox");hf.isOpen(o)?ud.getCurrent(o).each(function(t){gd.getHighlighted(t).fold(function(){e(t)},function(){xr(o,t.element,"keydown",n)})}):$y(r,u(t),t,o,i,function(t){ud.getCurrent(t).each(e)},Iy.HighlightFirst).get(st)}function u(e){return function(t){return t.map(function(t){var n=z(Z(t.menus),function(t){return F(t.items,function(t){return"item"===t.type})});return Tf.getState(e).update(M(n,function(t){return t.data})),t})}}var o=ty(r),a=[Vg.config({}),Tf.config({onSetValue:r.onSetValue,store:lt({mode:"dataset",getDataKey:function(t){return $r(t.element)},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){Qr(t.element,r.model.getDisplayText(n))}},r.initialData.map(function(t){return cr("initialValue",t)}).getOr({}))}),i1.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e,o=Yy.getCoupled(t,"sandbox");Vg.isFocused(t)&&$r(t.element).length>=r.minChars&&(e=ud.getCurrent(o).bind(function(t){return gd.getHighlighted(t).map(Tf.getValue)}),r.previewing.set(!0),$y(r,u(t),t,o,i,function(t){ud.getCurrent(o).each(function(t){e.fold(function(){r.model.selectsOver&&gd.highlightFirst(t)},function(n){gd.highlightBy(t,function(t){return Tf.getValue(t).value===n.value}),gd.getHighlighted(t).orThunk(function(){return gd.highlightFirst(t),vt.none()})})})},Iy.HighlightFirst).get(st))},cancelEvent:Hi()}),Mg.config({mode:"special",onDown:function(t,n){return e(t,n,gd.highlightFirst),vt.some(!0)},onEscape:function(t){var n=Yy.getCoupled(t,"sandbox");return hf.isOpen(n)?(hf.close(n),vt.some(!0)):vt.none()},onUp:function(t,n){return e(t,n,gd.highlightLast),vt.some(!0)},onEnter:function(n){var t=Yy.getCoupled(n,"sandbox"),e=hf.isOpen(t);if(e&&!r.previewing.get())return ud.getCurrent(t).bind(function(t){return gd.getHighlighted(t)}).map(function(t){return vr(n,a1(),{item:t}),!0});var o=Tf.getValue(n);return hr(n,Hi()),r.onExecute(t,n,o),e&&hf.close(t),vt.some(!0)}}),zg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),Yy.config({others:{sandbox:function(t){return tx(r,t,{onOpen:function(){return zg.on(t)},onClose:function(){return zg.off(t)}})}}}),fm("typeaheadevents",[iu(function(t){Qy(r,u(t),t,i,st,Iy.HighlightFirst).get(st)}),Cr(a1(),function(t,n){var e=Yy.getCoupled(t,"sandbox");iC(r.model,t,n.event.item),hr(t,Hi()),r.onItemExecute(t,e,n.event.item,Tf.getValue(t)),hf.close(e),u1(t)})].concat(r.dismissOnBlur?[Cr(Bi(),function(t){var n=Yy.getCoupled(t,"sandbox");Oa(n.element).isNone()&&hf.close(n)})]:[]))];return{uid:r.uid,dom:ny(Xo(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:lt(lt({},o),Zs(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),f1=function(i){return lt(lt({},i),{toCached:function(){return f1(i.toCached())},bindFuture:function(n){return f1(i.bind(function(t){return t.fold(function(t){return Ly(Re.error(t))},function(t){return n(t)})}))},bindResult:function(n){return f1(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return f1(i.map(function(t){return t.map(n)}))},mapError:function(n){return f1(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return f1(Ny(function(n){var e=!1,o=setTimeout(function(){e=!0,n(Re.error(r()))},t);i.get(function(t){e||(clearTimeout(o),n(t))})}))}})},d1=function(t){return f1(Ny(t))},m1={type:"separator"},g1=Fr("aria-invalid"),p1={bar:dC(function(t,n){return e=n.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:M(t.items,e.interpreter)};var e}),collection:dC(function(t,n){return u=t,a=n.shared.providers,c=u.label.map(function(t){return Zb(t,a)}),s=e(function(t,n,e,o){n.stop(),a.isDisabled()||vr(t,by,{name:u.name,value:o})}),l=[Cr(hi(),e(function(t,n,e){Sa(e)})),Cr(Ci(),s),Cr(Ri(),s),Cr(vi(),e(function(t,n,e){Fu(t.element,"."+Dh).each(function(t){Yr(t,Dh)}),Xr(e,Dh)})),Cr(bi(),e(function(t){Fu(t.element,"."+Dh).each(function(t){Yr(t,Dh)})})),iu(e(function(t,n,e,o){vr(t,by,{name:u.name,value:o})}))],Qb(c,sy.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:h},behaviours:tc([dd.config({disabled:a.isDisabled,onDisabled:function(t){o(t,function(t){Xr(t,"tox-collection__item--state-disabled"),on(t,"aria-disabled",!0)})},onEnabled:function(t){o(t,function(t){Yr(t,"tox-collection__item--state-disabled"),cn(t,"aria-disabled")})}}),mv(),Fg.config({}),Tf.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){var n=o,e=M(t,function(t){var n=lp.translate(t.text),e=1===u.columns?'<div class="tox-collection__item-label">'+n+"</div>":"",o='<div class="tox-collection__item-icon">'+t.icon+"</div>",r={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,function(t){return r[t]});return'<div class="tox-collection__item'+(a.isDisabled()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+dy.encodeAllRaw(t.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),r=M("auto"!==u.columns&&1<u.columns?B(e,u.columns):[e],function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"});Mr(n.element,r.join("")),"auto"===u.columns&&ih(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;Mg.setGridSize(o,n,e)}),hr(o,Cy)}}),fy.config({}),Mg.config(1===(i=u.columns)?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===i?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:"."+Sh}}),fm("collection-events",l)]),eventOrder:((r={})[Fi()]=["disabling","alloy.base.behaviour","collection-events"],r)}),["tox-form__group--collection"],[]);function e(o){return function(n,e){Iu(e.event.target,"[data-collection-item-value]").each(function(t){o(n,e,t,rn(t,"data-collection-item-value"))})}}function o(t,n){return M(ps(t.element,".tox-collection__item"),n)}var u,a,r,i,c,s,l}),alertbanner:dC(function(t,n){return o=n.shared.providers,uy.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+(e=t).level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[sp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Lm(e.icon,o.icons),attributes:{title:o.translate(e.iconTooltip)}},action:function(t){vr(t,by,{name:"alert-banner",value:e.url})},buttonBehaviours:tc([Wm()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:dC(function(t,n){return o=n.shared.providers,oC({name:(e=t).name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:e.disabled,classname:"tox-textfield",validation:vt.none(),maximized:e.maximized},o);var e,o}),textarea:dC(function(t,n){return o=n.shared.providers,oC({name:(e=t).name,multiline:!0,label:e.label,inputMode:vt.none(),placeholder:e.placeholder,flex:!0,disabled:e.disabled,classname:"tox-textarea",validation:vt.none(),maximized:e.maximized},o);var e,o}),label:dC(function(t,n){return r={dom:{tag:"label",innerHtml:(o=n.shared).providers.translate((e=t).label),classes:["tox-label"]}},i=M(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:tc([ow(),Fg.config({}),rw(vt.none()),Mg.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(Jk=function(t,n){return e=t,o=n.shared.providers,u=gS&&e.sandboxed,a=lt(lt({},e.label.map(function(t){return{title:t}}).getOr({})),u?{sandbox:"allow-scripts allow-same-origin"}:{}),r=u,i=Vo(""),c={getValue:function(t){return i.get()},setValue:function(t,n){var e;r?on(t.element,"srcdoc",n):(on(t.element,"src","javascript:''"),(e=t.element.dom.contentWindow.document).open(),e.write(n),e.close()),i.set(n)}},Qb(e.label.map(function(t){return Zb(t,o)}),sy.parts.field({factory:{sketch:function(t){return c0({uid:t.uid,dom:{tag:"iframe",attributes:a},behaviours:tc([fy.config({}),Vg.config({}),lS(vt.none(),c.getValue,c.setValue)])})}}}),["tox-form__group--stretched"],[]);var e,o,r,i,u,a,c},function(t,n,e){var o=Xo(n,{source:"dynamic"});return dC(Jk)(t,o,e)}),button:dC(function(t,n){return e=t,o=n.shared.providers,r=Y0(e.name,"custom"),Qb(vt.none(),sy.parts.field(lt({factory:sp},G0(e,vt.some(r),o,[fS(""),ow()]))),[],[]);var e,o,r}),checkbox:dC(function(t,n){return r=t,i=n.shared.providers,u=Tf.config({store:{mode:"manual",getValue:function(t){return t.element.dom.checked},setValue:function(t,n){t.element.dom.checked=n}}}),a=sy.parts.field({factory:{sketch:h},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:tc([ow(),dd.config({disabled:function(){return r.disabled||i.isDisabled()}}),fy.config({}),Vg.config({}),u,Mg.config({mode:"special",onEnter:e,onSpace:e,stopSpaceKeyup:!0}),fm("checkbox-events",[Cr(Si(),function(t,n){vr(t,py,{name:r.name})})])])}),c=sy.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:i.translate(r.label)},behaviours:tc([mw.config({})])}),s=Pm({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]}),sy.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[a,s.asSpec(),c],fieldBehaviours:tc([dd.config({disabled:function(){return r.disabled||i.isDisabled()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){sy.getField(t).each(dd.disable)},onEnabled:function(t){sy.getField(t).each(dd.enable)}}),mv()])});function e(t){return t.element.dom.click(),vt.some(!0)}function o(t){return jm("checked"===t?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t]},i.icons)}var r,i,u,a,c,s}),colorinput:dC(function(t,n){return e=t,r=n.shared,i=n.colorinput,c=sy.parts.field({factory:Oy,inputClasses:["tox-textfield"],onSetValue:function(t){return Gy.run(t).get(st)},inputBehaviours:tc([dd.config({disabled:r.providers.isDisabled}),mv(),fy.config({}),Gy.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return Yt(t.element)},notify:{onValid:function(t){var n=Tf.getValue(t);vr(t,gw,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=Tf.getValue(t);if(0===n.length)return Ly(Re.value(!0));var e=At.fromTag("span");fn(e,"background-color",n);var o=pn(e,"background-color").fold(function(){return Re.error("blah")},function(t){return Re.value(n)});return Ly(o)}}})]),selectOnFocus:!1}),s=e.label.map(function(t){return Zb(t,r.providers)}),l=Pm((u={dom:{tag:"span",attributes:{"aria-label":r.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Xa,Ga,Ja]},onLtr:function(){return[Ga,Xa,Ja]}},components:[],fetch:yb(i.getColors(),i.hasCustomColors()),columns:i.getColorCols(),presets:"color",onItemAction:function(t,e){l.getOpt(t).each(function(n){"custom"===e?i.colorPicker(function(t){t.fold(function(){return hr(n,hw)},function(t){o(n,t),gb(t)})},"#ffffff"):o(n,"remove"===e?"":e)})}},dw.sketch({dom:u.dom,components:u.components,toggleClass:"mce-active",dropdownBehaviours:tc([Kv((a=r).providers.isDisabled),mv(),mw.config({}),fy.config({})]),layouts:u.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:a.getSink,fetch:function(n){return Ny(function(t){return u.fetch(t)}).map(function(t){return vt.from(Wb(Xo(Cb(Fr("menu-value"),t,function(t){u.onItemAction(n,t)},u.columns,u.presets,xh.CLOSE_ON_EXECUTE,O,a.providers),{movement:kb(u.columns,u.presets)})))})},parts:{menu:$p(0,0,u.presets)}}))),sy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:s.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[c,l.asSpec()]}]),fieldBehaviours:tc([fm("form-field-events",[Cr(gw,function(t,n){l.getOpt(t).each(function(t){fn(t.element,"background-color",n.event.color)}),vr(t,py,{name:e.name})}),Cr(pw,function(n,e){sy.getField(n).each(function(t){Tf.setValue(t,e.event.value),ud.getCurrent(n).each(Vg.focus)})}),Cr(hw,function(n,t){sy.getField(n).each(function(t){ud.getCurrent(n).each(Vg.focus)})})])])});function o(t,n){vr(t,pw,{value:n})}var e,r,i,u,a,c,s,l}),colorpicker:dC(function(t){var r=Pm(o0(r0,function(t){return"tox-"+t}).sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){vr(t,by,{name:"hex-valid",value:!0})},onInvalidHex:function(t){vr(t,by,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:tc([Tf.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return ud.getCurrent(n).bind(function(t){return Tf.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);ud.getCurrent(o).fold(function(){console.log("Can not find form")},function(t){Tf.setValue(t,{hex:vt.from(e[1]).getOr("")}),Qw.getField(t,"hex").each(function(t){hr(t,wi())})})}}}),ow()])}}),dropzone:dC(function(t,n){return u0(t,n.shared.providers)}),grid:dC(function(t,n){return e=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+t.columns+"col"]},components:M(t.items,e.interpreter)};var e}),listbox:dC(function(t,n){return e=t,r=(o=n).shared.providers,i=Y(e.items).filter(nC),u=e.label.map(function(t){return Zb(t,r)}),a={dom:{tag:"div",classes:["tox-listboxfield"]},components:[sy.parts.field({dom:{},factory:{sketch:function(t){return P0({uid:t.uid,text:i.map(function(t){return t.text}),icon:vt.none(),tooltip:e.label,role:vt.none(),fetch:function(t,n){n(z0(t1(t,e.name,e.items,Tf.getValue(t)),xh.CLOSE_ON_EXECUTE,o,!1))},onSetup:rt(st),getApi:rt({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[fy.config({}),Tf.config({store:{mode:"manual",initialValue:i.map(function(t){return t.value}).getOr(""),getValue:function(t){return rn(t.element,Zk)},setValue:function(n,t){n1(e.items,t).each(function(t){on(n.element,Zk,t.value),vr(n,AS,{text:t.text})})}}})]},"tox-listbox",o.shared)}}})]},sy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ft([u.toArray(),[a]]),fieldBehaviours:tc([dd.config({disabled:rt(e.disabled),onDisabled:function(t){sy.getField(t).each(dd.disable)},onEnabled:function(t){sy.getField(t).each(dd.enable)}})])});var e,o,r,i,u,a}),selectbox:dC(function(t,n){return e=t,o=n.shared.providers,r=M(e.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=e.label.map(function(t){return Zb(t,o)}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:ft([[sy.parts.field({dom:{},selectAttributes:{size:e.size},options:r,factory:e1,selectBehaviours:tc([dd.config({disabled:function(){return e.disabled||o.isDisabled()}}),fy.config({}),fm("selectbox-change",[Cr(Si(),function(t,n){vr(t,py,{name:e.name})})])])})],(1<e.size?vt.none():vt.some(jm("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},o.icons))).toArray()])},sy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ft([i.toArray(),[u]]),fieldBehaviours:tc([dd.config({disabled:function(){return e.disabled||o.isDisabled()},onDisabled:function(t){sy.getField(t).each(dd.disable)},onEnabled:function(t){sy.getField(t).each(dd.enable)}}),mv()])});var e,o,r,i,u}),sizeinput:dC(function(t,n){return Z0(t,n.shared.providers)}),urlinput:dC(function(t,n){return fC(t,n,n.urlinput)}),customeditor:dC(function(e){var o=dc(),n=Pm({dom:{tag:e.tag}}),r=dc();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:tc([fm("custom-editor-events",[eu(function(t){n.getOpt(t).each(function(n){(Tt(e,"init")?e.init(n.element.dom):uS.load(e.scriptId,e.scriptUrl).then(function(t){return t(n.element.dom,e.settings)})).then(function(n){r.on(function(t){n.setValue(t)}),r.clear(),o.set(n)})})})]),Tf.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){o.get().fold(function(){r.set(n)},function(t){return t.setValue(n)})}}}),ow()]),components:[n.asSpec()]}}),htmlpanel:dC(function(t){return"presentation"===t.presets?uy.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):uy.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:tc([fy.config({}),Vg.config({})])})}),imagetools:dC(function(t,n){return tC(t,n.shared.providers)}),table:dC(function(t,n){return eC(t,n.shared.providers)}),panel:dC(function(t,n){return{dom:{tag:"div",classes:t.classes},components:M(t.items,n.shared.interpreter)}})},h1={field:function(t,n){return n}},v1=function(n,e,o){return tt(p1,e.type).fold(function(){return console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},b1={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},y1=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],x1=function(t){return R(t,function(t,n){if(Tt(n,"items")){var e=x1(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(Tt(n,"inline")||Tt(n,"block")||Tt(n,"selector")){var o="custom-"+(y(n.name)?n.name:n.title.toLowerCase());return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return lt(lt({},t),{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},w1=aS.trim,S1=bC("true"),C1=bC("false"),k1=function(t){var n=M(ps(At.fromDom(t),"h1,h2,h3,h4,h5,h6,a:not([href])"),function(t){return t.dom});return F(M(F(n,CC),kC).concat(M(F(n,wC),OC)),_C)},O1="tinymce-url-history",_1=nd,T1=Zf,E1=rt([To("shell",!1),fo("makeItem"),To("setupItem",st),Ef("listBehaviours",[Fg])]),D1=rt([Jf({name:"items",overrides:function(){return{behaviours:tc([Fg.config({})])}}})]),B1=Sl({name:rt("CustomList")(),configFields:E1(),partFields:D1(),factory:function(s,t,n,e){var o=s.shell?{behaviours:[Fg.config({})],components:[]}:{behaviours:[],components:t};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Zs(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){var t;t=a,(s.shell?vt.some(t):fl(t,s,"items")).fold(function(){throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(n){var t=Fg.contents(n),e=c.length,o=e-t.length,r=0<o?D(o,function(){return s.makeItem()}):[],i=t.slice(e);St(i,function(t){return Fg.remove(n,t)}),St(r,function(t){return Fg.append(n,t)});var u=Fg.contents(n);St(u,function(t,n){s.setupItem(a,t,c[n],n)})})}}}},apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),M1=rt([fo("dom"),To("shell",!0),$s("toolbarBehaviours",[Fg])]),A1=rt([Jf({name:"groups",overrides:function(){return{behaviours:tc([Fg.config({})])}}})]),F1=Sl({name:"Toolbar",configFields:M1(),partFields:A1(),factory:function(o,t,n,e){var r=o.shell?{behaviours:[Fg.config({})],components:[]}:{behaviours:[],components:t};return{uid:o.uid,dom:o.dom,components:r.components,behaviours:Zs(o.toolbarBehaviours,r.behaviours),apis:{setGroups:function(t,n){var e;e=t,(o.shell?vt.some(e):fl(e,o,"groups")).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){Fg.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),I1=rt([]),R1=Object.freeze({__proto__:null,setup:st,isDocked:O,getBehaviours:I1}),V1=Po([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),P1=function(t,n,e){var o,r,i,u;t.getSystem().isConnected()&&(i=e,u=(r=n).lazyViewport(o=t),i.isDocked()&&qC(o,r,i,u),GC(o,u,i).each(function(t){t.fold(function(){return XC(o,r,i)},function(t){return YC(o,r,i,t)},function(t){qC(o,r,i,u,!0),YC(o,r,i,t)})}))},H1=Object.freeze({__proto__:null,refresh:P1,reset:KC,isDocked:function(t,n,e){return e.isDocked()},getModes:function(t,n,e){return e.getModes()},setModes:function(t,n,e,o){return e.setModes(o)}}),z1=Object.freeze({__proto__:null,events:function(o,r){return nu([Dr(Oi(),function(n,e){o.contextual.each(function(t){qr(n.element,t.transitionClass)&&(Jr(n.element,[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(n)),e.stop()})}),Cr(Wi(),function(t,n){P1(t,o,r)}),Cr(Ui(),function(t,n){KC(t,o,r)})])}}),N1=ya({fields:[_o("contextual",[go("fadeInClass"),go("fadeOutClass"),go("transitionClass"),ho("lazyContext"),Ku("onShow"),Ku("onShown"),Ku("onHide"),Ku("onHidden")]),Fo("lazyViewport",Ae),Io("modes",["top","bottom"],tr),Ku("onDocked"),Ku("onUndocked")],name:"docking",active:z1,apis:H1,state:Object.freeze({__proto__:null,init:function(t){var n=Vo(!1),e=Vo(!0),o=dc(),r=Vo(t.modes);return xu({isDocked:n.get,setDocked:n.set,getInitialPos:o.get,setInitialPos:o.set,clearInitialPos:o.clear,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")}})}})}),L1=rt(Fr("toolbar-height-change")),W1={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},U1="tox-tinymce--toolbar-sticky-on",j1="tox-tinymce--toolbar-sticky-off",G1=Object.freeze({__proto__:null,setup:function(t,n,e){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",function(){e().each(N1.reset)}),t.on("ResizeWindow ResizeEditor",function(){e().each($C)}),t.on("SkinLoaded",function(){e().each(function(t){N1.isDocked(t)?N1.reset(t):N1.refresh(t)})}),t.on("FullscreenStateChanged",function(){e().each(N1.reset)})),t.on("AfterScrollIntoView",function(b){e().each(function(t){N1.refresh(t);var n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v=t.element;Rd(v)&&(n=b,r=(o=Ut(e=v)).dom.defaultView.innerHeight,i=In(o),a=zn(u=At.fromDom(n.elm)),c=xn(u),l=(s=a.y)+c,f=Cn(e),d=xn(e),g=(m=f.top)+d,p=Math.abs(m-i.top)<2,h=Math.abs(g-(i.top+r))<2,p&&s<g?Rn(i.left,s-d,o):h&&m<l&&Rn(i.left,s-r+c+d,o))})}),t.on("PostRender",function(){ZC(t,!1)})},isDocked:function(t){return t().map(N1.isDocked).getOr(!1)},getBehaviours:tk}),X1=Jo([go("type"),mo("items",no([$o([go("name"),yo("items",tr)]),tr]))].concat(Vp)),Y1=[Co("text"),Co("tooltip"),Co("icon"),ho("fetch"),Fo("onSetup",function(){return st})],q1=Jo(H([go("type")],Y1,!0)),K1=Jo([go("type"),Co("tooltip"),Co("icon"),Co("text"),ko("select"),ho("fetch"),Fo("onSetup",function(){return st}),Mo("presets","normal",["normal","color","listpreview"]),To("columns",1),ho("onAction"),ho("onItemAction")]),J1=wl({factory:function(n,o){var t={focus:Mg.focusIn,setMenus:function(t,n){var e=M(n,function(n){return L0(nk({type:"menubutton",text:n.text,fetch:function(t){t(n.getItems())}}).mapError(function(t){return ir(t)}).getOrDie(),"tox-mbtn",o.backstage,vt.some("menuitem"))});Fg.set(t,e)}};return{uid:n.uid,dom:n.dom,components:[],behaviours:tc([Fg.config({}),fm("menubar-events",[eu(function(t){n.onSetup(t)}),Cr(hi(),function(e,t){Fu(e.element,".tox-mbtn--active").each(function(n){Iu(t.event.target,".tox-mbtn").each(function(t){Lt(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){dw.expand(t),dw.close(n),Vg.focus(t)})})})})}),Cr(qi(),function(e,t){t.event.prevFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(n){t.event.newFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(t){dw.isOpen(n)&&(dw.expand(t),dw.close(n))})})})]),Mg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return n.onEscape(t),vt.some(!0)}}),fy.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[fo("dom"),fo("uid"),fo("onEscape"),fo("backstage"),To("onSetup",st)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),$1=Object.freeze({__proto__:null,refresh:function(t,n,e){var o;e.isExpanded()&&(vn(t.element,ok(n)),o=rk(n,t.element),fn(t.element,ok(n),o))},grow:function(t,n,e){e.isExpanded()||lk(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&sk(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&ck(t,n,e)},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:fk,isShrinking:dk,isTransitioning:function(t,n,e){return fk(t,n)||dk(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?sk:lk)(t,n,e)},disableTransitions:ik}),Q1=Object.freeze({__proto__:null,exhibit:function(t,n,e){return Nr(n.expanded?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:cr(n.dimension.property,"0px")})},events:function(e,o){return nu([Dr(Oi(),function(t,n){n.event.raw.propertyName===e.dimension.property&&(ik(t,e),o.isExpanded()&&vn(t.element,e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),Z1=ya({fields:[fo("closedClass"),fo("openClass"),fo("shrinkingClass"),fo("growingClass"),xo("getAnimationRoot"),Ku("onShrunk"),Ku("onStartShrink"),Ku("onGrown"),Ku("onStartGrow"),To("expanded",!1),mo("dimension",so("property",{width:[Zu("property","width"),Zu("getDimension",function(t){return kn(t)+"px"})],height:[Zu("property","height"),Zu("getDimension",function(t){return xn(t)+"px"})]}))],name:"sliding",active:Q1,apis:$1,state:Object.freeze({__proto__:null,init:function(t){var n=Vo(t.expanded);return xu({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:S(n.set,!1),setExpanded:S(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),tO="container",nO=[$s("slotBehaviours",[])],eO=dt({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},Hr),oO=lt(lt({},eO),{sketch:function(t){var e,n={slot:function(t,n){return e.push(t),ul(tO,mk(t),n)},record:rt(e=[])},o=t(n),r=M(n.record(),function(t){return qf({name:t,pname:mk(t)})});return xl(tO,nO,r,qk,o)}}),rO=Jo([Co("icon"),Co("tooltip"),Fo("onShow",st),Fo("onHide",st),Fo("onSetup",function(){return st})]),iO=Fr("FixSizeEvent"),uO=Fr("AutoSizeEvent"),aO=Object.freeze({__proto__:null,block:function(t,n,e,o){on(t.element,"aria-busy",!0);var r=n.getRoot(t).getOr(t),i=tc([Mg.config({mode:"special",onTab:function(){return vt.some(!0)},onShiftTab:function(){return vt.some(!0)}}),Vg.config({})]),u=o(r,i),a=r.getSystem().build(u);Fg.append(r,Tu(a)),a.hasConfigured(Mg)&&n.focus&&Mg.focusIn(a),e.isBlocked()||n.onBlock(t),e.blockWith(function(){return Fg.remove(r,a)})},unblock:function(t,n,e){cn(t.element,"aria-busy"),e.isBlocked()&&n.onUnblock(t),e.clear()}}),cO=ya({fields:[Fo("getRoot",vt.none),Ao("focus",!0),Ku("onBlock"),Ku("onUnblock")],name:"blocking",apis:aO,state:Object.freeze({__proto__:null,init:function(){var n=lc(function(t){return t.destroy()});return xu({readState:n.isSet,blockWith:function(t){n.set({destroy:t})},clear:n.clear,isBlocked:n.isSet})}})}),sO=rt([$s("splitToolbarBehaviours",[Yy]),ar("builtGroups",function(){return Vo([])})]),lO=rt([Yu(["overflowToggledClass"]),ko("getOverflowBounds"),fo("lazySink"),ar("overflowGroups",function(){return Vo([])})].concat(sO())),fO=rt([qf({factory:F1,schema:M1(),name:"primary"}),Kf({schema:M1(),name:"overflow"}),Kf({name:"overflow-button"}),Kf({name:"overflow-group"})]),dO=rt(function(t,n){var e=t,o=Math.floor(n);fn(e,"max-width",ke.max(e,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"])+"px")}),mO=rt([Yu(["toggledClass"]),fo("lazySink"),ho("fetch"),ko("getBounds"),_o("fireDismissalEventInstead",[To("event",Xi())]),Nc()]),gO=rt([Kf({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:tc([zg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),Kf({factory:F1,schema:M1(),name:"toolbar",overrides:function(n){return{toolbarBehaviours:tc([Mg.config({mode:"cyclic",onEscape:function(t){return fl(t,n,"button").each(Vg.focus),vt.none()}})])}}})]),pO=Sl({name:"FloatingToolbarButton",factory:function(u,t,a,n){return lt(lt({},sp.sketch(lt(lt({},n.button()),{action:function(t){Ok(t,n)},buttonBehaviours:Df({dump:n.button().buttonBehaviours},[Yy.config({others:{toolbarSandbox:function(t){return o=t,e=a,r=u,{dom:{tag:"div",attributes:{id:(i=Ru()).id}},behaviours:tc([Mg.config({mode:"special",onEscape:function(t){return hf.close(t),vt.some(!0)}}),hf.config({onOpen:function(t,n){r.fetch().get(function(t){Tk(o,n,r,e.layouts,t),i.link(o.element),Mg.focusIn(n)})},onClose:function(){zg.off(o),Vg.focus(o),i.unlink(o.element)},isPartOf:function(t,n,e){return Hu(n,e)||Hu(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),rc.config({channels:lt(lt({},js(lt({isExtraPart:O},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),Gs({doReposition:function(){hf.getState(Yy.getCoupled(o,"toolbarSandbox")).each(function(t){_k(o,t,r,e.layouts)})}}))})])};var o,e,r,i}}})])}))),{apis:{setGroups:function(n,e){hf.getState(Yy.getCoupled(n,"toolbarSandbox")).each(function(t){Tk(n,t,u,a.layouts,e)})},reposition:function(n){hf.getState(Yy.getCoupled(n,"toolbarSandbox")).each(function(t){_k(n,t,u,a.layouts)})},toggle:function(t){Ok(t,n)},getToolbar:function(t){return hf.getState(Yy.getCoupled(t,"toolbarSandbox"))},isOpen:function(t){return hf.isOpen(Yy.getCoupled(t,"toolbarSandbox"))}}})},configFields:mO(),partFields:gO(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)},isOpen:function(t,n){return t.isOpen(n)}}}),hO=rt([fo("items"),Yu(["itemSelector"]),$s("tgroupBehaviours",[Mg])]),vO=rt([$f({name:"items",unit:"item"})]),bO=Sl({name:"ToolbarGroup",configFields:hO(),partFields:vO(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:Zs(t.tgroupBehaviours,[Mg.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),yO=Sl({name:"SplitFloatingToolbar",configFields:lO(),partFields:fO(),factory:function(e,t,n,o){var r=Pm(pO.sketch({fetch:function(){return Ny(function(t){t(Ek(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[Xa,Ga]},onRtl:function(){return[Ga,Xa]},onBottomLtr:function(){return[qa,Ya]},onBottomRtl:function(){return[Ya,qa]}},getBounds:n.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Zs(e.splitToolbarBehaviours,[Yy.config({others:{overflowGroup:function(){return bO.sketch(lt(lt({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,n){e.builtGroups.set(M(n,t.getSystem().build)),Dk(t,r,e)},refresh:function(t){return Dk(t,r,e)},toggle:function(t){r.getOpt(t).each(function(t){pO.toggle(t)})},isOpen:function(t){return r.getOpt(t).map(pO.isOpen).getOr(!1)},reposition:function(t){r.getOpt(t).each(function(t){pO.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind(pO.getToolbar)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),xO=rt([Yu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ku("onOpened"),Ku("onClosed")].concat(sO())),wO=rt([qf({factory:F1,schema:M1(),name:"primary"}),qf({factory:F1,schema:M1(),name:"overflow",overrides:function(n){return{toolbarBehaviours:tc([Z1.config({dimension:{property:"height"},closedClass:n.markers.closedClass,openClass:n.markers.openClass,shrinkingClass:n.markers.shrinkingClass,growingClass:n.markers.growingClass,onShrunk:function(t){fl(t,n,"overflow-button").each(function(t){zg.off(t),Vg.focus(t)}),n.onClosed(t)},onGrown:function(t){Mg.focusIn(t),n.onOpened(t)},onStartGrow:function(t){fl(t,n,"overflow-button").each(zg.on)}}),Mg.config({mode:"acyclic",onEscape:function(t){return fl(t,n,"overflow-button").each(Vg.focus),vt.some(!0)}})])}}}),Kf({name:"overflow-button",overrides:function(t){return{buttonBehaviours:tc([zg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),Kf({name:"overflow-group"})]),SO=Sl({name:"SplitSlidingToolbar",configFields:xO(),partFields:wO(),factory:function(o,t,n,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:Zs(o.splitToolbarBehaviours,[Yy.config({others:{overflowGroup:function(n){return bO.sketch(lt(lt({},e["overflow-group"]()),{items:[sp.sketch(lt(lt({},e["overflow-button"]()),{action:function(t){hr(n,r)}}))]}))}}}),fm("toolbar-toggle-events",[Cr(r,function(t){Bk(t,o)})])]),apis:{setGroups:function(t,n){var e=M(n,t.getSystem().build);o.builtGroups.set(e),Kk(t,o)},refresh:function(t){return Kk(t,o)},toggle:function(t){return Bk(t,o)},isOpen:function(t){return fl(t,o,"overflow").map(Z1.hasGrown).getOr(!1)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)}}}),CO=T1.optional({factory:J1,name:"menubar",schema:[fo("backstage")]}),kO=T1.optional({factory:{sketch:function(t){return B1.sketch({uid:t.uid,dom:t.dom,listBehaviours:tc([Mg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return Rk({type:t.type,uid:Fr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return t.onEscape(),vt.some(!0)}})},setupItem:function(t,n,e,o){F1.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[fo("dom"),fo("onEscape")]}),OO=T1.optional({factory:{sketch:function(t){return(t.type===hh.sliding?function(t){var n=SO.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=SO.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=Ik(t);return SO.sketch(lt(lt({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([L1()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([L1()],{type:"closed"})}}))}:t.type===hh.floating?function(i){var t=Ik(i),n=yO.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return yO.sketch(lt(lt({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element,n=zn(t),e=Gt(t),o=zn(e),r=Math.max(e.dom.scrollHeight,o.height);return Be(n.x+4,o.y,n.width-8,r)},parts:lt(lt({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[n],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}:Rk)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),vt.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[fo("dom"),fo("onEscape"),fo("getSink")]}),_O=T1.optional({factory:{sketch:function(t){var n=t.editor,e=t.sticky?tk:I1;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:tc(e(n,t.sharedBackstage))}}},name:"header",schema:[fo("dom")]}),TO=T1.optional({name:"socket",schema:[fo("dom")]}),EO=T1.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:tc([fy.config({}),Vg.config({}),Z1.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){ud.getCurrent(t).each(oO.hideAllSlots),hr(t,uO)},onGrown:function(t){hr(t,uO)},onStartGrow:function(t){vr(t,iO,{width:pn(t.element,"width").getOr("")})},onStartShrink:function(t){vr(t,iO,{width:kn(t.element)+"px"})}}),Fg.config({}),ud.config({find:function(t){return Y(Fg.contents(t))}})])}],behaviours:tc([rS(0),fm("sidebar-sliding-events",[Cr(iO,function(t,n){fn(t.element,"width",n.event.width)}),Cr(uO,function(t,n){vn(t.element,"width")})])])}}},name:"sidebar",schema:[fo("dom")]}),DO=T1.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:tc([Fg.config({}),cO.config({focus:!1}),ud.config({find:function(t){return Y(t.components())}})]),components:[]}}},name:"throbber",schema:[fo("dom")]}),BO=Sl({name:"OuterContainer",factory:function(e,t,n){return{uid:e.uid,dom:e.dom,components:t,apis:{getSocket:function(t){return _1.getPart(t,e,"socket")},setSidebar:function(t,n){_1.getPart(t,e,"sidebar").each(function(t){return pk(t,n)})},toggleSidebar:function(t,n){_1.getPart(t,e,"sidebar").each(function(t){var e=n;ud.getCurrent(t).each(function(n){ud.getCurrent(n).each(function(t){Z1.hasGrown(n)?oO.isShowing(t,e)?Z1.shrink(n):(oO.hideAllSlots(t),oO.showSlot(t,e)):(oO.hideAllSlots(t),oO.showSlot(t,e),Z1.grow(n))})})})},whichSidebar:function(t){return _1.getPart(t,e,"sidebar").bind(hk).getOrNull()},getHeader:function(t){return _1.getPart(t,e,"header")},getToolbar:function(t){return _1.getPart(t,e,"toolbar")},setToolbar:function(t,n){_1.getPart(t,e,"toolbar").each(function(t){t.getApis().setGroups(t,n)})},setToolbars:function(t,n){_1.getPart(t,e,"multiple-toolbar").each(function(t){B1.setItems(t,n)})},refreshToolbar:function(t){_1.getPart(t,e,"toolbar").each(function(t){return t.getApis().refresh(t)})},toggleToolbarDrawer:function(t){_1.getPart(t,e,"toolbar").each(function(n){var t=n.getApis().toggle;null!=t?vt.some(function(t){return t(n)}(t)):vt.none()})},isToolbarDrawerToggled:function(t){return _1.getPart(t,e,"toolbar").bind(function(n){return vt.from(n.getApis().isOpen).map(function(t){return t(n)})}).getOr(!1)},getThrobber:function(t){return _1.getPart(t,e,"throbber")},focusToolbar:function(t){_1.getPart(t,e,"toolbar").orThunk(function(){return _1.getPart(t,e,"multiple-toolbar")}).each(function(t){Mg.focusIn(t)})},setMenubar:function(t,n){_1.getPart(t,e,"menubar").each(function(t){J1.setMenus(t,n)})},focusMenubar:function(t){_1.getPart(t,e,"menubar").each(function(t){J1.focus(t)})}},behaviours:e.behaviours}},configFields:[fo("dom"),fo("behaviours")],partFields:[_O,CO,OO,kO,TO,EO,DO],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=M(e,Ak);t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=M(e,function(t){return M(t,Ak)});t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},toggleToolbarDrawer:function(t,n){t.toggleToolbarDrawer(n)},isToolbarDrawerToggled:function(t,n){return t.isToolbarDrawerToggled(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),MO={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},AO=S(Nk,!1),FO=S(Nk,!0);function IO(t,n,e,o){var r;return{type:"basic",data:(r=t.getParam(n,e,"string"),M(o===$k.SemiColon?r.replace(/;$/,"").split(";"):r.split(" "),function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}}function RO(e){var t={type:"basic",data:g_};return{tooltip:"Align",text:vt.none(),icon:vt.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(t){return vt.none},onAction:function(n){return function(){return V(g_,function(t){return t.format===n.format}).each(function(t){return e.execCommand(t.command)})}},updateText:function(t){var n=V(g_,function(t){return e.formatter.match(t.format)}).fold(rt("left"),function(t){return t.title.toLowerCase()});vr(t,FS,{icon:"align-"+n})},dataset:t,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}}function VO(t){return M(t.split(/\s*,\s*/),function(t){return t.replace(/^['"]+|['"]+$/g,"")})}function PO(r){function i(){function e(t){return t?VO(t)[0]:""}var t=r.queryCommandValue("FontName"),n=a.data,o=t?t.toLowerCase():"";return{matchOpt:V(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return ot(0===(t=o).indexOf("-apple-system")&&(n=VO(t.toLowerCase()),N(p_,function(t){return-1<n.indexOf(t.toLowerCase())})),{title:u,format:o});var t,n}),font:t}}var u="System Font",a=IO(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",$k.SemiColon);return{tooltip:"Fonts",text:vt.some(u),icon:vt.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return vt.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},updateText:function(t){var n=i(),e=n.matchOpt,o=n.font,r=e.fold(rt(o),function(t){return t.title});vr(t,AS,{text:r})},dataset:a,shouldHide:!1,isInvalid:O}}function HO(e){function i(){var a=vt.none(),c=o.data,s=e.queryCommandValue("FontSize");if(s)for(var t=function(t){var n,e,o,r,i=(e=t,/[0-9.]+px$/.test(n=s)?(o=72*parseInt(n,10)/96,r=Math.pow(10,e||0),Math.round(o*r)/r+"pt"):tt(v_,n).getOr(n)),u=tt(h_,i).getOr("");a=V(c,function(t){return t.format===s||t.format===i||t.format===u})},n=3;a.isNone()&&0<=n;n--)t(n);return{matchOpt:a,size:s}}var t=rt(vt.none),o=IO(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",$k.Space);return{tooltip:"Font sizes",text:vt.some("12pt"),icon:vt.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,t.format)})}},updateText:function(t){var n=i(),e=n.matchOpt,o=n.size,r=e.fold(rt(o),function(t){return t.title});vr(t,AS,{text:r})},dataset:o,shouldHide:!1,isInvalid:O}}function zO(t,n){var e=n(),o=M(e,function(t){return t.format});return vt.from(t.formatter.closest(o)).bind(function(n){return V(e,function(t){return t.format===n})}).orThunk(function(){return ot(t.formatter.match("p"),{title:"Paragraph",format:"p"})})}function NO(e){var o=IO(e,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",$k.SemiColon);return{tooltip:"Blocks",text:vt.some("Paragraph"),icon:vt.none(),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(n){return function(){var t=e.formatter.get(n);return vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(n))})}},onAction:Uk(e),updateText:function(t){var n=zO(e,function(){return o.data}).fold(rt("Paragraph"),function(t){return t.title});vr(t,AS,{text:n})},dataset:o,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}}function LO(r,t){return{tooltip:"Formats",text:vt.some("Paragraph"),icon:vt.none(),isSelectedFor:function(t){return function(){return r.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(n){return function(){var t=r.formatter.get(n);return void 0!==t?vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:r.dom.parseStyle(r.formatter.getCssText(n))}):vt.none()}},onAction:Uk(r),updateText:function(t){var e=function(t){var n=t.items;return void 0!==n&&0<n.length?z(n,e):[{title:t.title,format:t.format}]},n=z(pC(r),e),o=zO(r,rt(n)).fold(rt("Paragraph"),function(t){return t.title});vr(t,AS,{text:o})},shouldHide:r.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!r.formatter.canApply(t.format)},dataset:t}}function WO(n){return{isDisabled:function(){return dd.isDisabled(n)},setDisabled:function(t){return dd.set(n,t)}}}function UO(n){return{setActive:function(t){zg.set(n,t)},isActive:function(){return zg.isOn(n)},isDisabled:function(){return dd.isDisabled(n)},setDisabled:function(t){return dd.set(n,t)}}}function jO(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})}function GO(n,e,t,o,r,i){var u;return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]),attributes:jO(t,i)},components:Qv([n.map(function(t){return I0(t,i.icons)}),e.map(function(t){return V0(t,"tox-tbtn",i)})]),eventOrder:((u={})[di()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:tc([Jv(i.isDisabled),mv(),fm("common-button-display-events",[Cr(di(),function(t,n){n.event.prevent(),hr(t,k_)})])].concat(o.map(function(t){return x_.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return Qv([t.icon.map(function(t){return I0(t,i.icons)}),t.text.map(function(t){return V0(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function XO(t,n,e){var o,r=Vo(st),i=GO(t.icon,t.text,t.tooltip,vt.none(),vt.none(),e);return sp.sketch({dom:i.dom,components:i.components,eventOrder:MS,buttonBehaviours:tc([fm("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},iu(function(n,t){gv(o,n)(function(t){vr(n,BS,{buttonApi:t}),o.onAction(t)})})),pv(n,r),hv(n,r)]),Jv(function(){return t.disabled||e.isDisabled()}),mv()].concat(n.toolbarButtonBehaviours))})}function YO(r,i){return function(t,n,e){var o=r(t).mapError(function(t){return ir(t)}).getOrDie();return i(o,n,e)}}function qO(e,t,o,r){var n,i=t.outerContainer,u=o.toolbar,a=o.buttons;f(u,y)?(n=u.map(function(t){var n={toolbar:t,buttons:a,allowToolbarGroups:o.allowToolbarGroups};return B_(e,n,{backstage:r},vt.none())}),BO.setToolbars(i,n)):BO.setToolbar(i,B_(e,o,{backstage:r},vt.none()))}function KO(t){return/^[0-9\.]+(|px)$/i.test(""+t)?vt.some(parseInt(""+t,10)):vt.none()}function JO(t){return u(t)?t+"px":t}function $O(n,t,e){var o=t.filter(function(t){return n<t}),r=e.filter(function(t){return t<n});return o.or(r).getOr(n)}function QO(t){var n=ch(t),e=sh(t),o=fh(t);return KO(n).map(function(t){return $O(t,e,o)})}function ZO(u,a,t,n,c){function s(){return x.get()&&!u.removed}function l(t){return y?t.fold(rt(0),function(t){return 1<t.components().length?xn(t.components()[1].element):0}):0}function f(){r.broadcastOn([bf()],{})}function o(t){var n,e,o,r,i;void 0===t&&(t=!1),s()&&(m||(n=p.getOrThunk(function(){var t=KO(gn(ve(),"margin-left")).getOr(0);return kn(ve())-Cn(a).left+t}),fn(c.get().element,"max-width",n+"px")),y&&BO.refreshToolbar(d),m||(e=l(BO.getToolbar(d)),o=Me(a),r=v()?Math.max(o.y-xn(c.get().element)+e,0):o.bottom,dn(d.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(o.x)+"px"})),g&&(i=c.get(),t?N1.reset(i):N1.refresh(i)),f())}function e(t){var n,e;void 0===t&&(t=!0),!m&&g&&s()&&(n=h.getDockingMode(),(e=function(t){switch(rv(u)){case bh.auto:var n=l(BO.getToolbar(d)),e=xn(t.element)-n,o=Me(a);if(o.y>e)return"top";var r=Gt(a),i=Math.max(r.dom.scrollHeight,xn(r));return o.bottom<i-e||Ae().bottom<o.bottom-e?"bottom":"top";case bh.bottom:return"bottom";default:return bh.top,"top"}}(c.get()))!==n&&(function(t){var n=c.get();N1.setModes(n,[t]),h.setDockingMode(t);var e=v()?Cc.TopToBottom:Cc.BottomToTop;on(n.element,ts,e)}(e),t&&o(!0)))}var r=t.uiMothership,d=t.outerContainer,i=tv.DOM,m=av(u),g=lv(u),p=fh(u).or(QO(u)),h=n.shared.header,v=h.isPositionedAtTop,b=ov(u),y=b===hh.sliding||b===hh.floating,x=Vo(!1);return{isVisible:s,isPositionedAtTop:v,show:function(){x.set(!0),fn(d.element,"display","flex"),i.addClass(u.getBody(),"mce-edit-focus"),vn(r.element,"display"),e(!1),o()},hide:function(){x.set(!1),t.outerContainer&&(fn(d.element,"display","none"),i.removeClass(u.getBody(),"mce-edit-focus")),fn(r.element,"display","none")},update:o,updateMode:e,repositionPopups:f}}function t_(t,n){var e=Me(t);return{pos:n?e.y:e.bottom,bounds:e}}function n_(i,u){return Cr(BS,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return hr(e,Pi())},getValue:function(){return Tf.getValue(e)}});u.onAction(r,n.event.buttonApi)})}function e_(t,n,e){return n.bottom-t.y>=(e=void 0===e?.01:e)&&t.bottom-n.y>=e}function o_(t){var n=function(t){var n=t.getBoundingClientRect();if(n.height<=0&&n.width<=0){var e=Qt(At.fromDom(t.startContainer),t.startOffset).element;return(Xn(e)?Yt(e):vt.some(e)).filter(Gn).map(function(t){return t.dom.getBoundingClientRect()}).getOr(n)}return n}(t.selection.getRng());if(t.inline){var e=In();return Be(e.left+n.left,e.top+n.top,n.width,n.height)}var o=zn(At.fromDom(t.getBody()));return Be(o.x+n.left,o.y+n.top,n.width,n.height)}function r_(t,n,e,o){void 0===o&&(o=0);var r,i,u,a,c=Pn(window),s=Me(At.fromDom(t.getContentAreaContainer())),l=mh(t)||gh(t)||ev(t),f=(r=s,i=c,u=o,{x:a=Math.max(r.x+u,i.x),width:Math.min(r.right-u,i.right)-a}),d=f.x,m=f.width;if(t.inline&&!l)return Be(d,c.y,m,c.height);var g=function(t,n,e,o,r,i){var u=At.fromDom(t.getContainer()),a=Fu(u,".tox-editor-header").getOr(u),c=Me(a),s=c.y>=n.bottom,l=o&&!s;if(t.inline&&l)return{y:Math.max(c.bottom+i,e.y),bottom:e.bottom};if(t.inline&&!l)return{y:e.y,bottom:Math.min(c.y-i,e.bottom)};var f="line"===r?Me(u):n;return l?{y:Math.max(c.bottom+i,e.y),bottom:Math.min(f.bottom-i,e.bottom)}:{y:Math.max(f.y+i,e.y),bottom:Math.min(c.y-i,e.bottom)}}(t,s,c,n.header.isPositionedAtTop(),e,o),p=g.y,h=g.bottom;return Be(d,p,m,h-p)}function i_(t){return"node"===t}function u_(t,r,n,i,e){var u=o_(t),o=i.lastElement().exists(function(t){return Lt(n,t)}),a=n,c=t.selection.getRng(),s=Qt(At.fromDom(c.startContainer),c.startOffset);return c.startContainer===c.endContainer&&c.startOffset===c.endOffset-1&&Lt(s.element,a)?o?Im:up:o?function(n,t){var e=pn(n,"position");fn(n,"position",t);var o=e_(u,Me(r))&&!i.isReposition()?Vm:Im;return e.each(function(t){return fn(n,"position",t)}),o}(r,i.getMode()):("fixed"===i.getMode()?e.y+In().top:e.y)+(xn(r)+12)<=u.y?up:ap}function a_(n,t){var e=A(F(t,function(t){return t.predicate(n.dom)}),function(t){return"contexttoolbar"===t.type});return{contextToolbars:e.pass,contextForms:e.fail}}function c_(e,t){function o(t){return Lt(t,r)}var n,r=At.fromDom(t.getBody()),i=At.fromDom(t.selection.getNode());return o(n=i)||Wt(r,n)?function(t,n,e){var o=a_(t,n);if(0<o.contextForms.length)return vt.some({elem:t,toolbars:[o.contextForms[0]]});var r=a_(t,e);if(0<r.contextForms.length)return vt.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;function n(n){return T(t,function(t){return t.position===n})}function e(n){return F(t,function(t){return t.position===n})}var o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=M(e("selection"),function(t){return lt(lt({},t),{position:"node"})});return i.concat(u)}return e(o?"selection":"node")}return e("line")}(o.contextToolbars.concat(r.contextToolbars));return vt.some({elem:t,toolbars:i})}return vt.none()}(i,e.inNodeScope,e.inEditorScope).orThunk(function(){return a=e,(t=o)(n=i)?vt.none():fr(n,function(t){if(Gn(t)){var n=a_(t,a.inNodeScope),e=n.contextToolbars,o=n.contextForms,r=0<o.length?o:(u=e).length<=1?u:i("selection").orThunk(function(){return i("node")}).orThunk(function(){return i("line")}).map(function(t){return t.position}).fold(function(){return[]},function(n){return F(u,function(t){return t.position===n})});return 0<r.length?vt.some({elem:t,toolbars:r}):vt.none()}function i(n){return V(u,function(t){return t.position===n})}var u;return vt.none()},t);var t,n,a}):vt.none()}function s_(a,c){var s={},l=[],f=[],d={},m={},t=kt(a);return St(t,function(t){var n,e,o,r,i,u=a[t];"contextform"===u.type?(o=t,i=ao(uo("ContextForm",Xp,r=u)),(s[o]=i).launch.map(function(t){d["form:"+o]=lt(lt({},r.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){c(i)}})}),("editor"===i.scope?f:l).push(i),m[o]=i):"contexttoolbar"===u.type&&(n=t,uo("ContextToolbar",Yp,e=u).each(function(t){("editor"===e.scope?f:l).push(t),m[n]=t}))}),{forms:s,inNodeScope:l,inEditorScope:f,lookupTable:m,formNavigators:d}}function l_(d,t,m,u){function a(){var t=y.get().getOr("node"),n=i_(t)?1:0;return r_(d,p,t,n)}function c(){return!(d.removed||h()&&g.isContextMenuOpen())}function s(){if(c()){var t=a(),n=mt(y.get(),"node")?(e=d,v.get().filter(he).map(zn).getOrThunk(function(){return o_(e)})):o_(d);return t.height<=0||!e_(n,t)}return 1;var e}function n(){v.clear(),b.clear(),y.clear(),rp.hide(x)}function e(){var t;rp.isOpen(x)&&(vn(t=x.element,"display"),s()?fn(t,"display","none"):(b.set(0),rp.reposition(x)))}function l(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:tc([Mg.config({mode:"acyclic"}),fm("pop-dialog-wrap-events",[eu(function(t){d.shortcuts.add("ctrl+F9","focus statusbar",function(){return Mg.focusIn(t)})}),ou(function(t){d.shortcuts.remove("ctrl+F9")})])])}}function f(t,n){var e,o,r,i,c,s,u,a="node"===t?p.anchors.node(n):p.anchors.cursor(),l=(e=d,o=t,r=h(),i={lastElement:v.get,isReposition:function(){return mt(b.get(),0)},getMode:function(){return sf.getMode(m)}},"line"===o?{bubble:xc(12,0,P_),layouts:{onLtr:function(){return[sa]},onRtl:function(){return[la]}},overrides:H_}:{bubble:xc(0,12,P_,1/12),layouts:(c=e,s=i,u=o,r?{onLtr:function(t){return[Ja,Ga,Xa,Ya,qa,Ka].concat(f(t))},onRtl:function(t){return[Ja,Xa,Ga,qa,Ya,Ka].concat(f(t))}}:{onLtr:function(t){return[Ka,Ja,Ya,Ga,qa,Xa].concat(f(t))},onRtl:function(t){return[Ka,Ja,qa,Xa,Ya,Ga].concat(f(t))}}),overrides:H_});function f(t){return i_(u)?[(a=t,function(t,n,e,o,r){var i=u_(c,o,a,s,r),u=lt(lt({},t),{y:r.y,height:r.height});return lt(lt({},i(u,n,e,o,r)),{alwaysFit:!0})})]:[];var a}return Xo(a,l)}function o(t,n){var e,o,r,i;C.cancel(),c()&&(e=S(t),r=f(o=t[0].position,n),y.set(o),b.set(1),vn(i=x.element,"display"),mt(Et(n,v.get(),Lt),!0)||(Yr(i,U_),sf.reset(m,x)),rp.showWithinBounds(x,l(e),{anchor:r,transition:{classes:[U_],mode:"placement"}},function(){return vt.some(a())}),n.fold(v.clear,v.set),s()&&fn(i,"display","none"))}var r,i,g=u.backstage,p=g.shared,h=se().deviceType.isTouch,v=dc(),b=dc(),y=dc(),x=_u((r={sink:m,onEscape:function(){return d.focus(),vt.some(!0)}},i=Vo([]),rp.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){i.set([]),rp.getContent(t).each(function(t){vn(t.element,"visibility")}),Yr(t.element,W_),vn(t.element,"width")},inlineBehaviours:tc([fm("context-toolbar-events",[Dr(Oi(),function(t,n){"width"===n.event.raw.propertyName&&(Yr(t.element,W_),vn(t.element,"width"))}),Cr(L_,function(t,n){var e=t.element;vn(e,"width");var o=kn(e);rp.setContent(t,n.event.contents),Xr(e,W_);var r=kn(e);fn(e,"width",o+"px"),rp.getContent(t).each(function(t){n.event.focus.bind(function(t){return Sa(t),Oa(e)}).orThunk(function(){return Mg.focusIn(t),ka(me(e))})}),cp.setTimeout(function(){fn(t.element,"width",r+"px")},0)}),Cr(z_,function(n,t){rp.getContent(n).each(function(t){i.set(i.get().concat([{bar:t,focus:ka(me(n.element))}]))}),vr(n,L_,{contents:t.event.forwardContents,focus:vt.none()})}),Cr(N_,function(n,t){q(i.get()).each(function(t){i.set(i.get().slice(0,i.get().length-1)),vr(n,L_,{contents:Tu(t.bar),focus:t.focus})})})]),Mg.config({mode:"special",onEscape:function(n){return q(i.get()).fold(function(){return r.onEscape()},function(t){return hr(n,N_),vt.some(!0)})}})]),lazySink:function(){return Re.value(r.sink)}}))),w=Rt(function(){return s_(t,function(t){var n=S([t]);vr(x,z_,{forwardContents:l(n)})})}),S=function(t){var n=d.ui.registry.getAll().buttons,e=w(),o=lt(lt({},n),e.formNavigators),r=ov(d)===hh.scrolling?hh.scrolling:hh.default,i=ft(M(t,function(t){return"contexttoolbar"===t.type?B_(d,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},u,vt.some(["form:"])):(n=p.providers,V_(t,n));var n}));return Rk({type:r,uid:Fr("context-toolbar"),initGroups:i,onEscape:vt.none,cyclicKeying:!0,providers:p.providers})},C=pp(function(){d.hasFocus()&&!d.removed&&(qr(x.element,U_)?C.throttle():c_(w(),d).fold(n,function(t){o(t.toolbars,vt.some(t.elem))}))},17);d.on("init",function(){d.on("remove",n),d.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",e),d.on("click keyup focus SetContent",C.throttle),d.on(R_,n),d.on("contexttoolbar-show",function(n){tt(w().lookupTable,n.toolbarKey).each(function(t){o([t],ot(n.target!==d,n.target)),rp.getContent(x).each(Mg.focusIn)})}),d.on("focusout",function(t){cp.setEditorTimeout(d,function(){Oa(m.element).isNone()&&Oa(x.element).isNone()&&n()},0)}),d.on("SwitchMode",function(){d.mode.isReadOnly()&&n()}),d.on("AfterProgressState",function(t){t.state?n():d.hasFocus()&&C.throttle()}),d.on("NodeChange",function(t){Oa(x.element).fold(C.throttle,st)})})}(Qk=$k=$k||{})[Qk.SemiColon=0]="SemiColon",Qk[Qk.Space=1]="Space";var f_,d_,m_,g_=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],p_=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],h_={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},v_={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},b_=Object.freeze({__proto__:null,events:function(r,i){function u(e,o){r.updateState.each(function(t){var n=t(e,o);i.set(n)}),r.renderComponents.each(function(t){var n=M(t(o,i.get()),e.getSystem().build);Fs(e,n)})}return nu([Cr(Ai(),function(t,n){var e,o=n;o.universal||(e=r.channel,wt(o.channels,e)&&u(t,o.data))}),eu(function(n,t){r.initialData.each(function(t){u(n,t)})})])}}),y_=Object.freeze({__proto__:null,getState:function(t,n,e){return e}}),x_=ya({fields:[fo("channel"),xo("renderComponents"),xo("updateState"),xo("initialData")],name:"reflecting",active:b_,apis:y_,state:Object.freeze({__proto__:null,init:function(){var t=Vo(vt.none());return{readState:function(){return t.get().getOr("none")},get:t.get,set:t.set,clear:function(){return t.set(vt.none())}}}})}),w_=rt([fo("toggleClass"),fo("fetch"),$u("onExecute"),To("getHotspot",vt.some),To("getAnchorOverrides",rt({})),Nc(),$u("onItemExecute"),xo("lazySink"),fo("dom"),Ku("onOpen"),$s("splitDropdownBehaviours",[Yy,Mg,Vg]),To("matchWidth",!1),To("useMinWidth",!1),To("eventOrder",{}),xo("role")].concat(ex())),S_=rt([qf({factory:sp,schema:[fo("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:tc([Vg.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(br)},buttonBehaviours:tc([zg.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),qf({factory:sp,schema:[fo("dom")],name:"button",defaults:function(){return{buttonBehaviours:tc([Vg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),Jf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[fo("text")],name:"aria-descriptor"}),Kf({schema:[Xu()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),Ky()]),C_=Sl({name:"SplitDropdown",configFields:w_(),partFields:S_(),factory:function(o,t,n,e){function r(t){ud.getCurrent(t).each(function(t){gd.highlightFirst(t),Mg.focusIn(t)})}function i(t){Qy(o,h,t,e,r,Iy.HighlightFirst).get(st)}function u(t){return br(dl(t,o,"button")),vt.some(!0)}var a,c=lt(lt({},nu([eu(function(e,t){fl(e,o,"aria-descriptor").each(function(t){var n=Fr("aria");on(t.element,"id",n),on(e.element,"aria-describedby",n)})})])),xm(vt.some(i))),s={repositionMenus:function(t){zg.isOn(t)&&nx(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:s,eventOrder:lt(lt({},o.eventOrder),((a={})[Fi()]=["disabling","toggling","alloy.base.behaviour"],a)),events:c,behaviours:Zs(o.splitDropdownBehaviours,[Yy.config({others:{sandbox:function(t){var n=dl(t,o,"arrow");return tx(o,t,{onOpen:function(){zg.on(n),zg.on(t)},onClose:function(){zg.off(n),zg.off(t)}})}}}),Mg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(t){return i(t),vt.some(!0)}}),Vg.config({}),zg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),k_=Fr("focus-button"),O_=function(t,n,e){return XO(t,{toolbarButtonBehaviours:[].concat(0<e.length?[fm("toolbarButtonWith",e)]:[]),getApi:WO,onSetup:t.onSetup},n)},__=function(t,n,e){return Xo(XO(t,{toolbarButtonBehaviours:[Fg.config({}),zg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[fm("toolbarToggleButtonWith",e)]:[]),getApi:UO,onSetup:t.onSetup},n))},T_=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],E_={button:YO(Cp,function(t,n){return e=n.backstage.shared.providers,O_(t,e,[]);var e}),togglebutton:YO(kp,function(t,n){return e=n.backstage.shared.providers,__(t,e,[]);var e}),menubutton:YO(nk,function(t,n){return L0(t,"tox-tbtn",n.backstage,vt.none())}),splitbutton:YO(function(t){return uo("SplitButton",K1,t)},function(t,n){return o=t,r=n.backstage.shared,s=Fr("channel-update-split-dropdown-display"),l=Vo(st),f={getApi:e,onSetup:o.onSetup},C_.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:lt({"aria-pressed":!1},jO(o.tooltip,r.providers))},onExecute:function(t){o.onAction(e(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:tc([Gv(r.providers.isDisabled),mv(),fm("split-dropdown-events",[Cr(k_,Vg.focus),pv(f,l),hv(f,l)]),mw.config({})]),eventOrder:((i={})[ji()]=["alloy.base.behaviour","split-dropdown-events"],i),toggleClass:"tox-tbtn--enabled",lazySink:r.getSink,fetch:(u=e,a=o,c=r.providers,function(n){return Ny(function(t){return a.fetch(t)}).map(function(t){return vt.from(Wb(Xo(Cb(Fr("menu-value"),t,function(t){a.onItemAction(u(n),t)},a.columns,a.presets,xh.CLOSE_ON_EXECUTE,a.select.getOr(O),c),{movement:kb(a.columns,a.presets),menuBehaviours:Yh("auto"!==a.columns?[]:[eu(function(o,t){ih(o,4,qp(a.presets)).each(function(t){var n=t.numRows,e=t.numColumns;Mg.setGridSize(o,n,e)})})])})))})}),parts:{menu:$p(0,o.columns,o.presets)},components:[C_.parts.button(GO(o.icon,o.text,vt.none(),vt.some(s),vt.some([zg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),r.providers)),C_.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Lm("chevron-down",r.providers.icons)},buttonBehaviours:tc([Gv(r.providers.isDisabled),mv(),Wm()])}),C_.parts["aria-descriptor"]({text:r.providers.translate("To open the popup, press Shift+Enter")})]});function e(e){return{isDisabled:function(){return dd.isDisabled(e)},setDisabled:function(t){return dd.set(e,t)},setIconFill:function(t,n){Fu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){on(t,"fill",n)})},setIconStroke:function(t,n){Fu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){on(t,"stroke",n)})},setActive:function(n){on(e.element,"aria-pressed",n),Fu(e.element,"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return zg.set(t,n)})})},isActive:function(){return Fu(e.element,"span").exists(function(t){return e.getSystem().getByDom(t).exists(zg.isOn)})}}}var o,r,i,u,a,c,s,l,f}),grouptoolbarbutton:YO(function(t){return uo("GroupToolbarButton",X1,t)},function(t,n,e){var o,r,i,u,a,c,s=e.ui.registry.getAll().buttons,l=((o={})[ts]=n.backstage.shared.header.isPositionedAtTop()?Cc.TopToBottom:Cc.BottomToTop,o);if(ov(e)!==hh.floating)throw new Error("Toolbar groups are only supported when using floating toolbar mode");return i=n.backstage,u=function(t){return B_(e,{buttons:s,toolbar:t,allowToolbarGroups:!1},n,vt.none())},a=l,c=i.shared,pO.sketch({lazySink:c.getSink,fetch:function(){return Ny(function(t){t(M(u(r.items),Ak))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:GO((r=t).icon,r.text,r.tooltip,vt.none(),vt.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}})}),styleSelectButton:function(t,n){return Xk(t,e=n.backstage,LO(t,lt({type:"advanced"},e.styleselect)));var e},fontsizeSelectButton:function(t,n){return Xk(t,n.backstage,HO(t))},fontSelectButton:function(t,n){return Xk(t,n.backstage,PO(t))},formatButton:function(t,n){return Xk(t,n.backstage,NO(t))},alignMenuButton:function(t,n){return Xk(t,n.backstage,RO(t))}},D_={styleselect:E_.styleSelectButton,fontsizeselect:E_.fontsizeSelectButton,fontselect:E_.fontSelectButton,formatselect:E_.formatButton,align:E_.alignMenuButton},B_=function(o,c,s,l){var e,t,n,r,i=(n=c.toolbar,r=c.buttons,!1===n?[]:void 0===n||!0===n?(e=r,t=M(T_,function(t){var n=F(t.items,function(t){return Tt(e,t)||Tt(D_,t)});return{name:t.name,items:n}}),F(t,function(t){return 0<t.items.length})):y(n)?M(n.split("|"),function(t){return{items:t.trim().split(" ")}}):f(n,function(t){return Tt(t,"name")&&Tt(t,"items")})?n:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[]));return F(M(i,function(t){var n=z(t.items,function(t){return 0===t.trim().length?[]:(r=o,n=c.buttons,i=t,u=c.allowToolbarGroups,a=s,e=l,tt(n,i.toLowerCase()).orThunk(function(){return e.bind(function(t){return K(t,function(t){return tt(n,t+i.toLowerCase())})})}).fold(function(){return tt(D_,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return vt.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(e=a,o=r,tt(E_,(n=t).type).fold(function(){return console.error("skipping button defined by",n),vt.none()},function(t){return vt.some(t(n,e,o))})):(console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),vt.none());var n,e,o}).toArray());var r,n,i,u,a,e});return{title:vt.from(o.translate(t.name)),items:n}}),function(t){return 0<t.items.length})},M_=se(),A_=M_.os.isiOS()&&M_.os.version.major<=12,F_=Object.freeze({__proto__:null,render:function(e,n,t,o,r){var i=Vo(0),u=n.outerContainer;AO(e);var a=At.fromDom(r.targetNode),c=tn(me(a)),s=n.mothership;mf(a,s,Dn),Hs(c,n.uiMothership),e.on("PostRender",function(){qO(e,n,t,o),i.set(e.getWin().innerWidth),BO.setMenubar(u,Pk(e,t)),BO.setSidebar(u,t.sidebar),function(e,t){function n(){var t=c.get();t.left===u.innerWidth&&t.top===u.innerHeight||(c.set(Se(u.innerWidth,u.innerHeight)),Nv(e))}function o(){var t=e.getDoc().documentElement,n=s.get();n.left===t.offsetWidth&&n.top===t.offsetHeight||(s.set(Se(t.offsetWidth,t.offsetHeight)),Nv(e))}function r(t){return e.fire("ScrollContent",t)}var i=e.dom,u=e.getWin(),a=e.getDoc().documentElement,c=Vo(Se(u.innerWidth,u.innerHeight)),s=Vo(Se(a.offsetWidth,a.offsetHeight));i.bind(u,"resize",n),i.bind(u,"scroll",r);var l=gc(At.fromDom(e.getBody()),"load",o),f=t.uiMothership.element;e.on("hide",function(){fn(f,"display","none")}),e.on("show",function(){vn(f,"display")}),e.on("NodeChange",o),e.on("remove",function(){l.unbind(),i.unbind(u,"resize",n),i.unbind(u,"scroll",r),u=null})}(e,n)});var l,f,d,m,g=BO.getSocket(u).getOrDie("Could not find expected socket element");A_&&(dn(g.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"}),f=function(){e.fire("ScrollContent")},d=null,m=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];b(d)&&(d=setTimeout(function(){d=null,f.apply(null,t)},20))},l=mc(g.element,"scroll",m),e.on("remove",l.unbind)),dv(e,n),e.addCommand("ToggleSidebar",function(t,n){BO.toggleSidebar(u,n),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return BO.whichSidebar(u)});var p=ov(e);p!==hh.sliding&&p!==hh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=e.getWin().innerWidth;t!==i.get()&&(BO.refreshToolbar(n.outerContainer),i.set(t))});var h={enable:function(){fv(n,!1)},disable:function(){fv(n,!0)},isDisabled:function(){return dd.isDisabled(u)}};return{iframeContainer:g.element.dom,editorContainer:u.element.dom,api:h}}}),I_=Object.freeze({__proto__:null,render:function(n,e,o,r,t){var i=e.mothership,u=e.uiMothership,a=e.outerContainer,c=Vo(null),s=At.fromDom(t.targetNode),l=ZO(n,s,e,r,c),f=n.getParam("toolbar_persist",!1,"boolean");function d(){var t;c.get()?l.show():(c.set(BO.getHeader(a).getOrDie()),Hs(t=cv(n),i),Hs(t,u),qO(n,e,o,r),BO.setMenubar(a,Pk(n,o)),l.show(),function(c,s,l,t){function n(t){var n=t_(s,l.isPositionedAtTop()),e=n.pos,o=n.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:e,bounds:o}),a&&Nv(c,t),l.isVisible()&&(i!==e?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))}var f=Vo(t_(s,l.isPositionedAtTop()));t||(c.on("activate",l.show),c.on("deactivate",l.hide)),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){cp.requestAnimationFrame(function(){return n(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var e=fc();e.set(gc(At.fromDom(c.getBody()),"load",n)),c.on("remove",function(){e.clear()})}(n,s,l,f),n.nodeChanged())}function m(){return cp.setEditorTimeout(n,d,0)}FO(n),n.on("show",d),n.on("hide",l.hide),f||(n.on("focus",m),n.on("blur",l.hide)),n.on("init",function(){(n.hasFocus()||f)&&m()}),dv(n,e);var g={show:function(){l.show()},hide:function(){l.hide()},enable:function(){fv(e,!1)},disable:function(){fv(e,!0)},isDisabled:function(){return dd.isDisabled(a)}};return{editorContainer:a.element.dom,api:g}}}),R_="contexttoolbar-hide",V_=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=Pm(Oy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:tc([Mg.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return br(t),!0})},onLeft:function(t,n){return n.cut(),vt.none()},onRight:function(t,n){return n.cut(),vt.none()}})])})),c=(e=a,o=t.commands,r=n,i=M(o,function(t){return Pm(("contextformtogglebutton"===t.type?function(t,n,e){var o=n.original;o.primary;var r=s(o,["primary"]),i=ao(kp(lt(lt({},r),{type:"togglebutton",onAction:st})));return __(i,e.backstage.shared.providers,[n_(t,n)])}:function(t,n,e){var o=n.original;o.primary;var r=s(o,["primary"]),i=ao(Cp(lt(lt({},r),{type:"button",onAction:st})));return O_(i,e.backstage.shared.providers,[n_(t,n)])})(e,t,{backstage:{shared:{providers:r}}}))}),{asSpecs:function(){return M(i,function(t){return t.asSpec()})},findPrimary:function(e){return K(o,function(t,n){return t.primary?vt.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(C(dd.isDisabled)):vt.none()})}});return[{title:vt.none(),items:[a.asSpec()]},{title:vt.none(),items:c.asSpecs()}]},P_={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},H_={maxHeightFunction:Ic(),maxWidthFunction:dO()},z_=Fr("forward-slide"),N_=Fr("backward-slide"),L_=Fr("change-slide-event"),W_="tox-pop--resizing",U_="tox-pop--transition",j_={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},G_=(m_=["Infinity",(f_="[0-9]+")+"\\."+X_(f_)+X_(d_="[eE][+-]?[0-9]+"),"\\."+f_+X_(d_),f_+X_(d_)].join("|"),new RegExp("^([+-]?(?:"+m_+"))(.*)$"));function X_(t){return"(?:"+t+")?"}function Y_(u,a){function n(){var t=a.getOptions(u),r=a.getCurrent(u).map(a.hash),i=dc();return M(t,function(o){return{type:"togglemenuitem",text:a.display(o),onSetup:function(n){function t(t){t&&(i.on(function(t){return t.setActive(!1)}),i.set(n)),n.setActive(t)}t(mt(r,a.hash(o)));var e=a.watcher(u,o,t);return function(){i.clear(),e()}},onAction:function(){return a.setCurrent(u,o)}}})}u.ui.registry.addMenuButton(a.name,{tooltip:a.text,icon:a.icon,fetch:function(t){return t(n())},onSetup:a.onToolbarSetup}),u.ui.registry.addNestedMenuItem(a.name,{type:"nestedmenuitem",text:a.text,getSubmenuItems:n,onSetup:a.onMenuSetup})}function q_(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}}function K_(t){var n,e;!function(e){aS.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,n){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:Lk(e,t.name),onAction:q_(e,t.name)})});for(var t=1;t<=6;t++){var n="h"+t;e.ui.registry.addToggleButton(n,{text:n.toUpperCase(),tooltip:"Heading "+t,onSetup:Lk(e,n),onAction:q_(e,n)})}}(t),n=t,aS.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:jk(n,t.action)})}),e=t,aS.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:jk(e,t.action),onSetup:Lk(e,t.name)})})}function J_(n,e){return Wk(n,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(t){t.setDisabled(n.mode.isReadOnly()||!n.undoManager[e]())})}function $_(t){var n;t.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:jk(t,"mceToggleVisualAid")}),t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:Wk(n=t,"VisualAid",function(t){t.setActive(n.hasVisual)}),onAction:jk(t,"mceToggleVisualAid")})}function Q_(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,C,k,O,_,T=t;St([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){T.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:jk(T,t.cmd),onSetup:Lk(T,t.name)})}),T.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:jk(T,"JustifyNone")}),K_(O=t),_=O,aS.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){_.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:jk(_,t.action)})}),_.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:q_(_,"code")}),k=Gk(0,d=l=n,RO(f=s=t)),f.ui.registry.addNestedMenuItem("align",{text:d.shared.providers.translate("Align"),getSubmenuItems:function(){return k.items.validateItems(k.getStyleItems())}}),p=Gk(0,g=l,PO(m=s)),m.ui.registry.addNestedMenuItem("fontformats",{text:g.shared.providers.translate("Fonts"),getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}}),h=s,b=lt({type:"advanced"},(v=l).styleselect),y=Gk(0,v,LO(h,b)),h.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return y.items.validateItems(y.getStyleItems())}}),w=Gk(0,l,NO(x=s)),x.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return w.items.validateItems(w.getStyleItems())}}),C=Gk(0,l,HO(S=s)),S.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return C.items.validateItems(C.getStyleItems())}}),(a=u=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:J_(a,"hasUndo"),onAction:jk(a,"undo")}),a.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:J_(a,"hasRedo"),onAction:jk(a,"redo")}),(c=u).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",disabled:!0,onSetup:J_(c,"hasUndo"),onAction:jk(c,"undo")}),c.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",disabled:!0,onSetup:J_(c,"hasRedo"),onAction:jk(c,"redo")}),function(t){var i;(i=t).addCommand("mceApplyTextcolor",function(t,n){var e,o=t,r=n;(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e=t;(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})});var n=Vo(null),e=Vo(null);wb(t,"forecolor","forecolor","Text color",n),wb(t,"backcolor","hilitecolor","Background color",e),Sb(t,"forecolor","forecolor","Text color"),Sb(t,"backcolor","hilitecolor","Background color")}(t),$_(t),(r=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:Wk(i=r,"NodeChange",function(t){t.setDisabled(!i.queryCommandState("outdent"))}),onAction:jk(r,"outdent")}),r.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:jk(r,"indent")}),Y_(e=t,PT),o=e,vt.from(o.getParam("content_langs",void 0,"array")).map(function(t){return{name:"language",text:"Language",icon:"language",getOptions:rt(t),hash:function(t){return E(t.customCode)?t.code:t.code+"/"+t.customCode},display:function(t){return t.title},watcher:function(t,n,e){return t.formatter.formatChanged("lang",e,!1,{value:n.code,customValue:n.customCode}).unbind},getCurrent:function(t){return dr(At.fromDom(t.selection.getNode()),function(t){return vt.some(t).filter(Gn).bind(function(n){return un(n,"lang").map(function(t){return{code:t,customCode:un(n,"data-mce-lang").getOrUndefined(),title:""}})})})},setCurrent:function(t,n){return t.execCommand("Lang",!1,n)},onToolbarSetup:function(t){var n=fc();return t.setActive(o.formatter.match("lang",{},void 0,!0)),n.set(o.formatter.formatChanged("lang",t.setActive,!0)),n.clear}}}).each(function(t){return Y_(e,t)})}function Z_(t,n){return{type:"makeshift",x:t,y:n}}function tT(t){return"longpress"===t.type||0===t.type.indexOf("touch")}function nT(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?function(t){if(tT(t)){var n=t.touches[0];return Z_(n.pageX,n.pageY)}return Z_(t.pageX,t.pageY)}(n):(e=t.getContentAreaContainer(),o=function(t){if(tT(t)){var n=t.touches[0];return Z_(n.clientX,n.clientY)}return Z_(t.clientX,t.clientY)}(n),r=tv.DOM.getPos(e),i=r.x,u=r.y,Z_(o.x+i,o.y+u)):HT(t);var e,o,r,i,u}function eT(t,n,e){switch(e){case"node":return{type:"node",node:vt.some(At.fromDom((o=t).selection.getNode())),root:At.fromDom(o.getBody())};case"point":return nT(t,n);case"selection":return HT(t)}var o}function oT(f,d,m,g,p,h){function t(){var n,e,t,o,r,i,u,a,c,s,l=m();t=l,o=g,r=p,u=!(y||v||b&&x),c=eT(n=f,e=d,a=i=h),s=lt({bubble:xc(0,"point"===a?12:0,NT),layouts:zT,overrides:{maxWidthFunction:dO(),maxHeightFunction:Ic()}},c),z0(t,xh.CLOSE_ON_EXECUTE,o,!0).map(function(t){e.preventDefault(),rp.showMenuWithinBounds(r,{anchor:s},{menu:{markers:Jp("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return vt.some(r_(n,o.shared,"node"===i?"node":"selection"))}),n.fire(R_)})}var n,e=se(),v=e.os.isiOS(),b=e.os.isOSX(),y=e.os.isAndroid(),x=e.deviceType.isTouch();(b||v)&&"node"!==h?(n=function(){(function(t){function n(){cp.setEditorTimeout(t,function(){t.selection.setRng(e)},10),i()}var e=t.selection.getRng();function o(t){t.preventDefault(),t.stopImmediatePropagation()}function r(){return i()}t.once("touchend",n),t.on("mousedown",o,!0),t.once("longpresscancel",r);var i=function(){t.off("touchend",n),t.off("longpresscancel",r),t.off("mousedown",o)}})(f),t()},function(t,n){var e=t.selection;if(!(e.isCollapsed()||n.touches.length<1)){var o=n.touches[0],r=e.getRng();return bs(t.getWin(),fs.domRange(r)).exists(function(t){return t.left<=o.clientX&&t.right>=o.clientX&&t.top<=o.clientY&&t.bottom>=o.clientY})}}(f,d)?n():(f.once("selectionchange",n),f.once("touchend",function(){return f.off("selectionchange",n)}))):t()}function rT(t){return"string"==typeof t?t.split(/[ ,]/):t}function iT(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")}function uT(t){return y(t)?"|"===t:"separator"===t.type}function aT(t,n){if(0===n.length)return t;var e=q(t).filter(function(t){return!uT(t)}).fold(function(){return[]},function(t){return[LT]});return t.concat(e).concat(n).concat([LT])}function cT(t,n){return"longpress"!==n.type&&(2!==n.button||n.target===t.getBody()&&""===n.pointerType)}function sT(t,n){return cT(t,n)?t.selection.getStart(!0):n.target}function lT(s,t,n){function e(t){return rp.hide(i)}function o(c){var t;iT(s)&&c.preventDefault(),c.ctrlKey&&!iT(s)||!1===s.getParam("contextmenu")||(t=function(t,n){var e=t.getParam("contextmenu_avoid_overlap","","string"),o=cT(t,n)?"selection":"point";if(at(e)){var r=sT(t,n);return Ub(At.fromDom(r),e)?"node":o}return o}(s,c),(r()?oT:function(t,n,e,o,r,i){var u=e(),a=eT(t,n,i);z0(u,xh.CLOSE_ON_EXECUTE,o,!1).map(function(t){n.preventDefault(),rp.showMenuAt(r,{anchor:a},{menu:{markers:Jp("normal")},data:t})})})(s,c,function(){var t,n,e,o=sT(s,c),r=s.ui.registry.getAll(),i=(e=(n=s).ui.registry.getAll().contextMenus,vt.from(n.getParam("contextmenu")).map(rT).getOrThunk(function(){return F(rT("link linkchecker image imagetools table spellchecker configurepermanentpen"),function(t){return Tt(e,t)})})),u=r.contextMenus,a=o;return 0<(t=R(i,function(o,t){return tt(u,t.toLowerCase()).map(function(t){var n=t.update(a);if(y(n))return aT(o,n.split(" "));if(0<n.length){var e=M(n,WT);return aT(o,e)}return o}).getOrThunk(function(){return o.concat([t])})},[])).length&&uT(t[t.length-1])&&t.pop(),t},n,i,t))}var r=se().deviceType.isTouch,i=_u(rp.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return s.focus()},onShow:function(){return n.setContextMenuState(!0)},onHide:function(){return n.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:tc([fm("dismissContextMenu",[Cr(Xi(),function(t,n){hf.close(t),s.focus()})])])}));s.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(r()?"":" ResizeWindow");s.on(t,e),s.on("longpress contextmenu",o)})}function fT(n){return function(t){return t.translate(-n.left,-n.top)}}function dT(n){return function(t){return t.translate(n.left,n.top)}}function mT(e){return function(t,n){return R(e,function(t,n){return n(t)},Se(t,n))}}function gT(t,n,e){return t.fold(mT([dT(e),fT(n)]),mT([fT(n)]),mT([]))}function pT(t,n,e){return t.fold(mT([dT(e)]),mT([]),mT([dT(n)]))}function hT(t,n,e){return t.fold(mT([]),mT([fT(e)]),mT([dT(n),fT(e)]))}function vT(t,n,e){var o=t.fold(function(t,n){return{position:vt.some("absolute"),left:vt.some(t+"px"),top:vt.some(n+"px")}},function(t,n){return{position:vt.some("absolute"),left:vt.some(t-e.left+"px"),top:vt.some(n-e.top+"px")}},function(t,n){return{position:vt.some("fixed"),left:vt.some(t+"px"),top:vt.some(n+"px")}});return lt({right:vt.none(),bottom:vt.none()},o)}function bT(t,i,u,a){function n(o,r){return function(t,n){var e=o(i,u,a);return r(t.getOr(e.left),n.getOr(e.top))}}return t.fold(n(hT,jT),n(pT,GT),n(gT,XT))}function yT(t,n){var e=rn(t,n);return E(e)?NaN:parseInt(e,10)}function xT(t,n,e,o,r,i){var u,a,c,s,l,f,d=(u=e,a=o,l=yT(s=t.element,(c=n).leftAttr),f=yT(s,c.topAttr),(isNaN(l)||isNaN(f)?vt.none():vt.some(Se(l,f))).fold(function(){return u},function(t){return XT(t.left+a.left,t.top+a.top)})),m=(n.mustSnap?YT:qT)(t,n,d,r,i),g=gT(d,r,i),p=n,h=g,v=t.element;return on(v,p.leftAttr,h.left+"px"),on(v,p.topAttr,h.top+"px"),m.fold(function(){return{coord:XT(g.left,g.top),extra:vt.none()}},function(t){return{coord:t.output,extra:t.extra}})}function wT(t,c,s,l){return K(t,function(t){var n,e,o=t.sensor,r=t.range.left,i=t.range.top,u=pT(c,n=s,e=l),a=pT(o,n,e);return Math.abs(u.left-a.left)<=r&&Math.abs(u.top-a.top)<=i?vt.some({output:bT(t.output,c,s,l),extra:t.extra}):vt.none()})}function ST(t,n){var e;t.getSystem().addToGui(n),Yt((e=n).element).filter(Gn).each(function(n){pn(n,"z-index").each(function(t){on(n,JT,t)}),fn(n,"z-index",gn(e.element,"z-index"))})}function CT(t){Yt(t.element).filter(Gn).each(function(n){un(n,JT).fold(function(){return vn(n,"z-index")},function(t){return fn(n,"z-index",t)}),cn(n,JT)}),t.getSystem().removeFromGui(t)}function kT(t,n,e){return t.getSystem().build(uy.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))}function OT(t,n){return{bounds:t.getBounds(),height:wn(n.element),width:On(n.element)}}function _T(B,M,t,n,e){var o=t.update(n,e),A=t.getStartData().getOrThunk(function(){return OT(M,B)});o.each(function(t){var n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,C,k,O,_=B,T=A,E=t,D=(n=M).getTarget(_.element);n.repositionTarget&&(e=In(Ut(_.element)),o=zC(D),S=pn(w=D,"left"),C=pn(w,"top"),k=pn(w,"position"),O=function(t,n,e){return("fixed"===e?XT:jT)(parseInt(t,10),parseInt(n,10))},r=(S.isSome()&&C.isSome()&&k.isSome()?vt.some(O(S.getOrDie(),C.getOrDie(),k.getOrDie())):vt.none()).getOrThunk(function(){var t=Cn(w);return GT(t.left,t.top)}),i=_,u=n.snaps,a=r,c=e,s=o,l=E,f=T,m=u.fold(function(){var e,o,t=gT((e=l.left,o=l.top,a.fold(function(t,n){return jT(t+e,n+o)},function(t,n){return GT(t+e,n+o)},function(t,n){return XT(t+e,n+o)})),c,s);return XT(t.left,t.top)},function(n){var t=xT(i,n,a,l,c,s);return t.extra.each(function(t){n.onSensor(i,t)}),t.coord}),g=c,p=s,h=(d=f).bounds,v=pT(m,g,p),b=Ua(v.left,h.x,h.x+h.width-d.width),y=Ua(v.top,h.y,h.y+h.height-d.height),x=GT(b,y),mn(D,vT(m.fold(function(){var t=hT(x,g,p);return jT(t.left,t.top)},rt(x),function(){var t=gT(x,g,p);return XT(t.left,t.top)}),0,o))),n.onDrag(_,D,E)})}function TT(o,t,n,e){t.each(CT),n.snaps.each(function(t){var n,e;n=t,cn(e=o.element,n.leftAttr),cn(e,n.topAttr)});var r=n.getTarget(o.element);e.reset(),n.onDrop(o,r)}function ET(t){return function(n,e){function o(t){e.setStartData(OT(n,t))}return nu(H([Cr(Wi(),function(t){e.getStartData().each(function(){return o(t)})})],t(n,e,o),!0))}}function DT(a,c,s){return[Cr(di(),function(n,t){var e,o,r,i,u;0===t.event.raw.button&&(t.stop(),r={drop:e=function(){return TT(n,vt.some(i),a,c)},delayDrop:(o=jb(e,200)).schedule,forceDrop:e,move:function(t){o.cancel(),_T(n,a,c,ZT,t)}},i=kT(n,a.blockerClass,(u=r,nu([Cr(di(),u.forceDrop),Cr(pi(),u.drop),Cr(mi(),function(t,n){u.move(n.event)}),Cr(gi(),u.delayDrop)]))),s(n),ST(n,i))})]}function BT(a,c,s){function l(t){TT(t,f.get(),a,c),f.clear()}var f=dc();return[Cr(ci(),function(n,t){function e(){return l(n)}t.stop();var o,r,i,u=kT(n,a.blockerClass,(r=o=e,i=function(t){_T(n,a,c,nE,t)},nu([Cr(ci(),r),Cr(li(),o),Cr(fi(),o),Cr(si(),function(t,n){i(n.event)})])));f.set(u),s(n),ST(n,u)}),Cr(si(),function(t,n){n.stop(),_T(t,a,c,nE,n.event)}),Cr(li(),function(t,n){n.stop(),l(t)}),Cr(fi(),l)]}function MT(t,r,i,u,n,e){return t.fold(function(){return rE.snap({sensor:GT(i-20,u-20),range:Se(n,e),output:GT(vt.some(i),vt.some(u)),extra:{td:r}})},function(t){var n=i-20,e=u-20,o=t.element.dom.getBoundingClientRect();return rE.snap({sensor:GT(n,e),range:Se(40,40),output:GT(vt.some(i-o.width/2),vt.some(u-o.height/2)),extra:{td:r}})})}function AT(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,n){var e=n.td,o=i.get(),r=e;o.exists(function(t){return Lt(t,r)})||(i.set(e),u(e))},mustSnap:!0}}function FT(t){return Pm(sp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:tc([rE.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),mw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}function IT(a,e){function n(t){var n=zn(t);return MT(g.getOpt(e),t,n.x,n.y,n.width,n.height)}function o(t){var n=zn(t);return MT(p.getOpt(e),t,n.right,n.bottom,n.width,n.height)}function r(t,i,n,u){var e=n(i);rE.snapTo(t,e),function(t){var n=i.dom.getBoundingClientRect();vn(t.element,"display");var e=Xt(At.fromDom(a.getBody())).dom.innerHeight,o=n[u]<0,r=n[u]>e;(o||r)&&fn(t.element,"display","none")}(t)}function i(t){return r(h,t,n,"top")}function u(t){return r(v,t,o,"bottom")}var c=Vo([]),s=Vo([]),t=Vo(!1),l=dc(),f=dc(),d=AT(function(){return M(c.get(),n)},l,function(n){f.get().each(function(t){a.fire("TableSelectorChange",{start:n,finish:t})})}),m=AT(function(){return M(s.get(),o)},f,function(n){l.get().each(function(t){a.fire("TableSelectorChange",{start:t,finish:n})})}),g=FT(d),p=FT(m),h=_u(g.asSpec()),v=_u(p.asSpec());se().deviceType.isTouch()&&(a.on("TableSelectionChange",function(n){t.get()||(Is(e,h),Is(e,v),t.set(!0)),l.set(n.start),f.set(n.finish),n.otherCells.each(function(t){c.set(t.upOrLeftCells),s.set(t.downOrRightCells),i(n.start),u(n.finish)})}),a.on("ResizeEditor ResizeWindow ScrollContent",function(){l.get().each(i),f.get().each(u)}),a.on("TableSelectionClear",function(){t.get()&&(Vs(h),Vs(v),t.set(!1)),l.clear(),f.clear()}))}var RT,VT,PT={name:"lineheight",text:"Line height",icon:"line-height",getOptions:function(t){return t.getParam("lineheight_formats","1 1.1 1.2 1.3 1.4 1.5 2","string").split(" ")},hash:function(t){return r=["fixed","relative","empty"],vt.from(G_.exec(t)).bind(function(t){var n=Number(t[1]),e=t[2],o=e;return T(r,function(t){return T(j_[t],function(t){return o===t})})?vt.some({value:n,unit:e}):vt.none()}).map(function(t){return t.value+t.unit}).getOr(t);var r},display:h,watcher:function(t,n,e){return t.formatter.formatChanged("lineheight",e,!1,{value:n}).unbind},getCurrent:function(t){return vt.from(t.queryCommandValue("LineHeight"))},setCurrent:function(t,n){return t.execCommand("LineHeight",!1,n)}},HT=function(t){return{type:"selection",root:At.fromDom(t.selection.getNode())}},zT={onLtr:function(){return[Ja,Ga,Xa,Ya,qa,Ka,up,ap,Bm,Em,Dm,Tm]},onRtl:function(){return[Ja,Xa,Ga,qa,Ya,Ka,up,ap,Dm,Tm,Bm,Em]}},NT={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},LT={type:"separator"},WT=function(n){function t(t){return{text:t.text,icon:t.icon,disabled:t.disabled,shortcut:t.shortcut}}var e;if(y(n))return n;switch(n.type){case"separator":return LT;case"submenu":return lt(lt({type:"nestedmenuitem"},t(n)),{getSubmenuItems:function(){var t=n.getSubmenuItems();return y(t)?t:M(t,WT)}});default:return lt(lt({type:"menuitem"},t(n)),{onAction:(e=n.onAction,function(){return e()})})}},UT=Po([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),jT=UT.offset,GT=UT.absolute,XT=UT.fixed,YT=function(t,n,l,f,d){var e=n.getSnapPoints(t);return wT(e,l,f,d).orThunk(function(){return R(e,function(n,e){var t,o,r,i,u,a,c,s=(t=e.sensor,e.range.left,e.range.top,i=pT(l,o=f,r=d),u=pT(t,o,r),a=Math.abs(i.left-u.left),c=Math.abs(i.top-u.top),Se(a,c));return n.deltas.fold(function(){return{deltas:vt.some(s),snap:vt.some(e)}},function(t){return(s.left+s.top)/2<=(t.left+t.top)/2?{deltas:vt.some(s),snap:vt.some(e)}:n})},{deltas:vt.none(),snap:vt.none()}).snap.map(function(t){return{output:bT(t.output,l,f,d),extra:t.extra}})})},qT=function(t,n,e,o,r){return wT(n.getSnapPoints(t),e,o,r)},KT=Object.freeze({__proto__:null,snapTo:function(t,n,e,o){var r,i,u,a=n.getTarget(t.element);n.repositionTarget&&(r=In(Ut(t.element)),i=zC(a),mn(a,vT({coord:bT((u=o).output,u.output,r,i),extra:u.extra}.coord,0,i)))}}),JT="data-initial-z-index",$T=_o("snaps",[fo("getSnapPoints"),Ku("onSensor"),fo("leftAttr"),fo("topAttr"),To("lazyViewport",Ae),To("mustSnap",!1)]),QT=[To("useFixed",O),fo("blockerClass"),To("getTarget",h),To("onDrag",st),To("repositionTarget",!0),To("onDrop",st),Fo("getBounds",Ae),$T],ZT=Object.freeze({__proto__:null,getData:function(t){return vt.from(Se(t.x,t.y))},getDelta:function(t,n){return Se(n.left-t.left,n.top-t.top)}}),tE=H(H([],QT,!0),[Zu("dragger",{handlers:ET(DT)})],!1),nE=Object.freeze({__proto__:null,getData:function(t){var n,e=t.raw.touches;return 1===e.length?(n=e[0],vt.some(Se(n.clientX,n.clientY))):vt.none()},getDelta:function(t,n){return Se(n.left-t.left,n.top-t.top)}}),eE=H(H([],QT,!0),[Zu("dragger",{handlers:ET(BT)})],!1),oE=H(H([],QT,!0),[Zu("dragger",{handlers:ET(function(t,n,e){return H(H([],DT(t,n,e),!0),BT(t,n,e),!0)})})],!1),rE=xa({branchKey:"mode",branches:Object.freeze({__proto__:null,mouse:tE,touch:eE,mouseOrTouch:oE}),name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:vt.from(t.extra)}}},state:Object.freeze({__proto__:null,init:function(){var i=vt.none(),n=vt.none(),t=rt({});return xu({readState:t,reset:function(){i=vt.none(),n=vt.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=vt.some(e),o;var n,e,o})},getStartData:function(){return n},setStartData:function(t){n=vt.some(t)}})}}),apis:KT});function iE(t,n,e){var o,r,i,u,a,c,s=At.fromDom(t.getContainer());J((o=t,r=n,i=e,u=xn(s),a=kn(s),(c={}).height=$O(u+r.top,lh(o),dh(o)),i===RT.Both&&(c.width=$O(a+r.left,sh(o),fh(o))),c),function(t,n){return fn(s,n,JO(t)),0}),t.fire("ResizeEditor")}function uE(t,n,e,o){return iE(t,Se(20*e,20*o),n),vt.some(!0)}function aE(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(v=[],t.getParam("elementpath",!0,"boolean")&&v.push((g=t,h=n,(p={}).delimiter||(p.delimiter="\xbb"),{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:tc([Mg.config({mode:"flow",selector:"div[role=button]"}),dd.config({disabled:h.isDisabled}),mv(),fy.config({}),Fg.config({}),fm("elementPathEvents",[eu(function(r,t){g.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return Mg.focusIn(r)}),g.on("NodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return 1;if("bookmark"===t.getAttribute("data-mce-type"))return 1}}(o)){var r=g.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length?Fg.set(r,(n=M(e||[],function(n,t){return sp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){g.focus(),g.selection.select(n.element),g.nodeChanged()},buttonBehaviours:tc([Kv(h.isDisabled),mv()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+p.delimiter+" "}},R(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]]))):Fg.set(r,[])})})])]),components:[]})),t.hasPlugin("wordcount")&&v.push((f=t,d=n,sp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:tc([Kv(d.isDisabled),mv(),fy.config({}),Fg.config({}),Tf.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),fm("wordcount-events",[iu(function(t){var n=Tf.getValue(t),e="words"===n.mode?"characters":"words";Tf.setValue(t,{mode:e,count:n.count}),b(t,n.count,e)}),eu(function(e){f.on("wordCountUpdate",function(t){var n=Tf.getValue(e).mode;Tf.setValue(e,{mode:n,count:t.wordCount}),b(e,t.wordCount,n)})})])]),eventOrder:((m={})[Fi()]=["disabling","alloy.base.behaviour","wordcount-events"],m)}))),t.getParam("branding",!0,"boolean")&&v.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(l=lp.translate(["Powered by {0}","Tiny"]))+'">'+l+"</a>"}}),e=0<v.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:v}]:[],i=n,a=!(u=r=t).hasPlugin("autoresize"),o=(s=!1===(c=u.getParam("resize",a))?RT.None:"both"===c?RT.Both:RT.Vertical)===RT.None?vt.none():vt.some(jm("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:i.translate("Resize")},behaviours:[rE.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){return iE(r,e,s)},blockerClass:"tox-blocker"}),Mg.config({mode:"special",onLeft:function(){return uE(r,s,-1,0)},onRight:function(){return uE(r,s,1,0)},onUp:function(){return uE(r,s,0,-1)},onDown:function(){return uE(r,s,0,1)}}),fy.config({}),Vg.config({})]},i.icons)),e.concat(o.toArray()))};function b(t,n,e){return Fg.set(t,[oi(d.translate(["{0} "+e,n[e]]))])}}function cE(d){function m(){return i.bind(BO.getHeader)}function g(){return Re.value(y)}function p(){return i.bind(function(t){return BO.getThrobber(t)}).getOrDie("Could not find throbber element")}var t,n,e,o,r=d.inline,h=r?I_:F_,v=lv(d)?G1:R1,i=vt.none(),u=se(),a=u.browser.isIE()?["tox-platform-ie"]:[],c=u.deviceType.isTouch()?["tox-platform-touch"]:[],s=iv(d),l=cv(d),f=lp.isRtl()?{attributes:{dir:"rtl"}}:{},b={attributes:((t={})[ts]=s?Cc.BottomToTop:Cc.TopToBottom,t)},y=_u((n=Lt(ve(),l)&&"grid"===gn(l,"display"),e={dom:lt({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(a).concat(c)},f),behaviours:tc([sf.config({useFixed:function(){return v.isDocked(m)}})])},o={dom:{styles:{width:document.body.clientWidth+"px"}},events:nu([Cr(Ui(),function(){fn(J.element,"width",document.body.clientWidth+"px")})])},Xo(e,n?o:{}))),x=Pm({dom:{tag:"div",classes:["tox-anchorbar"]}}),w=PC(y,d,function(){return i.bind(function(t){return x.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),S=BO.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:w,onEscape:function(){d.focus()}}),C=ov(d),k=BO.parts.toolbar(lt({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:w.shared.providers,onEscape:function(){d.focus()},type:C,lazyToolbar:function(){return i.bind(function(t){return BO.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return m().getOrDie("Could not find header element")}},b)),O=BO.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:w.shared.providers,onEscape:function(){d.focus()},type:C}),_=BO.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),T=BO.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),E=BO.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:w}),D=d.getParam("statusbar",!0,"boolean")&&!r?vt.some(aE(d,w.shared.providers)):vt.none(),B={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[_,T]},M=ev(d),A=gh(d),F=mh(d),I=BO.parts.header({dom:lt({tag:"div",classes:["tox-editor-header"]},b),components:ft([F?[S]:[],M?[O]:A?[k]:[],av(d)?[]:[x.asSpec()]]),sticky:lv(d),editor:d,sharedBackstage:w.shared}),R=ft([s?[]:[I],r?[]:[B],s?[I]:[]]),V=ft([[{dom:{tag:"div",classes:["tox-editor-container"]},components:R}],r?[]:D.toArray(),[E]]),P=sv(d),H=lt(lt({role:"application"},lp.isRtl()?{dir:"rtl"}:{}),P?{"aria-hidden":"true"}:{}),z=_u(BO.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(r?["tox-tinymce-inline"]:[]).concat(s?["tox-tinymce--toolbar-bottom"]:[]).concat(c).concat(a),styles:lt({visibility:"hidden"},P?{opacity:"0",border:"0"}:{}),attributes:H},components:V,behaviours:tc([mv(),dd.config({disableClass:"tox-tinymce--disabled"}),Mg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),i=vt.some(z);d.shortcuts.add("alt+F9","focus menubar",function(){BO.focusMenubar(z)}),d.shortcuts.add("alt+F10","focus toolbar",function(){BO.focusToolbar(z)}),d.addCommand("ToggleToolbarDrawer",function(){BO.toggleToolbarDrawer(z)}),d.addQueryStateHandler("ToggleToolbarDrawer",function(){return BO.isToolbarDrawerToggled(z)});var N,L,W,U,j,G,X,Y,q,K=$b(z),J=$b(y);function $(){var t,n,e,o,r,i,u=JO((o=ah(e=n=d),r=lh(e),i=dh(e),KO(o).map(function(t){return $O(t,r,i)}).getOr(ah(n)))),a=JO(QO(t=d).getOr(ch(t)));return d.inline||(hn("div","width",a)&&fn(z.element,"width",a),hn("div","height",u)?fn(z.element,"height",u):fn(z.element,"height","200px")),u}return N=d,L=K,W=J,U=wa(),j=mc(U,"touchstart",tt),G=mc(U,"touchmove",function(t){return Q(Ni(),t)}),X=mc(U,"touchend",function(t){return Q(Li(),t)}),Y=mc(U,"mousedown",tt),q=mc(U,"mouseup",function(t){0===t.raw.button&&Z(yf(),{target:t.target})}),N.on("PostRender",function(){N.on("click",nt),N.on("tap",nt),N.on("mouseup",et),N.on("mousedown",ot),N.on("ScrollWindow",rt),N.on("ResizeWindow",it),N.on("ResizeEditor",ut),N.on("AfterProgressState",at),N.on("DismissPopups",ct)}),N.on("remove",function(){N.off("click",nt),N.off("tap",nt),N.off("mouseup",et),N.off("mousedown",ot),N.off("ScrollWindow",rt),N.off("ResizeWindow",it),N.off("ResizeEditor",ut),N.off("AfterProgressState",at),N.off("DismissPopups",ct),Y.unbind(),j.unbind(),G.unbind(),X.unbind(),q.unbind()}),N.on("detach",function(){zs(L),zs(W),L.destroy(),W.destroy()}),{mothership:K,uiMothership:J,backstage:w,renderUI:function(){var o,r;v.setup(d,w.shared,m),Q_(d,w),lT(d,g,w),r=(o=d).ui.registry.getAll().sidebars,St(kt(r),function(n){function e(){return mt(vt.from(o.queryCommandValue("ToggleSidebar")),n)}var t=r[n];o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){function n(){return t.setActive(e())}return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})}),yk(d,p,w.shared),dt(d.getParam("toolbar_groups",{},"object"),function(t,n){d.ui.registry.addGroupToolbarButton(n,t)});var t,n=d.ui.registry.getAll(),e=n.buttons,i=n.menuItems,u=n.contextToolbars,a=n.sidebars,c=ph(d),s={menuItems:i,menus:(t=d.getParam("menu"))?dt(t,function(t){return lt(lt({},t),{items:t.items})}):{},menubar:d.getParam("menubar"),toolbar:c.getOrThunk(function(){return d.getParam("toolbar",!0)}),allowToolbarGroups:C===hh.floating,buttons:e,sidebar:a};l_(d,u,y,{backstage:w}),IT(d,y);var l=d.getElement(),f=$();return h.render(d,{mothership:K,uiMothership:J,outerContainer:z},s,w,{targetNode:l,height:f})},getUi:function(){return{channels:{broadcastAll:J.broadcast,broadcastOn:J.broadcastOn,register:st}}}};function Q(n,e){St([L,W],function(t){t.broadcastEvent(n,e)})}function Z(n,e){St([L,W],function(t){t.broadcastOn([n],e)})}function tt(t){return Z(vf(),{target:t.target})}function nt(t){return Z(vf(),{target:At.fromDom(t.target)})}function et(t){0===t.button&&Z(yf(),{target:At.fromDom(t.target)})}function ot(){St(N.editorManager.get(),function(t){N!==t&&t.fire("DismissPopups",{relatedTarget:N})})}function rt(t){return Q(Wi(),Bc(t))}function it(t){Z(bf(),{}),Q(Ui(),Bc(t))}function ut(){return Z(bf(),{})}function at(t){t.state&&Z(vf(),{target:At.fromDom(N.getContainer())})}function ct(t){Z(vf(),{target:At.fromDom(t.relatedTarget.getContainer())})}}function sE(n){return ur("items","items",We(),to(ro(function(t){return uo("Checking item of "+n,QD,t).fold(function(t){return Re.error(ir(t))},function(t){return Re.value(t)})})))}function lE(t){return y(t.type)&&y(t.name)}function fE(t){return{internalDialog:ao(uo("dialog",uB,t)),dataValidator:(n=z(F(sB(t),lE),function(n){return vt.from(lB[n.type]).fold(function(){return[]},function(t){return[mo(n.name,t)]})}),Jo(n)),initialData:t.initialData};var n}function dE(t){var e=[],o={};return J(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?Re.error(e):Re.value(o)}function mE(t,n){fn(t,"height",n+"px"),se().browser.isIE()?vn(t,"flex-basis"):fn(t,"flex-basis",n+"px")}function gE(t,d,n){Au(t,'[role="dialog"]').each(function(f){Fu(f,'[role="tablist"]').each(function(l){n.get().map(function(t){return fn(d,"height","0"),fn(d,"flex-basis","0"),Math.min(t,(e=d,o=l,r=Gt(n=f).dom,i="fixed"===gn(Au(n,".tox-dialog-wrap").getOr(n),"position")?Math.max(r.clientHeight,window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight),u=xn(e),a=e.dom.offsetLeft>=o.dom.offsetLeft+kn(o)?Math.max(xn(o),u):u,c=parseInt(gn(n,"margin-top"),10)||0,s=parseInt(gn(n,"margin-bottom"),10)||0,i-(xn(n)+c+s-a)));var n,e,o,r,i,u,a,c,s}).each(function(t){mE(d,t)})})})}function pE(t){return Fu(t,'[role="tabpanel"]')}function hE(t,e){function o(t){var n=dE(Tf.getValue(t)).getOr({}),e=i.get(),o=Xo(e,n);i.set(o)}function r(t){var n=i.get();Tf.setValue(t,n)}var u,a,i=Vo({}),c=Vo(null),n=M(t.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(t.title)},view:function(){return[Qw.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:M(t.items,function(t){return mC(n,t,e)}),formBehaviours:tc([Mg.config({mode:"acyclic",useTabstopAt:C(f0)}),fm("TabView.form.events",[eu(r),ou(o)]),rc.config({channels:sr([{key:xB,value:{onReceive:o}},{key:wB,value:{onReceive:r}}])})])}})]}}}),s=(u=n,a=dc(),{extraEvents:[eu(function(t){var o=t.element;pE(o).each(function(n){var e;fn(n,"visibility","hidden"),t.getSystem().getByDom(n).toOptional().each(function(t){var o,r,i;Y(G((r=n,i=t,M(o=u,function(t,n){Fg.set(i,o[n].view());var e=r.dom.getBoundingClientRect();return Fg.set(i,[]),e.height})),function(t,n){return n<t?-1:t<n?1:0})).fold(a.clear,a.set)}),gE(o,n,a),vn(n,"visibility"),e=t,Y(u).each(function(t){return yB.showTab(e,t.value)}),cp.requestAnimationFrame(function(){gE(o,n,a)})})}),Cr(Ui(),function(t){var n=t.element;pE(n).each(function(t){gE(n,t,a)})}),Cr(Cy,function(t,n){var r=t.element;pE(r).each(function(n){var t=ka(me(n));fn(n,"visibility","hidden");var e=pn(n,"height").map(function(t){return parseInt(t,10)});vn(n,"height"),vn(n,"flex-basis");var o=n.dom.getBoundingClientRect().height;e.forall(function(t){return t<o})?(a.set(o),gE(r,n,a)):e.each(function(t){mE(n,t)}),vn(n,"visibility"),t.each(Sa)})})],selectFirst:!1});return yB.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=Tf.getValue(n);vr(t,Sy,{name:o,oldName:c.get()}),c.set(o)},tabs:n,components:[yB.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[pB.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:tc([fy.config({})])}),yB.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:s.selectFirst,tabSectionBehaviours:tc([fm("tabpanel",s.extraEvents),Mg.config({mode:"acyclic"}),ud.config({find:function(t){return Y(yB.getViewItems(t))}}),Tf.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([xB],{}),i.get()},setValue:function(t,n){i.set(n),t.getSystem().broadcastOn([wB],{})}}})])})}function vE(t,n,r,e){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:lt(lt({},n.map(function(t){return{id:t}}).getOr({})),e?{"aria-live":"polite"}:{})},components:[],behaviours:tc([rS(0),x_.config({channel:kB,updateState:function(t,n){return vt.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){return"tabpanel"!==t.body.type?[(e=t.body,o=r,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(n=Pm(Qw.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:M(e.items,function(t){return mC(n,t,o)})}}))).asSpec()]}],behaviours:tc([Mg.config({mode:"acyclic",useTabstopAt:C(f0)}),oS(n),sS(n,{postprocess:function(t){return dE(t).fold(function(t){return console.error(t),{}},h)}})])})]:[hE(t.body,r)];var e,o,n},initialData:t})])}}function bE(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}}function yE(t,n){return YE.parts.close(sp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:tc([fy.config({})])}))}function xE(){return YE.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function wE(t,n){return YE.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:vk("<p>"+n.translate(t)+"</p>")}]}]})}function SE(t){return YE.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})}function CE(t,n){return[uy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),uy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]}function kE(n){var t,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return YE.sketch({lazySink:n.lazySink,onEscape:function(t){return n.onEscape(t),vt.some(!0)},useTabstopAt:function(t){return!f0(t)},dom:{tag:"div",classes:[e].concat(n.extraClasses),styles:lt({position:"relative"},n.extraStyles)},components:H([n.header,n.body],n.footer.toArray(),!0),parts:{blocker:{dom:vk('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:TB?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:tc(H([Vg.config({}),fm("dialog-events",n.dialogEvents.concat([Dr(vi(),function(t,n){Mg.focusIn(t)})])),fm("scroll-lock",[eu(function(){Xr(ve(),i)}),ou(function(){Yr(ve(),i)})])],n.extraBehaviours,!0)),eventOrder:lt(((t={})[Fi()]=["dialog-events"],t[ji()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Gi()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),n.eventOrder)})}function OE(t){return sp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[jm("close",{tag:"div",classes:["tox-icon"]},t.icons)],action:function(t){hr(t,vy)}})}function _E(t,n,e){function o(t){return[oi(e.translate(t.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:lt({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:tc([x_.config({channel:CB,renderComponents:o})])}}function TE(){return{dom:vk('<div class="tox-dialog__draghandle"></div>')}}function EE(t,n){return e={title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},o=n.shared.providers,r=YE.parts.title(_E(e,vt.none(),o)),i=YE.parts.draghandle(TE()),u=YE.parts.close(OE(o)),a=[r].concat(e.draggable?[i]:[]).concat([u]),uy.sketch({dom:vk('<div class="tox-dialog__header"></div>'),components:a});var e,o,r,i,u,a}function DE(t,n,e){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.translate(t)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:vk('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}}function BE(t,o,n){return{onClose:function(){return n.closeWindow()},onBlock:function(e){YE.setBusy(t(),function(t,n){return DE(e.message,n,o)})},onUnblock:function(){YE.setIdle(t())}}}function ME(t,n,e,o){var r;return _u(kE(lt(lt({},t),{lazySink:o.shared.getSink,extraBehaviours:H([x_.config({channel:SB,updateState:function(t,n){return vt.some(n)},initialData:n}),fS({})],t.extraBehaviours,!0),onEscape:function(t){hr(t,vy)},dialogEvents:e,eventOrder:((r={})[Ai()]=[x_.name(),rc.name()],r[ji()]=["scroll-lock",x_.name(),"messages","dialog-events","alloy.base.behaviour"],r[Gi()]=["alloy.base.behaviour","dialog-events","messages",x_.name(),"scroll-lock"],r)})))}function AE(t){return M(t,function(t){return"menu"===t.type?(e=M((n=t).items,function(t){var n=Vo(!1);return lt(lt({},t),{storage:n})}),lt(lt({},n),{items:e})):t;var n,e})}function FE(t){return R(t,function(t,n){return"menu"!==n.type?t:R(n.items,function(t,n){return t[n.name]=n.storage,t},t)},{})}function IE(t,e){return[Tr(vi(),l0),t(hy,function(t,n){e.onClose(),n.onClose()}),t(vy,function(t,n,e,o){n.onCancel(t),hr(o,hy)}),Cr(wy,function(t,n){return e.onUnblock()}),Cr(xy,function(t,n){return e.onBlock(n.event)})]}function RE(t,n){function e(t,n){return uy.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:M(n,function(t){return t.memento.asSpec()})})}var o=A(n.map(function(t){return t.footerButtons}).getOr([]),function(t){return"start"===t.align});return[e("start",o.pass),e("end",o.fail)]}function VE(t,e){return{dom:vk('<div class="tox-dialog__footer"></div>'),components:[],behaviours:tc([x_.config({channel:OB,initialData:t,updateState:function(t,n){var r=M(n.buttons,function(t){var n=Pm(q0(t,t.type,e));return{name:t.name,align:t.align,memento:n}});return vt.some({lookupByName:function(t,n){return e=t,o=n,V(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:RE})])}}function PE(t,n){return YE.parts.footer(VE(t,n))}function HE(n,e){if(n.getRoot().getSystem().isConnected()){var o=ud.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return Qw.getField(o,e).fold(function(){var t=n.getFooter();return x_.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return vt.some(t)})}return vt.none()}function zE(c,o,s){function t(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)}var l={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t,e=Tf.getValue(n),o=dt(s,function(t){return t.get()});return lt(lt({},e),o)},setData:function(a){t(function(t){var n,e,o=l.getData(),r=lt(lt({},o),a),i=(n=r,e=c.getRoot(),x_.getState(e).get().map(function(t){return ao(uo("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();Tf.setValue(u,i),J(s,function(t,n){Tt(r,n)&&t.set(r[n])})})},disable:function(t){HE(c,t).each(dd.disable)},enable:function(t){HE(c,t).each(dd.enable)},focus:function(t){HE(c,t).each(Vg.focus)},block:function(n){if(!y(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){vr(t,xy,{message:n})})},unblock:function(){t(function(t){hr(t,wy)})},showTab:function(e){t(function(t){var n=c.getBody();x_.getState(n).get().exists(function(t){return t.isTabPanel()})&&ud.getCurrent(n).each(function(t){yB.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([SB],n),t.getSystem().broadcastOn([CB],n.internalDialog),t.getSystem().broadcastOn([kB],n.internalDialog),t.getSystem().broadcastOn([OB],n.internalDialog),l.setData(n.initialData)})},close:function(){t(function(t){hr(t,hy)})}};return l}function NE(t){return x(t)&&-1!==AB.indexOf(t.mceAction)}function LE(o,t,r,n){var e,i,u,a=EE(o.title,n),c=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[c0({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:tc([fy.config({}),Vg.config({})])})]}],behaviours:tc([Mg.config({mode:"acyclic",useTabstopAt:C(f0)})])},YE.parts.body(i)),s=o.buttons.bind(function(t){return 0===t.length?vt.none():vt.some(PE({buttons:t},n))}),l=EB(function(){return v},BE(function(){return h},n.shared.providers,t)),f=lt(lt({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),d=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],m=new MB(o.url,{base_uri:new MB(window.location.href)}),g=m.protocol+"://"+m.host+(m.port?":"+m.port:""),p=fc(),h=ME({header:a,body:c,footer:s,extraClasses:d,extraBehaviours:[fm("messages",[eu(function(){var t=mc(At.fromDom(window),"message",function(t){var n,e;m.isSameOrigin(new MB(t.raw.origin))&&(NE(n=t.raw.data)?function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!w(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock()}}(r,v,n):!NE(e=n)&&x(e)&&Tt(e,"mceAction")&&o.onMessage(v,n))});p.set(t)}),ou(p.clear)]),rc.config({channels:((e={})[_B]={onReceive:function(t,n){Fu(t.element,"iframe").each(function(t){t.dom.contentWindow.postMessage(n,g)})}},e)})],extraStyles:f},o,l,n),v={block:function(n){if(!y(n))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");b(function(t){vr(t,xy,{message:n})})},unblock:function(){b(function(t){hr(t,wy)})},close:function(){b(function(t){hr(t,hy)})},sendMessage:function(n){b(function(t){t.getSystem().broadcastOn([_B],n)})}};function b(t){u.getSystem().isConnected()&&t(u)}return{dialog:u=h,instanceApi:v}}function WE(t){function o(t,y){return fB.open(function(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p,h,v=n,b=(r={redial:fB.redial,closeWindow:function(){YE.hide(b.dialog),y(b.instanceApi)}},i=R,c=EE((o={dataValidator:e,initialData:v,internalDialog:t}).internalDialog.title,i),u=i,a=vE({body:o.internalDialog.body},vt.none(),u,!1),s=YE.parts.body(a),l=AE(o.internalDialog.buttons),f=FE(l),d=PE({buttons:l},i),m=DB(function(){return h},BE(function(){return p},i.shared.providers,r),i.shared.getSink),g=function(){switch(o.internalDialog.size){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}}(),p=ME({header:c,body:s,footer:vt.some(d),extraClasses:g,extraBehaviours:[],extraStyles:{}},o,m,i),h=zE({getRoot:rt(p),getBody:function(){return YE.getBody(p)},getFooter:function(){return YE.getFooter(p)},getFormWrapper:function(){var t=YE.getBody(p);return ud.getCurrent(t).getOr(t)}},r.redial,f),{dialog:p,instanceApi:h});return YE.show(b.dialog),b.instanceApi.setData(v),b.instanceApi},t)}function r(t,A,F,I){return fB.open(function(t,n,e){function o(){return E.on(function(t){rp.reposition(t),N1.refresh(t)})}var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,C,k,O,_,T=ao(uo("data",e,n)),E=dc(),D=R.shared.header.isPositionedAtTop(),B=(i={dataValidator:e,initialData:T,internalDialog:t},u={redial:fB.redial,closeWindow:function(){E.on(rp.hide),V.off("ResizeEditor",o),E.clear(),F(B.instanceApi)}},a=R,c=I,v=Fr("dialog-label"),b=Fr("dialog-content"),y=Pm((f={title:i.internalDialog.title,draggable:!0},d=v,m=a.shared.providers,uy.sketch({dom:vk('<div class="tox-dialog__header"></div>'),components:[_E(f,vt.some(d),m),TE(),OE(m)],containerBehaviours:tc([rE.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Iu(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),x=Pm((g={body:i.internalDialog.body},p=a,h=c,vE(g,vt.some(b),p,h))),w=AE(i.internalDialog.buttons),S=FE(w),C=Pm(BB({buttons:w},a)),k=DB(function(){return _},{onBlock:function(e){cO.block(O,function(t,n){return DE(e.message,n,a.shared.providers)})},onUnblock:function(){cO.unblock(O)},onClose:function(){return u.closeWindow()}},a.shared.getSink),O=_u({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((s={role:"dialog"})["aria-labelledby"]=v,s["aria-describedby"]=b,s)},eventOrder:((l={})[Ai()]=[x_.name(),rc.name()],l[Fi()]=["execute-on-form"],l[ji()]=["reflecting","execute-on-form"],l),behaviours:tc([Mg.config({mode:"cyclic",onEscape:function(t){return hr(t,hy),vt.some(!0)},useTabstopAt:function(t){return!f0(t)&&("button"!==Ft(t)||"disabled"!==rn(t,"disabled"))}}),x_.config({channel:SB,updateState:function(t,n){return vt.some(n)},initialData:i}),Vg.config({}),fm("execute-on-form",k.concat([Dr(vi(),function(t,n){Mg.focusIn(t)})])),cO.config({getRoot:function(){return vt.some(O)}}),Fg.config({}),fS({})]),components:[y.asSpec(),x.asSpec(),C.asSpec()]}),_=zE({getRoot:rt(O),getFooter:function(){return C.get(O)},getBody:function(){return x.get(O)},getFormWrapper:function(){var t=x.get(O);return ud.getCurrent(t).getOr(t)}},u.redial,S),{dialog:O,instanceApi:_}),M=_u(rp.sketch(lt(lt({lazySink:R.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},D?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:tc(H([fm("window-manager-inline-events",[Cr(Xi(),function(t,n){hr(B.dialog,vy)})])],(r=V,P&&D?[]:[N1.config({contextual:{lazyContext:function(){return vt.some(Me(At.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]),!0)),isExtraPart:function(t,n){return Ub(e=n,".tox-alert-dialog")||Ub(e,".tox-confirm-dialog");var e}})));return E.set(M),rp.showWithin(M,Tu(B.dialog),{anchor:A},vt.some(ve())),P&&D||(N1.refresh(M),V.on("ResizeEditor",o)),B.instanceApi.setData(T),Mg.focusIn(B.dialog),B.instanceApi},t)}var c,s,l,f,R=t.backstage,V=t.editor,P=lv(V),e=(s=(c=t).backstage.shared,{open:function(t,n){function e(){YE.hide(u),n()}var o=Pm(q0({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:vt.none()},"cancel",c.backstage)),r=xE(),i=yE(e,s.providers),u=_u(kE({lazySink:function(){return s.getSink()},header:bE(r,i),body:wE(t,s.providers),footer:vt.some(SE(CE([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Cr(vy,e)],eventOrder:{}}));YE.show(u);var a=o.get(u);Vg.focus(a)}}),i=(f=(l=t).backstage.shared,{open:function(t,n){function e(t){YE.hide(a),n(t)}var o=Pm(q0({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:vt.none()},"submit",l.backstage)),r=q0({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:vt.none()},"cancel",l.backstage),i=xE(),u=yE(function(){return e(!1)},f.providers),a=_u(kE({lazySink:function(){return f.getSink()},header:bE(i,u),body:wE(t,f.providers),footer:vt.some(SE(CE([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Cr(vy,function(){return e(!1)}),Cr(yy,function(){return e(!0)})],eventOrder:{}}));YE.show(a);var c=o.get(a);Vg.focus(c)}});return{open:function(t,n,e){return void 0!==n&&"toolbar"===n.inline?r(t,R.shared.anchors.inlineDialog(),e,n.ariaAttrs):void 0!==n&&"cursor"===n.inline?r(t,R.shared.anchors.cursor(),e,n.ariaAttrs):o(t,e)},openUrl:function(t,n){return e=n,fB.openUrl(function(t){var n=LE(t,{closeWindow:function(){YE.hide(n.dialog),e(n.instanceApi)}},V,R);return YE.show(n.dialog),n.instanceApi},t);var e},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){i.open(t,function(t){n(t)})}}}(VT=RT=RT||{})[VT.None=0]="None",VT[VT.Both=1]="Both",VT[VT.Vertical=2]="Vertical";var UE,jE=rt([fo("lazySink"),xo("dragBlockClass"),Fo("getBounds",Ae),To("useTabstopAt",_),To("eventOrder",{}),$s("modalBehaviours",[Mg]),Ju("onExecute"),Qu("onEscape")]),GE={sketch:h},XE=rt([Jf({name:"draghandle",overrides:function(t,n){return{behaviours:tc([rE.config({mode:"mouse",getTarget:function(t){return Au(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),qf({schema:[fo("dom")],name:"title"}),qf({factory:GE,schema:[fo("dom")],name:"close"}),qf({factory:GE,schema:[fo("dom")],name:"body"}),Jf({factory:GE,schema:[fo("dom")],name:"footer"}),Kf({factory:{sketch:function(t,n){return lt(lt({},t),{dom:n.dom,components:n.components})}},schema:[To("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),To("components",[])],name:"blocker"})]),YE=Sl({name:"ModalDialog",configFields:jE(),partFields:XE(),factory:function(a,t,n,r){var e,i=dc(),o=Fr("modal-events"),u=lt(lt({},a.eventOrder),((e={})[ji()]=[o].concat(a.eventOrder["alloy.system.attached"]||[]),e));return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(t){i.set(t);var n=a.lazySink(t).getOrDie(),e=r.blocker(),o=n.getSystem().build(lt(lt({},e),{components:e.components.concat([Tu(t)]),behaviours:tc([Vg.config({}),fm("dialog-blocker-events",[Dr(vi(),function(){Mg.focusIn(t)})])])}));Is(n,o),Mg.focusIn(t)},hide:function(n){i.clear(),Yt(n.element).each(function(t){n.getSystem().getByDom(t).each(function(t){Vs(t)})})},getBody:function(t){return dl(t,a,"body")},getFooter:function(t){return dl(t,a,"footer")},setIdle:function(t){cO.unblock(t)},setBusy:function(t,n){cO.block(t,n)}},eventOrder:u,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Zs(a.modalBehaviours,[Fg.config({}),Mg.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),cO.config({getRoot:i.get}),fm(o,[eu(function(t){var n,e,o,r=t.element,i=dl(t,a,"title").element,u=un(r,"id").fold(function(){var t=Fr("dialog-label");return on(i,"id",t),t},h);on(r,"aria-labelledby",u),n=t.element,e=dl(t,a,"body").element,o=vt.from(rn(n,"id")).fold(function(){var t=Fr("dialog-describe");return on(e,"id",t),t},h),on(n,"aria-describedby",o)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),qE=Jo([go("type"),go("name")].concat(zh)),KE=nr,JE=[ur("name","name",Ue(function(){return Fr("button-name")}),tr),Co("icon"),Mo("align","end",["start","end"]),Ao("primary",!1),Ao("disabled",!1)],$E=H(H([],JE,!0),[go("text")],!1),QE=H([po("type",["submit","cancel","custom"])],$E,!0),ZE=so("type",{submit:QE,cancel:QE,custom:QE,menu:H([po("type",["menu"]),Co("text"),Co("tooltip"),Co("icon"),yo("items",qE)],JE,!0)}),tD=[go("type"),go("text"),po("level",["info","warn","error","success"]),go("icon"),To("url","")],nD=Jo(tD),eD=[go("type"),go("text"),Ao("disabled",!1),Ao("primary",!1),ur("name","name",Ue(function(){return Fr("button-name")}),tr),Co("icon"),Ao("borderless",!1)],oD=Jo(eD),rD=[go("type"),go("name"),go("label"),Ao("disabled",!1)],iD=Jo(rD),uD=nr,aD=[go("type"),go("name")],cD=aD.concat([Co("label")]),sD=cD.concat([To("columns","auto")]),lD=Jo(sD),fD=$o([go("value"),go("text"),go("icon")]),dD=Jo(cD),mD=tr,gD=Jo(cD),pD=tr,hD=aD.concat([Bo("tag","textarea"),go("scriptId"),go("scriptUrl"),Eo("settings",void 0,rr)]),vD=aD.concat([Bo("tag","textarea"),ho("init")]),bD=ro(function(t){return uo("customeditor.old",Ze(vD),t).orThunk(function(){return uo("customeditor.new",Ze(hD),t)})}),yD=tr,xD=Jo(cD),wD=to(Ko),SD=[go("type"),go("html"),Mo("presets","presentation",["presentation","document"])],CD=Jo(SD),kD=cD.concat([Ao("sandboxed",!0)]),OD=Jo(kD),_D=tr,TD=cD.concat([mo("currentState",Jo([fo("blob"),go("url")]))]),ED=Jo(TD),DD=cD.concat([Co("inputMode"),Co("placeholder"),Ao("maximized",!1),Ao("disabled",!1)]),BD=Jo(DD),MD=tr,AD=[go("text"),go("value")],FD=[go("text"),yo("items",(UE=Rt(function(){return ID}),{extract:function(t,n){return UE().extract(t,n)},toString:function(){return UE().toString()}}))],ID=no([Jo(AD),Jo(FD)]),RD=cD.concat([yo("items",ID),Ao("disabled",!1)]),VD=Jo(RD),PD=tr,HD=cD.concat([bo("items",[go("text"),go("value")]),Do("size",1),Ao("disabled",!1)]),zD=Jo(HD),ND=tr,LD=cD.concat([Ao("constrain",!0),Ao("disabled",!1)]),WD=Jo(LD),UD=Jo([go("width"),go("height")]),jD=[go("type"),yo("header",tr),yo("cells",to(tr))],GD=Jo(jD),XD=cD.concat([Co("placeholder"),Ao("maximized",!1),Ao("disabled",!1)]),YD=Jo(XD),qD=tr,KD=cD.concat([Mo("filetype","file",["image","media","file"]),To("disabled",!1)]),JD=Jo(KD),$D=Jo([go("value"),To("meta",{})]),QD=Qe(function(){return oo("type",{alertbanner:nD,bar:Jo((e=sE("bar"),[go("type"),e])),button:oD,checkbox:iD,colorinput:dD,colorpicker:gD,dropzone:xD,grid:Jo((t=sE("grid"),[go("type"),mo("columns",Zo),t])),iframe:OD,input:BD,listbox:VD,selectbox:zD,sizeinput:WD,textarea:YD,urlinput:JD,customeditor:bD,htmlpanel:CD,imagetools:ED,collection:lD,label:Jo((n=sE("label"),[go("type"),go("label"),n])),table:GD,panel:tB});var t,n,e}),ZD=[go("type"),To("classes",[]),yo("items",QD)],tB=Jo(ZD),nB=[ur("name","name",Ue(function(){return Fr("tab-name")}),tr),go("title"),yo("items",QD)],eB=[go("type"),bo("tabs",nB)],oB=Jo(eB),rB=$E,iB=ZE,uB=Jo([go("title"),mo("body",oo("type",{panel:tB,tabpanel:oB})),Bo("size","normal"),yo("buttons",iB),To("initialData",{}),Fo("onAction",st),Fo("onChange",st),Fo("onSubmit",st),Fo("onClose",st),Fo("onCancel",st),To("onTabChange",st)]),aB=Jo(H([po("type",["cancel","custom"])],rB,!0)),cB=Jo([go("title"),go("url"),So("height"),So("width"),Oo("buttons",aB),Fo("onAction",st),Fo("onCancel",st),Fo("onClose",st),Fo("onMessage",st)]),sB=function(t){return x(t)?[t].concat(z(Z(t),sB)):c(t)?z(t,sB):[]},lB={checkbox:uD,colorinput:mD,colorpicker:pD,dropzone:wD,input:MD,iframe:_D,sizeinput:UD,selectbox:ND,listbox:PD,size:UD,textarea:qD,urlinput:$D,customeditor:yD,collection:fD,togglemenuitem:KE},fB={open:function(t,n){var e=fE(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){return t(ao(uo("dialog",cB,n)))},redial:fE},dB=wl({name:"TabButton",configFields:[To("uid",void 0),fo("value"),ur("dom","dom",Xe(function(){return{attributes:{role:"tab",id:Fr("aria"),"aria-selected":"false"}}}),Qo()),xo("action"),To("domModification",{}),$s("tabButtonBehaviours",[Vg,Mg,Tf]),fo("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:xm(t.action),behaviours:Zs(t.tabButtonBehaviours,[Vg.config({}),Mg.config({mode:"execution",useSpace:!0,useEnter:!0}),Tf.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),mB=rt([fo("tabs"),fo("dom"),To("clickToDismiss",!1),$s("tabbarBehaviours",[gd,Mg]),Yu(["tabClass","selectedClass"])]),gB=rt([$f({factory:dB,name:"tabs",unit:"tab",overrides:function(o){return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=gd.isHighlighted(n,t);(e&&o.clickToDismiss?function(t,n){gd.dehighlight(t,n),vr(t,$i(),{tabbar:t,button:n})}:e?st:function(t,n){gd.highlight(t,n),vr(t,Ji(),{tabbar:t,button:n})})(n,t)},domModification:{classes:[o.markers.tabClass]}}}})]),pB=Sl({name:"Tabbar",configFields:mB(),partFields:gB(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Zs(t.tabbarBehaviours,[gd.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){on(n.element,"aria-selected","true")},onDehighlight:function(t,n){on(n.element,"aria-selected","false")}}),Mg.config({mode:"flow",getInitial:function(t){return gd.getHighlighted(t).map(function(t){return t.element})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),hB=wl({name:"Tabview",configFields:[$s("tabviewBehaviours",[Fg])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:Zs(t.tabviewBehaviours,[Fg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),vB=rt([To("selectFirst",!0),Ku("onChangeTab"),Ku("onDismissTab"),To("tabs",[]),$s("tabSectionBehaviours",[])]),bB=rt([qf({factory:pB,schema:[fo("dom"),vo("markers",[fo("tabClass"),fo("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),qf({factory:hB,name:"tabview"})]),yB=Sl({name:"TabSection",configFields:vB(),partFields:bB(),factory:function(i,t,n,e){function o(t,n){fl(t,i,"tabbar").each(function(t){n(t).each(br)})}return{uid:i.uid,dom:i.dom,components:t,behaviours:Qs(i.tabSectionBehaviours),events:nu(ft([i.selectFirst?[eu(function(t,n){o(t,gd.getFirst)})]:[],[Cr(Ji(),function(t,n){var o=n.event.button,r=Tf.getValue(o);fl(o,i,"tabview").each(function(e){V(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();un(o.element,"id").each(function(t){on(e.element,"aria-labelledby",t)}),Fg.set(e,n),i.onChangeTab(e,o,n)})})}),Cr($i(),function(t,n){var e=n.event.button;i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return fl(t,i,"tabview").map(function(t){return Fg.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){return V(gd.getCandidates(n),function(t){return Tf.getValue(t)===e}).filter(function(t){return!gd.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),xB="send-data-to-section",wB="send-data-to-view",SB=Fr("update-dialog"),CB=Fr("update-title"),kB=Fr("update-body"),OB=Fr("update-footer"),_B=Fr("body-send-message"),TB=Zv.deviceType.isTouch(),EB=function(u,t){function n(t,i){return Cr(t,function(e,o){var n,r;n=e,r=function(t,n){i(u(),t,o.event,e)},x_.getState(n).get().each(function(t){r(t,n)})})}return H(H([],IE(n,t),!0),[n(by,function(t,n,e){n.onAction(t,{name:e.name})})],!1)},DB=function(u,t,c){function n(t,i){return Cr(t,function(e,o){var n,r;n=e,r=function(t,n){i(u(),t,o.event,e)},x_.getState(n).get().each(function(t){r(t.internalDialog,n)})})}return H(H([],IE(n,t),!0),[n(yy,function(t,n){return n.onSubmit(t)}),n(py,function(t,n,e){n.onChange(t,{name:e.name})}),n(by,function(t,n,e,o){function r(){return Mg.focusIn(o)}function i(t){return an(t,"disabled")||un(t,"aria-disabled").exists(function(t){return"true"===t})}var u=me(o.element),a=ka(u);n.onAction(t,{name:e.name,value:e.value}),ka(u).fold(r,function(n){i(n)||a.exists(function(t){return Wt(n,t)&&i(t)})?r():c().toOptional().filter(function(t){return!Wt(t.element,n)}).each(r)})}),n(Sy,function(t,n,e){n.onTabChange(t,{newTabName:e.name,oldTabName:e.oldName})}),ou(function(t){var n=u();Tf.setValue(t,n.getData())})],!1)},BB=VE,MB=tinymce.util.Tools.resolve("tinymce.util.URI"),AB=["insertContent","setContent","execCommand","close","block","unblock"];o.add("silver",function(t){var n=cE(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;return ey(t,o.shared),{renderUI:r,getWindowManagerImpl:rt(WE({editor:t,backstage:o})),getNotificationManagerImpl:function(){return u=t,r=e,l={backstage:o}.backstage.shared,{open:function(t,n){function e(){n(),rp.hide(c)}var o=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),a=_u(gp.sketch({text:t.text,level:wt(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:vt.from(t.icon),closeButton:!o,onAction:e,iconProvider:l.providers.icons,translationProvider:l.providers.translate})),c=_u(rp.sketch(lt({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:l.getSink,fireDismissalEventInstead:{}},l.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));function s(){var t=Me(At.fromDom(u.getContentAreaContainer())),n=Ae(),e=Ua(n.x,t.x,t.right),o=Ua(n.y,t.y,t.bottom),r=Math.max(t.right,n.right),i=Math.max(t.bottom,n.bottom);return vt.some(Be(e,o,r-e,i-o))}return r.add(c),0<t.timeout&&cp.setTimeout(function(){e()},t.timeout),{close:e,moveTo:function(t,n){rp.showAt(c,Tu(a),{anchor:{type:"makeshift",x:t,y:n}})},moveRel:function(t,n){var e,o,r,i=Tu(a),u={maxHeightFunction:Ic()};"banner"!==n&&d(t)?(e=function(){switch(n){case"bc-bc":return ap;case"tc-tc":return up;case"tc-bc":return Ka;default:return Ja}}(),o={type:"node",root:ve(),node:vt.some(At.fromDom(t)),overrides:u,layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}},rp.showWithinBounds(c,i,{anchor:o},s)):(r=lt(lt({},l.anchors.banner()),{overrides:u}),rp.showWithinBounds(c,i,{anchor:r},s))},text:function(t){gp.updateText(a,t)},settings:t,getEl:function(){return a.element.dom},progressBar:{value:function(t){gp.updateProgress(a,t)}}}},close:function(t){t.close()},reposition:function(e){0<e.length&&St(e,function(t,n){0===n?t.moveRel(null,"banner"):t.moveRel(e[n-1].getEl(),"bc-tc")})},getArgs:function(t){return t.settings}};var u,r,l},ui:i()}})}();