# 在线问卷系统 Docker 部署完整指南

## 项目概述
- **项目名称**: TDuck 在线问卷系统
- **架构**: 前后端分离
- **后端**: Spring Boot + Java 8 + MySQL
- **前端**: Vue.js + Element UI
- **部署方式**: Docker 容器化部署

## 端口配置优化
为避免端口冲突，使用以下端口配置：
- **MySQL数据库**: 13306 (避免与默认3306冲突)
- **后端API**: 18999 (避免与默认8999冲突)
- **前端Web**: 18888 (避免与默认8888冲突)
- **Nginx反向代理**: 18080

## 第一步：本地项目打包

### 1.1 后端项目打包
```bash
# 进入后端项目目录
cd tduck-platform

# 使用Maven打包
mvn clean package -DskipTests

# 打包完成后，jar文件位置：
# tduck-platform/tduck-api/target/tduck-api.jar
```

### 1.2 前端项目打包
```bash
# 进入前端项目目录
cd tduck-front

# 安装依赖
npm install

# 生产环境打包
npm run build

# 打包完成后，静态文件位置：
# tduck-front/dist/
```

### 1.3 创建部署包
```bash
# 在项目根目录创建部署包
mkdir wenjuan-deploy
cd wenjuan-deploy

# 复制必要文件
cp -r ../tduck-platform/tduck-api/target/tduck-api.jar ./
cp -r ../tduck-front/dist ./frontend
cp ../wenjuan.sql ./
cp -r ../images ./

# 创建配置文件目录
mkdir config
mkdir docker-configs
```

## 第二步：创建Docker配置文件

### 2.1 MySQL Dockerfile
```dockerfile
# docker-configs/mysql/Dockerfile
FROM mysql:8.0

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=wenjuan2024
ENV MYSQL_DATABASE=wenjuan
ENV MYSQL_USER=wenjuan
ENV MYSQL_PASSWORD=wenjuan2024

# 复制初始化脚本
COPY wenjuan.sql /docker-entrypoint-initdb.d/

# 复制自定义配置
COPY my.cnf /etc/mysql/conf.d/

EXPOSE 3306
```

### 2.2 MySQL配置文件
```ini
# docker-configs/mysql/my.cnf
[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
default-time-zone='+8:00'
max_connections=200
innodb_buffer_pool_size=256M
```

### 2.3 后端应用 Dockerfile
```dockerfile
# docker-configs/backend/Dockerfile
FROM openjdk:8-jre-alpine

# 设置工作目录
WORKDIR /app

# 复制jar文件
COPY tduck-api.jar app.jar

# 复制配置文件
COPY application-prod.yml /app/config/

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 暴露端口
EXPOSE 8999

# 启动命令
ENTRYPOINT ["java", "-jar", "-Dspring.profiles.active=prod", "/app/app.jar"]
```

### 2.4 前端 Dockerfile
```dockerfile
# docker-configs/frontend/Dockerfile
FROM nginx:alpine

# 复制静态文件
COPY dist/ /usr/share/nginx/html/

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 2.5 Nginx配置文件
```nginx
# docker-configs/frontend/nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    server {
        listen 80;
        server_name localhost;
        
        # 前端静态文件
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # API代理
        location /api/ {
            proxy_pass http://wenjuan-backend:8999/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## 第三步：生产环境配置文件

### 3.1 后端生产配置
```yaml
# config/application-prod.yml
spring:
  application:
    name: tduck-api
  profiles:
    active: prod
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************
    username: wenjuan
    password: wenjuan2024
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: WenjuanHikariCP
      max-lifetime: 1800000
      connection-test-query: SELECT 1

server:
  port: 8999
  compression:
    enabled: true
    mime-types: application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain

logging:
  level:
    com.tduck.cloud: info
    root: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

platform:
  request:
    trace-log: false
  jwt:
    secret: f6f31a5f2136758f86b67cde583cb125
    expire: 604800
    header: token

mybatis-plus:
  type-aliases-package: com.tduck.cloud.*.entity
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      id-type: AUTO
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: true
    cache-enabled: false
```

### 3.2 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # MySQL数据库
  wenjuan-mysql:
    build:
      context: ./docker-configs/mysql
      dockerfile: Dockerfile
    container_name: wenjuan-mysql
    restart: always
    ports:
      - "13306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./wenjuan.sql:/docker-entrypoint-initdb.d/init.sql:ro
    environment:
      MYSQL_ROOT_PASSWORD: wenjuan2024
      MYSQL_DATABASE: wenjuan
      MYSQL_USER: wenjuan
      MYSQL_PASSWORD: wenjuan2024
    networks:
      - wenjuan-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端应用
  wenjuan-backend:
    build:
      context: .
      dockerfile: ./docker-configs/backend/Dockerfile
    container_name: wenjuan-backend
    restart: always
    ports:
      - "18999:8999"
    depends_on:
      wenjuan-mysql:
        condition: service_healthy
    volumes:
      - ./config/application-prod.yml:/app/config/application-prod.yml:ro
      - ./images:/app/images:ro
      - backend_logs:/app/logs
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: "-Xms512m -Xmx1024m -XX:+UseG1GC"
    networks:
      - wenjuan-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8999/actuator/health"]
      timeout: 10s
      retries: 5

  # 前端应用
  wenjuan-frontend:
    build:
      context: .
      dockerfile: ./docker-configs/frontend/Dockerfile
    container_name: wenjuan-frontend
    restart: always
    ports:
      - "18888:80"
    depends_on:
      - wenjuan-backend
    volumes:
      - ./docker-configs/frontend/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - wenjuan-network

  # Nginx反向代理
  wenjuan-nginx:
    image: nginx:alpine
    container_name: wenjuan-nginx
    restart: always
    ports:
      - "18080:80"
    volumes:
      - ./docker-configs/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - wenjuan-frontend
      - wenjuan-backend
    networks:
      - wenjuan-network

volumes:
  mysql_data:
  backend_logs:

networks:
  wenjuan-network:
    driver: bridge
```

## 第四步：打包部署文件
```bash
# 创建最终部署包
tar -czf wenjuan-deploy.tar.gz wenjuan-deploy/

# 上传到服务器
scp wenjuan-deploy.tar.gz user@your-server:/home/<USER>/
```

## 第五步：Ubuntu服务器操作步骤

### 5.1 连接服务器并准备环境
```bash
# SSH连接到Ubuntu服务器
ssh user@your-server-ip

# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget unzip tar
```

### 5.2 安装Docker和Docker Compose
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使组权限生效
newgrp docker

# 验证Docker安装
docker --version
docker run hello-world

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证Docker Compose安装
docker-compose --version
```

### 5.3 解压和部署项目
```bash
# 创建项目目录
mkdir -p /home/<USER>/projects
cd /home/<USER>/projects

# 解压部署包
tar -xzf ~/wenjuan-deploy.tar.gz

# 进入项目目录
cd wenjuan-deploy

# 查看项目结构
ls -la
```

### 5.4 构建Docker镜像
```bash
# 构建MySQL镜像
docker build -t wenjuan-mysql:latest -f docker-configs/mysql/Dockerfile .

# 构建后端镜像
docker build -t wenjuan-backend:latest -f docker-configs/backend/Dockerfile .

# 构建前端镜像
docker build -t wenjuan-frontend:latest -f docker-configs/frontend/Dockerfile .

# 查看构建的镜像
docker images | grep wenjuan
```

### 5.5 启动容器服务
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 查看容器状态
docker-compose ps

# 查看所有容器
docker ps -a
```

## 第六步：检查和验证部署

### 6.1 检查容器状态
```bash
# 查看所有容器状态
docker ps

# 查看特定容器状态
docker inspect wenjuan-mysql
docker inspect wenjuan-backend
docker inspect wenjuan-frontend

# 检查容器健康状态
docker-compose ps
```

### 6.2 查看容器日志
```bash
# 查看MySQL日志
docker logs wenjuan-mysql

# 查看后端应用日志
docker logs wenjuan-backend

# 查看前端日志
docker logs wenjuan-frontend

# 实时查看日志
docker logs -f wenjuan-backend
```

### 6.3 检查网络连接
```bash
# 查看Docker网络
docker network ls

# 检查容器网络连接
docker network inspect wenjuan-deploy_wenjuan-network

# 测试容器间连接
docker exec wenjuan-backend ping wenjuan-mysql
```

### 6.4 检查端口监听
```bash
# 检查端口占用情况
sudo netstat -tlnp | grep -E "(13306|18999|18888|18080)"

# 或使用ss命令
sudo ss -tlnp | grep -E "(13306|18999|18888|18080)"

# 测试端口连通性
curl -I http://localhost:18888
curl -I http://localhost:18999/actuator/health
```

### 6.5 数据库连接测试
```bash
# 进入MySQL容器
docker exec -it wenjuan-mysql mysql -u wenjuan -p

# 在MySQL中执行测试查询
USE wenjuan;
SHOW TABLES;
SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'wenjuan';
```

## 第七步：服务管理命令

### 7.1 启动和停止服务
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart wenjuan-backend

# 停止特定服务
docker-compose stop wenjuan-mysql
```

### 7.2 更新和维护
```bash
# 重新构建并启动服务
docker-compose up -d --build

# 查看资源使用情况
docker stats

# 清理未使用的镜像和容器
docker system prune -a
```

### 7.3 备份和恢复
```bash
# 备份MySQL数据
docker exec wenjuan-mysql mysqldump -u wenjuan -pwenjuan2024 wenjuan > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份Docker卷
docker run --rm -v wenjuan-deploy_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_data_backup.tar.gz -C /data .
```

## 第八步：防火墙配置
```bash
# 检查防火墙状态
sudo ufw status

# 开放必要端口
sudo ufw allow 18080/tcp
sudo ufw allow 18888/tcp
sudo ufw allow 18999/tcp
sudo ufw allow 13306/tcp

# 启用防火墙
sudo ufw enable
```

## 第九步：访问测试
```bash
# 测试前端访问
curl -I http://your-server-ip:18888

# 测试后端API
curl -I http://your-server-ip:18999/actuator/health

# 测试反向代理
curl -I http://your-server-ip:18080
```

## 故障排除

### 常见问题解决
1. **容器启动失败**：检查日志 `docker logs container-name`
2. **端口冲突**：修改docker-compose.yml中的端口映射
3. **数据库连接失败**：检查数据库容器状态和网络连接
4. **内存不足**：调整JVM参数或增加服务器内存

### 监控命令
```bash
# 实时监控容器资源使用
docker stats

# 查看系统资源
htop
free -h
df -h
```

这样您就完成了整个项目的Docker化部署！
