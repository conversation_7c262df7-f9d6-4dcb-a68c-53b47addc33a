// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;
$backgroundColor: rgba(247, 247, 247, 90);
// sidebar
$menuText: #bfcbd9;
$menuActiveText: #409eff;
$subMenuActiveText: #f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #304156;
$menuHover: #263445;

$subMenuBg: #1f2d3d;
$subMenuHover: #001528;

$sideBarWidth: 210px;

/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
$--color-primary: #1890ff;
$--color-success: #13ce66;
$--color-warning: #ffba00;
$--color-danger: #ff4949;
// $--color-info: #1E1E1E;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

$--table-border: 1px solid #dfe6ec;

$--box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
  menutext: $menuText;
  menuactivetext: $menuActiveText;
  submenuactivetext: $subMenuActiveText;
  menubg: $menuBg;
  menuhover: $menuHover;
  submenubg: $subMenuBg;
  submenuhover: $subMenuHover;
  sidebarwidth: $sideBarWidth;
}
