spring:
  application:
    name: tduck-api
  profiles:
    active: prod
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************************************************************
    username: wenjuan
    password: wenjuan2024
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: WenjuanHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: always
    serialization:
      indent_output: false
      fail_on_empty_beans: false
    deserialization:
      fail_on_unknown_properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

  # 缓存配置
  cache:
    ehcache:
      config: classpath:ehcache.xml

  # 邮件配置（生产环境需要配置真实邮箱）
  mail:
    host: smtp.qq.com
    port: 587
    username: # 配置真实邮箱
    password: # 配置邮箱授权码
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# 服务器配置
server:
  port: 8999
  compression:
    enabled: true
    mime-types: application/javascript,text/css,application/json,application/xml,text/html,text/xml,text/plain
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    connection-timeout: 20000
    max-connections: 8192

# 日志配置
logging:
  level:
    com.tduck.cloud: info
    org.springframework.web: warn
    org.springframework.security: warn
    org.mybatis: warn
    com.zaxxer.hikari: warn
    root: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: /app/logs/tduck-api.log
    max-size: 100MB
    max-history: 30

# MyBatis Plus配置
mybatis-plus:
  type-aliases-package: com.tduck.cloud.*.entity
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 平台配置
platform:
  request:
    trace-log: false
  jwt:
    secret: f6f31a5f2136758f86b67cde583cb125
    expire: 604800
    header: token

# Swagger配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    enabled: false  # 生产环境关闭
  api-docs:
    path: /v3/api-docs
    enabled: false  # 生产环境关闭

# 验证码配置
aj:
  captcha:
    enable: true
    water-mark: tduck
    cache-type: local
    type: default
    interference-options: 2

# 微信配置（如需要）
wx:
  mp:
    configs:
      - appId: # 微信公众号AppId
        secret: # 微信公众号Secret
        token: # 微信公众号Token
        aesKey: # 微信公众号AESKey

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  health:
    db:
      enabled: true
