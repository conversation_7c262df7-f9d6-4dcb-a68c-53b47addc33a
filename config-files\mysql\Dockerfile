# MySQL 8.0 Docker镜像
FROM mysql:8.0

# 设置维护者信息
LABEL maintainer="wenjuan-system"

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=wenjuan2024
ENV MYSQL_DATABASE=wenjuan
ENV MYSQL_USER=wenjuan
ENV MYSQL_PASSWORD=wenjuan2024
ENV TZ=Asia/Shanghai

# 复制自定义配置文件
COPY my.cnf /etc/mysql/conf.d/custom.cnf

# 复制初始化脚本
COPY init-scripts/ /docker-entrypoint-initdb.d/

# 设置权限
RUN chmod -R 755 /docker-entrypoint-initdb.d/

# 暴露端口
EXPOSE 3306

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD mysqladmin ping -h localhost -u root -p$MYSQL_ROOT_PASSWORD || exit 1
